services:
  # Add missing services for local development
  mariadb:
    image: mariadb:10.6
    environment:
      MYSQL_ROOT_PASSWORD: secret
      MYSQL_DATABASE: rmconnect
      MYSQL_USER: homestead
      MYSQL_PASSWORD: secret
    ports:
      - "3306:3306"
    volumes:
      - mariadb_data:/var/lib/mysql
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  rabbitmq:
    image: rabbitmq:3-management
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

  mailhog:
    image: mailhog/mailhog:latest
    ports:
      - "1025:1025"
      - "8025:8025"

  elasticsearch:
    image: elasticsearch:7.17.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

  # Override services to add port mappings for local access
  http:
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - php-fpm
      - mariadb
    volumes:
      - ./certs:/etc/certs:ro
      - .:/var/www

  php-fpm:
    depends_on:
      - mariadb
      - redis
      - rabbitmq
      - elasticsearch
    volumes:
      - .:/var/www
    networks:
      default:
        aliases:
          - rmc-php-fpm

  php-fpm-dev:
    volumes:
      - .:/var/www

  runner:
    depends_on:
      - mariadb
      - redis
      - rabbitmq

  interservice:
    depends_on:
      - rabbitmq

volumes:
  mariadb_data:
  rabbitmq_data:
  elasticsearch_data: