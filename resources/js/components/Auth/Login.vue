<template>
    <div>
        <h1>Log in to your account</h1>
        <p class="text-light">Enter your details below.</p>

        <form method="post" ref="loginForm" class="form" autocomplete="off">
            <input type="hidden" name="_token" :value="csrfToken">
            <input
                v-if="redirectAfterLogin"
                type="hidden"
                name="redirectAfterLogin"
                :value="redirectAfterLogin"
            />

            <div class="bg-error box text-white"
                 v-if="loginErrors && Object.keys(loginErrors).length > 0"
            >
                <div class="inner-small vertical-align-container-medium">
                    <div class="vertical-align-item badge badge-large badge-zoom badge-no-border button-hollow">
                        <i class="icon ion-alert-circled"></i>
                    </div>

                    <div class="vertical-align-item inner-medium-size-small padding-top-none padding-bottom-none">
                        <p class="text-large" v-if="loginErrors.email && loginErrors.email.length > 0">
                            {{ loginErrors.email[0] }}</p>

                        <p class="text-large" v-if="loginErrors.password && loginErrors.password.length > 0">
                            {{ loginErrors.password[0] }}</p>

                        <p class="text-large"
                           v-if="loginErrors.email && loginErrors.email.length > 0 && loginErrors.email[0] === ''">
                            We were unable to log you into your account. <a class="text-color7"
                                                                            :href="typeof route('forgot-password') === 'string' ? route('forgot-password') : route('forgot-password').url()"><strong>Click
                            here to
                            recover your email or password</strong></a> or call {{ customerServiceNumber }}
                            for assistance.</p>
                    </div>
                </div>
            </div>

            <div class="margin-bottom control-group">
                <label for="email">Email</label>
                <input tabindex="1" type="text" id="email" name="email" autocomplete="off" required
                       class="login-email--input select-on-focus margin-none"
                       v-model="form.email">
            </div>

            <div v-if="isPasswordVisible" class="margin-bottom control-group">
                <label for="email">Password</label>
                <div class="input-icon-container input-icon-container-right">
                    <a
                        @click="togglePassword"
                        class="icon icomoon input-icon-right"
                        :class="{ 'icon-show': passwordInput.show, 'icon-hide': !passwordInput.show }"
                        v-tooltip="{
                        content: passwordInput.show ? 'Show password' : 'Hide password',
                        trigger: 'click hover',
                        hideOnTargetClick: true
                    }"
                    ></a>
                    <input tabindex="2" :type="passwordInput.type" id="password" autocomplete="off" name="password"
                           required
                           class="margin-none"
                           v-model="form.password">
                </div>
            </div>

            <button tabindex="3" type="submit" @click.prevent="login(loginAdapter)" class="button button-color1"
                    v-for="(loginAdapter, index) in loginAdapters"
                    :key="index"
            >
                {{ loginAdapter.buttonText }}
            </button>

            <p><a :href="typeof route('forgot-password') === 'string' ? route('forgot-password') : route('forgot-password').url()">Forgot your password?</a></p>
        </form>
    </div>
</template>

<script>

import _debounce from "lodash/debounce";

export default {
    name: "Login",

    props: {
        csrfToken: {
            type: String,
            required: true,
        },
        loginErrors: {},
        customerServiceNumber: {
            type: String,
            default: '',
        },
        redirectAfterLogin: {
            type: Boolean,
            default: false,
        },
        authPolicies: {
            type: Object,
            default: () => ({}),
        }
    },

    data: () => ({
        isPasswordVisible: true,
        loginAdapters: [],
        form: {
            email: '',
            password: '',
        },
        passwordInput: {
            type: 'password',
            show: true,
        },
    }),

    mounted() {
        this.loginAdapters.push(this.authPolicies['password']);
        console.log('Forgot password route:', this.route('forgot-password'));
    },

    beforeCreate() {
        // prevent login from opening in an iframe on session timeout
        if (window.top !== window.self) {
            top.location.href = document.location.href;
        }
    },

    methods: {
        login(loginAdapter) {
            const routeResult = this.route(loginAdapter.route);
            console.log('Route result for', loginAdapter.route, ':', routeResult);
            this.$refs.loginForm.action = typeof routeResult === 'string' ? routeResult : routeResult.url();
            this.$refs.loginForm.submit();
        },

        clearSession() {
            sessionStorage.clear();
        },

        setAuthPolicyFromUsername: _debounce(function (username) {
            // If we empty the username input we have to init the loginAdapters to the default one
            if (!this.form.email) {
                this.loginAdapters = [this.authPolicies['password']];
                this.isPasswordVisible = this.loginAdapters[0].requiresPassword;

                return;
            }

            axios.get(rmc_route('api.auth-policy.show', {
                email: username
            })).then(response => {
                const authPoliciesByUser = response.data;

                this.loginAdapters = Object.values(this.authPolicies).filter((authPolicy) => {
                    return authPoliciesByUser.find((policyByUser) => {
                        return policyByUser === authPolicy.name;
                    });
                })

                this.isPasswordVisible = this.loginAdapters.find((adapter) => {
                    return adapter.requiresPassword;
                }) !== undefined;
            });
        }, 300),

        togglePassword() {
            if (this.passwordInput.type === 'password') {
                this.passwordInput.type = 'text';
                this.passwordInput.show = false;
            } else {
                this.passwordInput.type = 'password';
                this.passwordInput.show = true;
            }
        },
    },

    watch: {
        'form.email': {
            handler: function (value) {
                this.setAuthPolicyFromUsername(value);
            },
            deep: true,
        },
    }
}
</script>
