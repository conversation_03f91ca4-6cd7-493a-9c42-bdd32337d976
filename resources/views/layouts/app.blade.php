<!doctype html>
<html class="no-js" lang="en" dir="ltr">

<head>
    @env('production')
        <!-- Global site tag (gtag.js) - Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=UA-5125079-1"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }

            gtag('js', new Date());

            gtag('config', 'UA-5125079-1');
        </script>
        <!-- / Global site tag (gtag.js) - Google Analytics -->

        @auth
            <!-- Event snippet for Users With Account remarketing page -->
            <script>
                gtag('event', 'conversion', {
                    'send_to': 'AW-**********/KMJQCKb0rcoBEJmwu-wD',
                    'value': 1.0,
                    'currency': 'USD',
                    'aw_remarketing_only': true
                });
            </script>
            <!-- / Event snippet for Users With Account remarketing page -->
        @endauth

        <!-- <PERSON>o Munchkin Tracking Code -->
        <script type="text/javascript">
            (function () {
                var didInit = false;

                function initMunchkin() {
                    if (didInit === false) {
                        didInit = true;
                        Munchkin.init('349-FQY-146');
                    }
                }

                var s = document.createElement('script');
                s.type = 'text/javascript';
                s.async = true;
                s.src = '//munchkin.marketo.net/munchkin.js';
                s.onload = function () {
                    initMunchkin();
                };
                document.getElementsByTagName('head')[0].appendChild(s);
            })();
        </script>
        <!-- / Marketo Munchkin Tracking Code -->
    @endenv

    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta property="og:image" content="{{ asset('images/remindermedia/logo/logo-with-type.png') }}">

    @hasSection('title')
        <title>@yield('title') | ReminderMedia</title>
    @else
        <title>ReminderMedia</title>
    @endif

    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="stylesheet" type="text/css" href="{{ mix('css/app.css') }}">
    @yield('stylesheets')

    <script>
        // Manual route generation for Ziggy
        window.route = function(name, params, absolute, customZiggy) {
            // Basic route definitions for login/signup functionality
            const routes = {
                'login': '/login',
                'register': '/createaccount',
                'home': '/',
                'dashboard': '/dashboard'
            };

            if (routes[name]) {
                return { url: () => routes[name] };
            }

            // Fallback for unknown routes
            return { url: () => '/' };
        };

        // Add current route functionality
        window.route.current = function(name) {
            const currentPath = window.location.pathname;
            if (name) {
                // Check if current route matches the given name
                const routes = {
                    'login': '/login',
                    'register': '/createaccount',
                    'home': '/',
                    'dashboard': '/dashboard'
                };
                return currentPath === routes[name];
            }
            // Return current route name based on path
            if (currentPath === '/login') return 'login';
            if (currentPath === '/createaccount') return 'register';
            if (currentPath === '/dashboard') return 'dashboard';
            return 'home';
        };
    </script>
    @auth
        <script>
            // todo remove - replace references with context
            window.accountSlug = "{!! $__accountSlug !!}";
            window.accountId = {!! $__accountId ?: 'null'; !!};

            window.context = {!! json_encode($__context) ?? '{}' !!};
        </script>
    @endauth
    @guest
        <script>
            const accountId = null;
        </script>
    @endguest

    <!-- Facebook Pixel Code -->
    @env('production')
        <script>
            !function (f, b, e, v, n, t, s) {
                if (f.fbq) return;
                n = f.fbq = function () {
                    n.callMethod ?
                        n.callMethod.apply(n, arguments) : n.queue.push(arguments)
                };
                if (!f._fbq) f._fbq = n;
                n.push = n;
                n.loaded = !0;
                n.version = '2.0';
                n.queue = [];
                t = b.createElement(e);
                t.async = !0;
                t.src = v;
                s = b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t, s)
            }(window,
                document, 'script', 'https://connect.facebook.net/en_US/fbevents.js');

            fbq('init', '***************');
            fbq('track', 'PageView');
        </script>
        <noscript>
            <img height="1" width="1" style="display:none"
                 src="https://www.facebook.com/tr?id=***************&ev=PageView&noscript=1"/>
        </noscript>
        <!-- / Facebook Pixel Code -->

        <!-- Proof Pixel -->
        <script src="https://cdn.useproof.com/proof.js?acc=obz6BKSEytS96QUXKxsIAT1xOZJ2" async></script>
        <!-- / Proof Pixel -->
    @endenv

    @include('layouts.partials.appcues')
</head>

<body class="@yield('body_class') has-top-bar">

@include('layouts.partials.facebook-sdk')
@include('layouts.partials.upscope')

@isset($page)
    {{-- $page should only be passed by Inertia when handling pages --}}
    @inertia
@else
    {{-- This page is not handled by Inertia, we need to make use of blade templates --}}
    <div id="root">
        <rm-full-page-loader></rm-full-page-loader>
        @yield('body')
    </div>
@endisset

<script src="{{ mix('js/manifest.js') }}"></script>
<script src="{{ mix('js/vendor.js') }}"></script>
<script src="{{ mix('js/app.js') }}"></script>
{{-- SHOW_SUPPORT_CHAT is a function, not a variable. it returns boolean.  See App\Providers\AppServiceProvider --}}
@if( $SHOW_SUPPORT_CHAT() )
    <!-- Start of remindermediahelp Zendesk Widget script -->
    <script id="ze-snippet" src="https://static.zdassets.com/ekr/snippet.js?key=395d5aa9-b2bf-4b38-95e9-11975d608c12"></script>
    <!-- End of remindermediahelp Zendesk Widget script -->
@endif
@yield('javascript')

@yield('bottom')
</body>
</html>
