[mysqld]
datadir=/var/lib/mysql
socket=/var/lib/mysql/mysql.sock
user=mysql
symbolic-links=0
max_allowed_packet=2G
general_log=0
delay_key_write=all
general-log
general-log-file=/var/log/mysql/queries.log
log-output=file
log_error=/var/log/mysql/mysql_error.log
log_warnings=2
innodb_flush_log_at_trx_commit = 2
long-query-time=2
table_cache=2048
max_connections=200
key_buffer_size=512M
tmpdir = /dev/shm
innodb_file_per_table=1
max_heap_table_size=128M
tmp_table_size=128M
innodb_buffer_pool_size=1G
innodb_flush_method=O_DIRECT
innodb_log_file_size=256M
innodb_log_buffer_size=256M
sort_buffer=4M
read_buffer=1M
innodb_autoinc_lock_mode=1
innodb_file_format=BARRACUDA
#sql_mode=NO_ONLY_FULL_GROUP_BY,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION
sql_mode=ERROR_FOR_DIVISION_BY_ZERO
#character-set-server=utf8
#collation-server=utf8_general_ci

# Added in to allow for print pull to work
connect_timeout=10
innodb_open_files=2048 # We have a big db
thread_cache_size=200
wait_timeout=28800
tmpdir = /tmp # This won't run out of space

[mysqld_safe]
#log-error=/var/lib/mysql/mysqld.log
#pid-file=/var/run/mysqld/mysqld.pid
