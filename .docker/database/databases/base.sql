-- ----------------------------------------------------------------------------
-- INITIAL CLEANUP/SETUP
--
-- We are dropping all users except one system user so that we don't have any
-- carry over from production permissions.  We will create the bare minimum of
-- users aftewards.
--
-- This script runs every time the DB container is restarted, not just on
-- rebuild.  In this approach, we are able to handle people copying prod data
-- down w/o having to rebuild the container.
-- ----------------------------------------------------------------------------

-- Delete All users not mariadb.sys@*
-- Stuff completely breaks when you delete this user.
DROP USER IF EXISTS 'root'@'%';
DROP USER IF EXISTS 'programmer'@'%';
FLUSH PRIVILEGES;

CREATE USER IF NOT EXISTS 'root'@'%';
GRANT ALL ON *.* TO 'root'@'%' WITH GRANT OPTION;

CREATE USER IF NOT EXISTS 'programmer'@'%';
GRANT ALL ON *.* TO 'programmer'@'%' WITH GRANT OPTION;

CREATE USER IF NOT EXISTS 'xrms'@'%';
GRANT ALL ON *.* TO 'xrms'@'%' WITH GRANT OPTION;

CREATE USER IF NOT EXISTS 'accounting'@'%';
GRANT ALL ON *.* TO 'accounting'@'%' WITH GRANT OPTION;

CREATE USER IF NOT EXISTS 'query_only'@'%';
GRANT ALL ON *.* TO 'query_only'@'%' WITH GRANT OPTION;

CREATE USER IF NOT EXISTS 'presenter_demo'@'%';
GRANT ALL ON *.* TO 'presenter_demo'@'%' WITH GRANT OPTION;

-- ----------------------------------------------------------------------------
-- XRMS TASKS
-- ----------------------------------------------------------------------------

-- Create XRMS DB and changelog table.
CREATE DATABASE IF NOT EXISTS `xrms`;
CREATE TABLE IF NOT EXISTS `xrms`.`changelog` (
    `change_number` bigint(20) NOT NULL,
    `complete_dt` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    `applied_by` varchar(100) NOT NULL,
    `description` varchar(500) NOT NULL,
    PRIMARY KEY (`change_number`)
    ) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- needed because of a landing pages seeder yuck
CREATE TABLE IF NOT EXISTS `xrms`.`customer_contact_contracts` (
    contact_contract_id int unsigned auto_increment
        primary key,
    contact_id          int unsigned                                not null comment 'The Contact who is bound to the contract',
    contract_id         int unsigned                                not null comment 'The Contract that the Contact is bound too',
    signed_at           timestamp                                   null comment 'The date the contract is signed',
    ip_address          varchar(15)                                 null,
    file_id             int(10)                                     null comment 'FKey xrms.file, when a file exists for this contract',
    signature_optional  tinyint(1)      default 0                   not null comment 'Grandfather in contacts who dont have a contract and dont have to sign one',
    is_signed_digitally tinyint(1)      default 0                   not null comment 'Is the contract signed digitally',
    row_status          enum ('a', 'd') default 'a'                 not null comment 'The status of the row a - active, d - deleted',
    is_completed        tinyint(1)      default 0                   not null comment 'Is the Contract Completed / fulfilled ',
    reason_for_delete   varchar(255)                                null,
    entered_at          timestamp       default current_timestamp() not null comment 'The date the record is created'
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


-- Check if the index exists
IF NOT EXISTS (SELECT 1 FROM information_schema.statistics
               WHERE table_schema = 'xrms'
                 AND table_name = 'customer_contact_contracts'
                 AND index_name = 'IX_ccc') THEN
-- Create the index
CREATE INDEX IX_ccc ON xrms.customer_contact_contracts (contact_id, contract_id);
END IF;

--needed on contact groups page -- double yuck
create table xrms.additionalcopies
(
    id                 int(20) unsigned auto_increment
        primary key,
    copies             int(20) unsigned                default 0                     null,
    addressid          int(20) unsigned                default 0                     null,
    dearname           varchar(100)                    default ''                    null,
    groupid            int(20)                                                       null,
    agentid            int(20)                                                       null,
    alt_address1       varchar(100)                    default ''                    null,
    alt_address2       varchar(100)                    default ''                    null,
    alt_citystatezip   varchar(100)                    default ''                    null,
    run                enum ('Once', 'Every', 'Never') default 'Every'               null,
    run_updated        timestamp                       default '0000-00-00 00:00:00' not null,
    alt_city           varchar(100)                                                  null,
    alt_state          varchar(100)                                                  null,
    alt_zip            varchar(10)                                                   null,
    recipient_group_id int unsigned                                                  null,
    record_version     int                             default 0                     null,
    constraint unique_idx
        unique (agentid)
);

-- Check if the index exists
IF NOT EXISTS (SELECT 1 FROM information_schema.statistics
               WHERE table_schema = 'xrms'
                 AND table_name = 'additionalcopies'
                 AND index_name = 'agentid') THEN
    -- Create the index
CREATE INDEX agentid ON xrms.additionalcopies (agentid);
END IF;

-- Create commission dbs
CREATE DATABASE IF NOT EXISTS `commission`;
CREATE DATABASE IF NOT EXISTS `commission_test`;

-- Grant non-xrms users permissions to their databases
GRANT ALL ON accounting.* TO 'accounting'@'%';
GRANT ALL ON xrms.* TO 'accounting'@'%';
GRANT SELECT ON *.* TO 'query_only'@'%';
GRANT ALL ON presenter_demo.* TO 'presenter_demo'@'%';

-- ----------------------------------------------------------------------------
-- NEXTGEN/NON-XRMS
-- ----------------------------------------------------------------------------

-- Create NextGen/Non-xrms dbs databases in case we're starting bare
CREATE DATABASE IF NOT EXISTS `book`;
CREATE DATABASE IF NOT EXISTS `book-cms`;
CREATE DATABASE IF NOT EXISTS `contact`;
CREATE DATABASE IF NOT EXISTS `mailing`;
CREATE DATABASE IF NOT EXISTS `rmconnect`;

-- Create test dbs
CREATE DATABASE IF NOT EXISTS `book_test`;
CREATE DATABASE IF NOT EXISTS `contact_test`;
CREATE DATABASE IF NOT EXISTS `mailing_test`;
CREATE DATABASE IF NOT EXISTS `rmconnect_test`;

-- Create RMC Users
CREATE USER 'homestead'@'%' IDENTIFIED BY 'secret';

-- Create gearmand user for CRM
CREATE USER IF NOT EXISTS gearmand@'%' IDENTIFIED BY 'gearmand';
GRANT ALL PRIVILEGES ON gearmand.* TO 'gearmand'@'%';
CREATE DATABASE IF NOT EXISTS `gearmand`;

GRANT ALL ON *.* TO 'homestead'@'%';

-- FLUSH
FLUSH PRIVILEGES;
