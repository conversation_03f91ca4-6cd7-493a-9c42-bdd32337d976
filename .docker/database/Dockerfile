FROM mariadb:10.6 AS build

LABEL maintainer="<PERSON> <<EMAIL>>"

# This sets the timezone in the db container to local time
# Without this, database is running in UTC, and tests fail because the webserver is running E[D/S]T
RUN echo "America/New_York" > /etc/timezone \
  && dpkg-reconfigure -f noninteractive tzdata


FROM docker.remindermedia.net/remindermedia/nextgen/rmc/database:latest AS dev

COPY databases/* /docker-entrypoint-initdb.d
