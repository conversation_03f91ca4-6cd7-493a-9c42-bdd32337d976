# Use the specified version of Node.js
FROM node:16.14.0 AS build

#RUN corepack enable \
#    && corepack prepare pnpm@latest --activate

USER node


FROM docker.remindermedia.net/remindermedia/nextgen/rmc/node:develop AS dev

ENV NODE_ENV=development

# Set the working directory inside the container
WORKDIR /var/www

COPY --chown=node .docker/node/entrypoint.sh /usr/lib/

RUN chmod +x /usr/lib/entrypoint.sh
