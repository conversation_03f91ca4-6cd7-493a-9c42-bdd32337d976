ARG TYPE

FROM php:7.1.33-buster AS cli
FROM php:7.1.33-fpm-buster AS fpm


# hadolint ignore=DL3006
FROM ${TYPE} AS build

LABEL maintainer="<PERSON> <<EMAIL>>"

RUN apt-get -y update  \
  && apt-get install -y  \
    cron \
    default-mysql-client \
    git \
    libc-client-dev \
    libkrb5-dev \
    libmemcached-dev \
    libxml2-dev \
    libpng-dev \
    zlib1g-dev \
    libjpeg-dev \
    libfreetype6-dev \
    librabbitmq4 \
    librabbitmq-dev \
    supervisor \
    unzip \
  && rm -rf /var/lib/apt/lists/* #reduce image size

COPY --from=mlocati/php-extension-installer:2.1.51 /usr/bin/install-php-extensions /usr/local/bin/

#####################################
# Laravel PHP Dependencies
#####################################
RUN install-php-extensions  \
    amqp \
    bcmath \
    exif \
    gd \
    imap \
    imagick \
    mbstring \
    memcached \
    mysqli \
    opcache \
    pdo \
    pdo_mysql \
    pcntl \
    redis-5.3.7 \
    soap \
    sockets \
    tokenizer \
    xml \
    zip

WORKDIR /var/www
