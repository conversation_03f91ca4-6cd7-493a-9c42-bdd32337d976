ARG SERVICE_TYPE
FROM docker.remindermedia.net/remindermedia/nextgen/rmc/php:${SERVICE_TYPE}-develop AS dev

LABEL maintainer="<PERSON> <<EMAIL>>"

RUN apt-get update \
  && apt-get install -y \
    procps \
    vim \
    net-tools \
    iputils-ping \
  && rm -rf /var/lib/apt/lists/* \
  && useradd -s /bin/bash -m -u 1000 programmer

COPY --chown=programmer:programmer --chmod=444 .docker/php/conf.d/docker-php-ext-memory.ini /usr/local/etc/php/conf.d/10-memory.ini
COPY --chown=programmer:programmer --chmod=444 .docker/php/conf.d/docker-php-ext-opcache.dev.ini /usr/local/etc/php/conf.d/20-opcache.ini
COPY --chown=programmer:programmer --chmod=444 .docker/php/conf.d/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/xdebug.ini
COPY --chown=programmer:programmer --chmod=444 .docker/php/php-fpm.d/ /usr/local/etc/php-fpm.d/

RUN install-php-extensions xdebug @composer-1 \
  && touch /var/log/xdebug.log \
  && chmod 777 /var/log/xdebug.log \
  && echo "UTC" > /etc/timezone  \
  && ln -sf "/usr/share/zoneinfo/UTC" /etc/localtime  \
  && dpkg-reconfigure -f noninteractive tzdata

USER programmer


FROM docker.remindermedia.net/remindermedia/nextgen/rmc/php:${SERVICE_TYPE}-develop AS version

COPY --chown=33:33 . /var/www
COPY --chown=www-data:www-data --chmod=444 .docker/php/conf.d/docker-php-ext-memory.ini /usr/local/etc/php/conf.d/10-memory.ini
COPY --chown=www-data:www-data --chmod=444 .docker/php/conf.d/docker-php-ext-opcache.prod.ini /usr/local/etc/php/conf.d/20-opcache.ini
COPY --chown=www-data:www-data --chmod=444 .docker/php/php-fpm.d/ /usr/local/etc/php-fpm.d/

USER www-data
