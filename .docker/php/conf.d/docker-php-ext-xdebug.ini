; NOTE: The actual debug.so extention is NOT SET HERE but rather (/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini)

xdebug.remote_autostart = 1
xdebug.remote_enable = 1
xdebug.remote_connect_back = 0
xdebug.cli_color = 0
xdebug.remote_handler = dbgp
xdebug.remote_mode = req
xdebug.remote_log = /var/log/xdebug.log

xdebug.remote_port = 9000
xdebug.remote_host = dockerhost
xdebug.idekey = PHPSTORM

xdebug.profiler_enable = 0
xdebug.profiler_output_dir = /tmp
