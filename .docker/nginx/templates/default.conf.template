server {
    listen 80;
    server_name ${DEFAULT_SITE_HOSTNAME} *.${DEFAULT_SITE_HOSTNAME};
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name ${DEFAULT_SITE_HOSTNAME} *.${DEFAULT_SITE_HOSTNAME};

    index index.php;

    charset utf-8;

    # Direct upload headshots to a diff spot.
    # See xrms: web/api/reboot/v1/media-manager/getPhoto.php
    # todo this should probably be removed or refactored
    location ~* "^/upload/([0-9]{4}/[0-9]{2}/[0-9]{2}/[0-9]{2,}_[0-9]{2,}_.*.(png|jpg|jpeg|pjpeg|gif))$" {
        alias /var/www/internal_images/www/uploaded_files/$1;
    }

    # Fetch images from production account manager if not found locally.
    location ~* ^/storage/.+\.(svg|svgz|jpg|jpeg|gif|png|ico|bmp|tiff|eps)$ {
        try_files $uri @image_fallback;
    }

    # Allow cross origin for fonts
    location ~* \.(eot|ttf|woff|woff2)$ {
        add_header Access-Control-Allow-Origin *;
    }

    # Fetch images from production account manager if not found locally.
    location ~* ^/storage/.+\.(svg|svgz|jpg|jpeg|gif|png|ico|bmp|tiff|eps)$ {
        try_files $uri @image_fallback;
    }

    location @image_fallback {
        proxy_pass https://${DEFAULT_SITE_HOSTNAME};
    }

    location ~ .+\.js$ {
        add_header Cache-Control "no-cache";
    }

    location = /favicon.ico { access_log off; log_not_found off; alias /var/www/public/favicon.ico; }
    location = /robots.txt  { access_log off; log_not_found off; }

    access_log /var/log/nginx/access.log;
    error_log  /var/log/nginx/error.log error;

    sendfile off;

    client_max_body_size 100m;

    location ~ /\.ht {
        deny all;
    }

    #ssl_protocols TLSv1.2 TLSv1.3;
    # Modern Compatibility
    # https://wiki.mozilla.org/Security/Server_Side_TLS
    ssl_certificate      ${SSL_CERTIFICATE};
    ssl_certificate_key  ${SSL_CERTIFICATE_KEY};

    include includes/server_backend;
}
