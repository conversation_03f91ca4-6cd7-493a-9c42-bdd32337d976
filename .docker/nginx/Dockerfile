FROM nginx:1.25.2 AS build

RUN apt-get update \
    && apt-get -y install inotify-tools \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

COPY templates /etc/nginx/templates
COPY includes /etc/nginx/includes

# setup for gelf logging from syslog
RUN rm /var/log/nginx/access.log \
    && rm /var/log/nginx/error.log \
    && ln -svf /dev/stdout /var/log/nginx/access.log  \
    && ln -svf /dev/stderr /var/log/nginx/error.log


FROM docker.remindermedia.net/remindermedia/nextgen/rmc/nginx:develop AS version

COPY --chown=nginx:nginx --chmod=555 ./.docker/nginx/nginx.prod.conf /etc/nginx/nginx.conf
COPY --chown=nginx:nginx --chmod=555 ./.docker/nginx/scripts /srv/scripts/
COPY --chown=nginx:nginx --chmod=555 ./.docker/nginx/entrypoint.sh /

COPY public /var/www/public

ENTRYPOINT [ "/entrypoint.sh" ]


FROM docker.remindermedia.net/remindermedia/nextgen/rmc/nginx:develop AS dev

ENV DEFAULT_SITE_HOSTNAME=localhost

# Install tools
RUN apt-get update \
    && apt-get -y install procps iproute2 dnsutils iputils-ping \
    && useradd -u 1000 programmer

COPY --chown=programmer:programmer --chmod=555 ./.docker/nginx/nginx.dev.conf /etc/nginx/nginx.conf
COPY --chown=programmer:programmer --chmod=555 ./.docker/nginx/scripts /srv/scripts/
COPY --chown=programmer:programmer --chmod=555 ./.docker/nginx/entrypoint.sh /

RUN ln -s /var/www/storage/app/public /var/www/public/storage

RUN sed -i "s/user  nginx;/user  programmer;/g" /etc/nginx/nginx.conf \
  && sed -i "s/ php-fpm:/ rmc-php-fpm:/g" /etc/nginx/includes/server_backend

ENTRYPOINT [ "/entrypoint.sh" ]
