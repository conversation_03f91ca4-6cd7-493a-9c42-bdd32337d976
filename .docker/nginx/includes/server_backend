resolver 127.0.0.11 valid=10s;
root   /var/www/public;

location / {
    set $target php-fpm:9000;
	try_files $uri $uri/ /index.php?$query_string;
}

location ~ \.php$ {
	include fastcgi_params;
    fastcgi_split_path_info ^(.+?\.php)(/.*)$;
	fastcgi_pass $target;
	fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;

	fastcgi_intercept_errors off;
	fastcgi_buffer_size 16k;
	fastcgi_buffers 4 16k;
	fastcgi_connect_timeout 300;
	fastcgi_send_timeout 300;
	fastcgi_read_timeout 300;
}
