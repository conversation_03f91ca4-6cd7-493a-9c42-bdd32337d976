user programmer;

worker_processes auto;

error_log  /var/log/nginx/error.log notice;
pid /run/nginx.pid;

events {
  worker_connections  2048;
  multi_accept on;
  use epoll;
}

http {
  include       /etc/nginx/mime.types;
  default_type  application/octet-stream;

  log_format  main  '[$time_local] $remote_addr - "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '$document_root$fastcgi_script_name > $request';

  access_log  /var/log/nginx/access.log  main;

  sendfile on;
  #tcp_nopush on;
  #tcp_nodelay on;

  keepalive_timeout 65;

  # Text Compression
  gzip  on;
  gzip_disable "MSIE [1-6]\.";
  gzip_vary on;
  gzip_proxied any;
  gzip_comp_level 6;
  gzip_buffers 16 8k;
  gzip_http_version 1.1;
  gzip_types text/plain text/css application/javascript application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript;

  # SSL Settings
  ssl_protocols TLSv1 TLSv1.1 TLSv1.2;

  ## Various other configurations
  include /etc/nginx/conf.d/*.conf;
  include /etc/nginx/sites-available/*.conf;
  server_names_hash_bucket_size 128;
  client_max_body_size 20M;

  # Configurations that don't exist in prod, commented out.
  #types_hash_max_size 2048;
  #open_file_cache off; # Disabled for issue 619
  #charset UTF-8;
}
