user  nginx;

worker_processes  4;
worker_rlimit_nofile 4096;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '[$time_local] $remote_addr - "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for" '
                      '$document_root$fastcgi_script_name > $request';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    # Text Compression
    gzip  on;
    gzip_disable "MSIE [1-6]\.";
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/javascript application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript;

    # SSL Settings
    ssl_protocols TLSv1.1 TLSv1.2;

    ## virtual sites config
    include /etc/nginx/conf.d/*;
    server_names_hash_bucket_size 64;
    client_max_body_size 200M;
}
