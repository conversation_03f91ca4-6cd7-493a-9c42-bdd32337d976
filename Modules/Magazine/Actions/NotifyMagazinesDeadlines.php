<?php

namespace Modules\Magazine\Actions;

use App\Contracts\AccountRepository;
use App\Events\MagazineDeadlineReminder as MagazineDeadlineReminderEvent;
use App\Models\Account;
use App\Models\User;
use Carbon\Carbon;
use Domain\Admin\Notifications\Contracts\NotificationHistoryRepository;
use Domain\Magazine\Entities\MagazineDeadlineSummary;
use Infrastructure\Traits\SmsNotificationHistory;
use Modules\Magazine\Notifications\MagazineDeadlineReminder;

class NotifyMagazinesDeadlines
{
    use SmsNotificationHistory;

    /** @var Account|null */
    private $account;

    /** @var NotificationHistoryRepository */
    private $notificationHistoryRepository;

    /** @var MagazineDeadlineReminder */
    private $notification;

    /** @var User|null */
    private $user;

    public function __construct(
        AccountRepository $accountRepository,
        NotificationHistoryRepository $notificationHistoryRepository
    ) {
        $this->account = $accountRepository->currentAccount();
        $this->notificationHistoryRepository = $notificationHistoryRepository;
    }

    /**
     * @throws \ReflectionException
     */
    public function execute(string $deadLine, string $recipientsRedirect)
    {
        $date = Carbon::createFromFormat('Y-m-d', $deadLine);
        $deadLineFormatted = $date->format('l, F jS Y');
        $this->notification = new MagazineDeadlineReminder($deadLineFormatted, $recipientsRedirect);

        $this->user = $this->account->getOwner();

        if (!$this->user) {
            return;
        }

        $this->user->notify($this->notification);

        $data = [
            'sentDate'          => $date,
            'typesNotification' => $this->notification->via($this->user),
            'emailAddresses'    => $this->user->routeNotificationForMail($this->notification),
            'phoneNumbers'      => $this->user->routeNotificationForTwilio($this->notification),
        ];

        event(MagazineDeadlineReminderEvent::fromMagazineDeadlineDTO(new MagazineDeadlineSummary($data)));

        $this->saveSmsNotificationHistory(class_basename($this));
    }
}
