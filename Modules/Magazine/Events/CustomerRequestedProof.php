<?php

namespace Modules\Magazine\Events;

use Carbon\Carbon;
use Illuminate\Support\Arr;
use Infrastructure\Events\AccountAwareEvent;
use Modules\Magazine\DTO\EmailProofDTO;
use ReminderMedia\Messaging\ShouldBroadcast;

class CustomerRequestedProof extends AccountAwareEvent implements ShouldBroadcast
{
    /** @var string */
    protected $globalName = 'magazine.customer-requested-proof';

    /** @var int */
    private $issueId;

    /** @var int */
    private $productId;

    /** @var string */
    private $requestedDate;

    public static function fromEmailProofDTO(EmailProofDTO $dto): self
    {
        $event = new self;

        $event->setData($dto->toArray());

        return $event;
    }

    protected function setData(array $data): void
    {
        $this->issueId = Arr::get($data, 'issue_id');
        $this->productId = Arr::get($data, 'product_id');
        $this->requestedDate = Arr::get($data, 'requested_date');
    }

    protected function getData(): array
    {
        return [
            'issueId'       => $this->getIssueId(),
            'productId'     => $this->getProductId(),
            'requestedDate' => $this->getRequestedDate(),
        ];
    }

    /**
     * @return int
     */
    public function getIssueId(): int
    {
        return $this->issueId;
    }

    /**
     * @return int
     */
    public function getProductId(): int
    {
        return $this->productId;
    }

    /**
     * @return string
     */
    public function getRequestedDate(): string
    {
        return $this->requestedDate;
    }

}
