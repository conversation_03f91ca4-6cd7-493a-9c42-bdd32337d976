<?php

namespace Modules\Magazine\DTO;

use Illuminate\Support\Arr;
use Spatie\DataTransferObject\DataTransferObject;

class RecipientLetterDTO extends DataTransferObject
{
    /** @var string */
    public $recipientLetter;

    /** @var string|null */
    public $recipientGroupName;

    /** @var int|null */
    public $recipientGroupId;

    public static function fromArray(array $data) : self
    {
        return new self([
            'recipientLetter'    => Arr::get($data, 'recipientLetter'),
            'recipientGroupName' => Arr::get($data, 'recipientGroupName'),
            'recipientGroupId'   => Arr::get($data, 'recipientGroupId'),
        ]);
    }
}

