<?php

namespace Modules\Magazine\DTO;

use Illuminate\Support\Arr;
use Spatie\DataTransferObject\DataTransferObject;

class FrontCoverSubstitutionsDto extends DataTransferObject
{
    //NOTE: DAM substitutions start with upper case, so the properties here need to reflect that.

    /** @var string */
    public $Address1 = '';

    /** @var string */
    public $Address2 = '';

    /** @var string */
    public $Address3 = '';

    /** @var string */
    public $ComplimentsOf = '';

    /** @var string */
    public $ContactInfo1 = '';

    /** @var string */
    public $ContactInfo1Ext = '';

    /** @var string */
    public $ContactInfo1Label = '';

    /** @var int */
    public $CoverDesign = '';

    /** @var string */
    public $Designations = '';

    /** @var int */
    public $FcLogo = '';

    /** @var int */
    public $FcPhoto = '';

    /** @var int */
    public $FcTemplate = '';

    /** @var string */
    public $IssueLine = '';

    /** @var string */
    public $LicenseNumber = '';

    /** @var string */
    public $Name = '';

    /** @var string */
    public $OfficeName = '';

    public static function fromArray(array $data) : self
    {
        return new self([
            'Address1' => Arr::get($data, 'address1', '', ''),
            'Address2' => Arr::get($data, 'address2', ''),
            'Address3' => Arr::get($data, 'address3', ''),
            'ComplimentsOf' => Arr::get($data, 'complimentsOf', ''),
            'ContactInfo1' => Arr::get($data, 'contactInfo1', ''),
            'ContactInfo1Ext' => Arr::get($data, 'contactInfo1Ext', ''),
            'ContactInfo1Label' => Arr::get($data, 'contactInfo1Label', ''),
            'CoverDesign' => (int) Arr::get($data, 'coverDesign', ''),
            'Designations' => Arr::get($data, 'designations', ''),
            'FcLogo' => (int) Arr::get($data, 'fcLogo', ''),
            'FcPhoto' => (int) Arr::get($data, 'fcPhoto', ''),
            'FcTemplate' => (int) Arr::get($data, 'fcTemplate', ''),
            'IssueLine' => Arr::get($data, 'issueLine', ''),
            'LicenseNumber' => Arr::get($data, 'licenseNumber', ''),
            'Name' => Arr::get($data, 'name', ''),
            'OfficeName' => Arr::get($data, 'officeName', ''),
        ]);
    }
}

