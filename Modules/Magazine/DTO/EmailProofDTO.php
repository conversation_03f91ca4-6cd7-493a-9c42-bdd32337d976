<?php

namespace Modules\Magazine\DTO;

use Illuminate\Support\Arr;
use Spatie\DataTransferObject\DataTransferObject;

class EmailProofDTO extends DataTransferObject
{
    /** @var int */
    public $issue_id;

    /** @var int|null */
    public $product_id;

    /** @var string */
    public $requested_date;

    public static function fromArray(array $data): self
    {
        return new self([
            'issue_id'          => Arr::get($data, 'issue_id'),
            'product_id'        => Arr::get($data, 'product_id'),
            'requested_date'    => Arr::get($data, 'requested_date'),
        ]);
    }
}
