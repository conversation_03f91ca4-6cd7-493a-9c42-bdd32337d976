<?php

namespace Modules\Magazine\Factories;

use Modules\Magazine\Entities\BackCoverMetadata;

class BackCoverMetadataFactory
{
    const PAGES_WITH_MAILING_LABEL = ['bc'];

    public function createFromName(string $name) : BackCoverMetadata
    {
        $metadata = new BackCoverMetadata($name);

        $images = $this->createImages($metadata);

        $metadata->setImages($images);

        return $metadata;
    }

    private function createImages(BackCoverMetadata $metadata) : array
    {
        $pageName = $metadata->getAbbrLowercase();

        $hasMailingLabel = $this->hasMailingLabel($pageName);

        return [
            'mailing_label' => [
                'small' => $hasMailingLabel
                    ? asset("images/standard/{$pageName}-mailing-label-sm.png")
                    : null,
                'large' => $hasMailingLabel
                    ? asset("images/standard/{$pageName}-mailing-label-lg.png")
                    : null,
            ],
            'not_available' => [
                'small' => asset("images/standard/placeholder_{$metadata->getAbbr()}.jpg"),
            ],
        ];
    }

    private function hasMailingLabel(string $pageName) : bool
    {
        return in_array($pageName, static::PAGES_WITH_MAILING_LABEL);
    }
}