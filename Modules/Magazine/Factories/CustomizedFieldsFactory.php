<?php

namespace Modules\Magazine\Factories;

use Modules\Magazine\Entities\CustomizedFields;

class CustomizedFieldsFactory
{
    public static function createFromFields(array $fields) : CustomizedFields
    {
        $customizedFields = new CustomizedFields;

        $customizedFields->setTitle1(array_get($fields, 'Title1'));
        $customizedFields->setTitle2(array_get($fields, 'Title2'));
        $customizedFields->setTitle3(array_get($fields, 'Title3'));
        $customizedFields->setTitle4(array_get($fields, 'Title4'));
        $customizedFields->setTitle5(array_get($fields, 'Title5'));
        $customizedFields->setTitle6(array_get($fields, 'Title6'));
        $customizedFields->setTitle7(array_get($fields, 'Title7'));
        $customizedFields->setContactInfo1(array_get($fields, 'ContactInfo1'));
        $customizedFields->setContactInfo2(array_get($fields, 'ContactInfo2'));
        $customizedFields->setContactInfo3(array_get($fields, 'ContactInfo3'));
        $customizedFields->setContactInfo4(array_get($fields, 'ContactInfo4'));
        $customizedFields->setContactInfo5(array_get($fields, 'ContactInfo5'));
        $customizedFields->setContactInfo6(array_get($fields, 'ContactInfo6'));
        $customizedFields->setContactInfo7(array_get($fields, 'ContactInfo7'));
        $customizedFields->setContactInfo8(array_get($fields, 'ContactInfo8'));
        $customizedFields->setWebsite1(array_get($fields, 'Website1'));
        $customizedFields->setWebsite2(array_get($fields, 'Website2'));
        $customizedFields->setWebsite3(array_get($fields, 'Website3'));
        $customizedFields->setDesignation1(array_get($fields, 'Designation1'));
        $customizedFields->setDesignation2(array_get($fields, 'Designation2'));
        $customizedFields->setDesignation3(array_get($fields, 'Designation3'));
        $customizedFields->setDesignation4(array_get($fields, 'Designation4'));
        $customizedFields->setDesignation5(array_get($fields, 'Designation5'));
        // After it is saved the first time the ContactAltName will always be used as the display name.
        $customizedFields->setDisplayName(array_get($fields, 'ContactAltName'));
        $customizedFields->setLicenseNumber(array_get($fields, 'LicenseNumber'));
        $customizedFields->setOfficeAltName(array_get($fields, 'OfficeAltName'));
        $customizedFields->setOfficeAltAddressLine1(array_get($fields, 'OfficeAltAddressLine1'));
        $customizedFields->setOfficeAltAddressLine2(array_get($fields, 'OfficeAltAddressLine2'));
        $customizedFields->setOfficeAltAddressCityStateZip(array_get($fields, 'OfficeAltAddressCityStateZip'));
        $customizedFields->setBcField1(array_get($fields, 'BcField1'));
        $customizedFields->setBcField2(array_get($fields, 'BcField2'));
        $customizedFields->setBcField3(array_get($fields, 'BcField3'));
        $customizedFields->setBcField4(array_get($fields, 'BcField4'));
        $customizedFields->setBcField5(array_get($fields, 'BcField5'));
        $customizedFields->setBcField6(array_get($fields, 'BcField6'));
        $customizedFields->setBcPhoto(array_get($fields, 'BcPhoto'));
        $customizedFields->setBicField1(array_get($fields, 'BicField1'));
        $customizedFields->setBicField2(array_get($fields, 'BicField2'));
        $customizedFields->setBicField3(array_get($fields, 'BicField3'));
        $customizedFields->setBicField4(array_get($fields, 'BicField4'));
        $customizedFields->setBicField5(array_get($fields, 'BicField5'));
        $customizedFields->setBicField6(array_get($fields, 'BicField6'));
        $customizedFields->setBicPhoto(array_get($fields, 'BicPhoto'));
        $customizedFields->setFrontCoverSelection(array_get($fields, 'FrontCoverSelection'));

        return $customizedFields;
    }
}