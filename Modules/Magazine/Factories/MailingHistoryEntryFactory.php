<?php

namespace Modules\Magazine\Factories;

use Modules\Magazine\Entities\MailingHistoryEntry;
use Modules\Xrms\Services\PathResolver;

class MailingHistoryEntryFactory
{
    /** @var \Modules\Xrms\Services\PathResolver */
    private $imagePathResolver;

    public function __construct(PathResolver $imagePathResolver)
    {
        $this->imagePathResolver = $imagePathResolver;
    }

    public function createFromArray(array $data) : MailingHistoryEntry
    {
        $entry = new MailingHistoryEntry;

        $bicThumbnailPath = array_get($data, 'bicThumbnailPath');
        $bicXLargePath = array_get($data, 'bicXLargePath');
        $bcThumbnailPath = array_get($data, 'bcThumbnailPath');
        $bcXLargePath = array_get($data, 'bcXLargePath');
        $firstTocThumbnailPath = array_get($data, 'firstTocThumbnailPath');
        $firstTocLargePath = array_get($data, 'firstTocLargePath');
        $secondTocThumbnailPath = array_get($data, 'secondTocThumbnailPath');
        $secondTocLargePath = array_get($data, 'secondTocLargePath');

        $entry->setDate(
            array_get($data, 'Date', array_get($data, 'magazineMailingDate'))
        );
        $entry->setIssueId(
            array_get($data, 'issueId', array_get($data, 'magazineId'))
        );

        $entry->setDeliveryDateStart(array_get($data, 'deliveryDateStart'));
        $entry->setDeliveryDateEnd(array_get($data, 'deliveryDateEnd'));
        $entry->setQuantity(array_get($data, 'directCount'));
        $entry->setPromotionalCopiesCount(array_get($data, 'copyCount'));
        $entry->setPromotionalCopiesAddress(array_get($data, 'copyAddress'));
        $entry->setPdfPath(array_get($data, 'samplePdfExists'));
        $entry->setPrintAgentHistoryId(array_get($data, 'printAgentHistoryId'));

        $entry->setBackInsideCoverThumbnail($this->resolveImageUrl($bicThumbnailPath, 'bic'));
        $entry->setBackInsideCoverImage($this->resolveImageUrl($bicXLargePath, 'bic'));
        $entry->setBackCoverThumbnail($this->resolveImageUrl($bcThumbnailPath, 'bc'));
        $entry->setBackCoverImage($this->resolveImageUrl($bcXLargePath, 'bc'));
        $entry->setFirstTearOutCardThumbnail($this->resolveImageUrl($firstTocThumbnailPath, 'toc'));
        $entry->setFirstTearOutCardImage($this->resolveImageUrl($firstTocLargePath, 'toc'));
        $entry->setSecondTearOutCardThumbnail($this->resolveImageUrl($secondTocThumbnailPath, 'toc'));
        $entry->setSecondTearOutCardImage($this->resolveImageUrl($secondTocLargePath, 'toc'));

        $entry->setSubscribers(array_get($data, 'subscribers', []));

        return $entry;
    }

    private function resolveImageUrl(?string $path, string $type) : string
    {
        return $path
            ? $this->imagePathResolver->resolveUrl($path)
            : asset("images/standard/placeholder-{$type}-thumb.png");
    }
}