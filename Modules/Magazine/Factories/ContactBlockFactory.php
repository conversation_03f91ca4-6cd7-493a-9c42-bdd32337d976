<?php

namespace Modules\Magazine\Factories;

use Modules\Magazine\Entities\ContactBlock;

class ContactBlockFactory
{
    public static function createFromFields(array $fields) : ContactBlock
    {
        $contactBlock = new ContactBlock;

        $contactBlock->setDisplayName(array_get($fields, 'contactBlockDisplayName'));
        $contactBlock->setContactBlockName2(array_get($fields, 'contactBlockName2'));
        $contactBlock->setOfficeName(array_get($fields, 'officeName'));
        $contactBlock->setAgentAddress1(array_get($fields, 'contactBlockOfficeAddress1'));
        $contactBlock->setAgentAddress2(array_get($fields, 'contactBlockOfficeAddress2'));
        $contactBlock->setAgentCityStateZip(array_get($fields, 'contactBlockOfficeAddress3'));
        $contactBlock->setLicenseNumber(array_get($fields, 'licenseNumber'));
        $contactBlock->setTitle1(array_get($fields, 'title1'));
        $contactBlock->setTitle2(array_get($fields, 'title2'));
        $contactBlock->setTitle3(array_get($fields, 'title3'));
        $contactBlock->setTitle4(array_get($fields, 'title4'));
        $contactBlock->setTitle5(array_get($fields, 'title5'));
        $contactBlock->setTitle6(array_get($fields, 'title6'));
        $contactBlock->setTitle7(array_get($fields, 'title7'));
        $contactBlock->setContactInfo1(array_get($fields, 'contactInfo1'));
        $contactBlock->setContactInfo2(array_get($fields, 'contactInfo2'));
        $contactBlock->setContactInfo3(array_get($fields, 'contactInfo3'));
        $contactBlock->setContactInfo4(array_get($fields, 'contactInfo4'));
        $contactBlock->setContactInfo5(array_get($fields, 'contactInfo5'));
        $contactBlock->setContactInfo6(array_get($fields, 'contactInfo6'));
        $contactBlock->setContactInfo7(array_get($fields, 'contactInfo7'));
        $contactBlock->setContactInfo8(array_get($fields, 'contactInfo8'));
        $contactBlock->setContactType1(array_get($fields, 'contactInfo1Label'));
        $contactBlock->setContactType2(array_get($fields, 'contactInfo2Label'));
        $contactBlock->setContactType3(array_get($fields, 'contactInfo3Label'));
        $contactBlock->setContactType4(array_get($fields, 'contactInfo4Label'));
        $contactBlock->setContactType5(array_get($fields, 'contactInfo5Label'));
        $contactBlock->setContactType6(array_get($fields, 'contactInfo6Label'));
        $contactBlock->setContactType7(array_get($fields, 'contactInfo7Label'));
        $contactBlock->setContactType8(array_get($fields, 'contactInfo8Label'));
        $contactBlock->setExt1(array_get($fields, 'contactInfo1Ext'));
        $contactBlock->setExt2(array_get($fields, 'contactInfo2Ext'));
        $contactBlock->setExt3(array_get($fields, 'contactInfo3Ext'));
        $contactBlock->setExt4(array_get($fields, 'contactInfo4Ext'));
        $contactBlock->setExt5(array_get($fields, 'contactInfo5Ext'));
        $contactBlock->setExt6(array_get($fields, 'contactInfo6Ext'));
        $contactBlock->setExt7(array_get($fields, 'contactInfo7Ext'));
        $contactBlock->setExt8(array_get($fields, 'contactInfo8Ext'));
        $contactBlock->setWebsite1(array_get($fields, 'website1'));
        $contactBlock->setWebsite2(array_get($fields, 'website2'));
        $contactBlock->setWebsite3(array_get($fields, 'website3'));
        $contactBlock->setDesignations(array_get($fields, 'designations'));
        $contactBlock->setDesignation1(array_get($fields, 'designation1'));
        $contactBlock->setDesignation2(array_get($fields, 'designation2'));
        $contactBlock->setDesignation3(array_get($fields, 'designation3'));
        $contactBlock->setDesignation4(array_get($fields, 'designation4'));
        $contactBlock->setDesignation5(array_get($fields, 'designation5'));

        return $contactBlock;
    }
}