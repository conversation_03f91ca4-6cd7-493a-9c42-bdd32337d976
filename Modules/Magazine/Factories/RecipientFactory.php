<?php

namespace Modules\Magazine\Factories;

use Modules\Magazine\Entities\Recipient;

class RecipientFactory
{
    public function createFromArray(array $data) : Recipient
    {
        $recipient = new Recipient;

        $recipient->setId(array_get($data, 'subscriberId'));
        $recipient->setFirstName(array_get($data, 'firstName'));
        $recipient->setLastName(array_get($data, 'lastName') ?? "");
        $recipient->setStreet(array_get($data, 'street'));
        $recipient->setCity(array_get($data, 'city'));
        $recipient->setState(array_get($data, 'state'));
        $recipient->setZip(array_get($data, 'zip'));
        $recipient->setTitle(array_get($data, 'title'));
        $recipient->setCompanyName(array_get($data, 'companyName'));
        $recipient->setRecipientGroupId(array_get($data, 'subscriberGroupId'));
        $recipient->setSpouseFirstName(array_get($data, 'spouseFirstName'));
        $recipient->setSpouseLastName(array_get($data, 'spouseLastName'));
        $recipient->setNote(array_get($data, 'note'));
        $recipient->setLetterSalutation(array_get($data, 'letterSalutation'));
        $recipient->setMailingSalutation(array_get($data, 'mailingSalutation'));

        $purchaseDate = array_get($data, 'purchaseDate');
        $recipient->setPurchaseDate($purchaseDate ? date('Y-m-d', $purchaseDate) : null);
        $recipient->setPurchasePrice(array_get($data, 'purchasePrice'));

        $dateAdded = array_get($data, 'dateAdded');
        $recipient->setDateAdded($dateAdded ? date('Y-m-d', $dateAdded) : null);

        $recipient->setBirthday(array_get($data, 'birthday'));
        $recipient->setSpouseBirthday(array_get($data, 'spouseBirthday'));
        $recipient->setHomePhone(array_get($data, 'homePhone'));
        $recipient->setHomeExt(array_get($data, 'homeExt'));
        $recipient->setWorkPhone(array_get($data, 'workPhone'));
        $recipient->setWorkExt(array_get($data, 'workExt'));
        $recipient->setCellPhone(array_get($data, 'cellPhone'));
        $recipient->setCellExt(array_get($data, 'cellExt'));
        $recipient->setFax(array_get($data, 'fax'));
        $recipient->setEmail(array_get($data, 'email'));
        $recipient->setEmail2(array_get($data, 'email2'));
        $recipient->setPayAhead(array_get($data, 'payAhead') ?? false);
        $recipient->setError(array_get($data, 'error') ?? false);
        $recipient->setClientInfoError(array_get($data, 'clientInfoError') ?? false);
        $recipient->setDuplicate(array_get($data, 'duplicate') ?? false);
        $recipient->setUnavailable(array_get($data, 'unavailable') ?? false);
        $recipient->setForceSend(array_get($data, 'forceSend') ?? false);
        $recipient->setFirstPrintRun(array_get($data, 'firstPrintRun') ?? false);
        $recipient->setSecondPrintRun(array_get($data, 'secondPrintRun') ?? false);
        $recipient->setThirdOrGreaterPrintRun(array_get($data, 'thirdOrGreaterPrintRun') ?? false);
        $recipient->setPhoto(array_get($data, 'photo'));

        return $recipient;
    }
}