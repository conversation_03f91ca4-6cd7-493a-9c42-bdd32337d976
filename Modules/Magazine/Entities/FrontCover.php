<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class FrontCover implements Arrayable, Jsonable
{
    /** @var string */
    private $complimentsOf;

    /** @var \Illuminate\Support\Collection */
    private $covers;

    /** @var int|null */
    private $selectedId;

    /** @var \Illuminate\Support\Collection */
    private $types;

    /** @var \Illuminate\Support\Collection */
    private $specialEditionCovers;

    /** @var string|null */
    private $specialEditionName;

    public function __construct(
        string $complimentsOf,
        iterable $covers,
        ?int $selectedId,
        iterable $types,
        iterable $specialEditionCovers,
        ?string $specialEditionName
    ) {
        $this->complimentsOf = $complimentsOf;
        $this->selectedId = $selectedId;
        $this->covers = collect($covers);
        $this->types = collect($types);
        $this->specialEditionCovers = collect($specialEditionCovers);
        $this->specialEditionName = $specialEditionName;
    }

    public function getComplimentsOf() : string
    {
        return $this->complimentsOf;
    }

    public function getCovers() : Collection
    {
        return $this->covers;
    }

    public function getSelectedId() : ?int
    {
        return $this->selectedId;
    }

    public function getTypes() : Collection
    {
        return $this->types;
    }

    public function getSpecialEditionCovers() : Collection
    {
        return $this->specialEditionCovers;
    }

    public function getSpecialEditionName() : ?string
    {
        return $this->specialEditionName;
    }

    public function toArray() : array
    {
        return [
            'compliments_of' => $this->getComplimentsOf(),
            'covers' => $this->getCovers(),
            'selected_id' => $this->getSelectedId(),
            'types' => $this->getTypes(),
            'special_edition_covers' => $this->getSpecialEditionCovers(),
            'special_edition_name' => $this->getSpecialEditionName(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}