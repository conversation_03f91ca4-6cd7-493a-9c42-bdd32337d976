<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class MailingList implements Arrayable, Jsonable
{
    /** @var bool */
    private $hasExclusivityOnDisabledGroups;

    /** @var int */
    private $maxRecipients;

    /** @var int */
    private $minRecipients;

    /** @var \Illuminate\Support\Collection */
    private $recipients;

    /** @var \Illuminate\Support\Collection */
    private $recipientGroups;

    /** @var int */
    private $recipientsPrinting;

    /** @var int */
    private $promotionalCopiesPrinting;

    /** @var int */
    private $totalCopiesPrinting;

    /** @var int */
    private $totalRecipients;

    public function __construct(
        bool $hasExclusivityOnDisabledGroups,
        int $maxRecipients,
        int $minRecipients,
        iterable $recipients,
        iterable $recipientGroups,
        int $recipientsPrinting,
        int $promotionalCopiesPrinting,
        int $totalCopiesPrinting,
        int $totalRecipients
    ) {
        $this->hasExclusivityOnDisabledGroups = $hasExclusivityOnDisabledGroups;
        $this->maxRecipients = $maxRecipients;
        $this->minRecipients = $minRecipients;
        $this->recipients = collect($recipients);
        $this->recipientGroups = collect($recipientGroups);
        $this->recipientsPrinting = $recipientsPrinting;
        $this->promotionalCopiesPrinting = $promotionalCopiesPrinting;
        $this->totalCopiesPrinting = $totalCopiesPrinting;
        $this->totalRecipients = $totalRecipients;
    }

    public function hasExclusivityOnDisabledGroups() : bool
    {
        return $this->hasExclusivityOnDisabledGroups;
    }

    public function getMaxRecipients() : int
    {
        return $this->maxRecipients;
    }

    public function getMinRecipients() : int
    {
        return $this->minRecipients;
    }

    public function getRecipients() : Collection
    {
        return $this->recipients;
    }

    public function getRecipientGroups() : Collection
    {
        return $this->recipientGroups;
    }

    public function getRecipientsPrinting() : int
    {
        return $this->recipientsPrinting;
    }

    public function getPromotionalCopiesPrinting() : int
    {
        return $this->promotionalCopiesPrinting;
    }

    public function getTotalCopiesPrinting() : int
    {
        return $this->totalCopiesPrinting;
    }

    public function getTotalRecipients() : int
    {
        return $this->totalRecipients;
    }

    public function getFirstPrintingGroupId() : ?int
    {
        $group = $this->getRecipientGroups()->first(function (RecipientGroup $group) {
            return $group->isPrinting() && $group->getRecipientCount() > 0;
        });

        return $group ? $group->getId() : null;
    }

    public function toArray() : array
    {
        return [
            'first_printing_group_id' => $this->getFirstPrintingGroupId(),
            'has_exclusivity_on_disabled_groups' => $this->hasExclusivityOnDisabledGroups(),
            'max_recipients' => $this->getMaxRecipients(),
            'min_recipients' => $this->getMinRecipients(),
            'recipients' => $this->getRecipients()->toArray(),
            'recipient_groups' => $this->getRecipientGroups()->toArray(),
            'recipients_printing' => $this->getRecipientsPrinting(),
            'promotional_copies_printing' => $this->getPromotionalCopiesPrinting(),
            'total_copies_printing' => $this->getTotalCopiesPrinting(),
            'total_recipients' => $this->getTotalRecipients(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}
