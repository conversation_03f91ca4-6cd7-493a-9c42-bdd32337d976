<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class ContactBlock implements Arrayable, Jsonable
{
    private $displayName;
    private $contactBlockName2;
    private $designations;
    private $designation1;
    private $designation2;
    private $designation3;
    private $designation4;
    private $designation5;
    private $title1;
    private $title2;
    private $title3;
    private $title4;
    private $title5;
    private $title6;
    private $title7;
    private $ext1;
    private $customName1;
    private $contactType1;
    private $contactInfo1;
    private $ext2;
    private $customName2;
    private $contactType2;
    private $contactInfo2;
    private $ext3;
    private $customName3;
    private $contactType3;
    private $contactInfo3;
    private $ext4;
    private $customName4;
    private $contactType4;
    private $contactInfo4;
    private $ext5;
    private $customName5;
    private $contactType5;
    private $contactInfo5;
    private $ext6;
    private $customName6;
    private $contactType6;
    private $contactInfo6;
    private $ext7;
    private $customName7;
    private $contactType7;
    private $contactInfo7;
    private $ext8;
    private $customName8;
    private $contactType8;
    private $contactInfo8;
    private $website1;
    private $website2;
    private $website3;
    private $officeName;
    private $agentAddress1;
    private $agentAddress2;
    private $agentCityStateZip;
    private $licenseNumber;

    /**
     * @return mixed
     */
    public function getDisplayName()
    {
        return $this->displayName;
    }

    /**
     * @param mixed $displayName
     */
    public function setDisplayName($displayName): void
    {
        $this->displayName = $displayName;
    }

    /**
     * @return mixed
     */
    public function getContactBlockName2()
    {
        return $this->contactBlockName2;
    }

    /**
     * @param mixed $contactBlockName2
     */
    public function setContactBlockName2($contactBlockName2): void
    {
        $this->contactBlockName2 = $contactBlockName2;
    }

    /**
     * @return mixed
     */
    public function getDesignations()
    {
        return $this->designations;
    }

    /**
     * @param mixed $designations
     */
    public function setDesignations($designations): void
    {
        $this->designations = $designations;
    }

    /**
     * @return mixed
     */
    public function getDesignation1()
    {
        return $this->designation1;
    }

    /**
     * @param mixed $designation1
     */
    public function setDesignation1($designation1): void
    {
        $this->designation1 = $designation1;
    }

    /**
     * @return mixed
     */
    public function getDesignation2()
    {
        return $this->designation2;
    }

    /**
     * @param mixed $designation2
     */
    public function setDesignation2($designation2): void
    {
        $this->designation2 = $designation2;
    }

    /**
     * @return mixed
     */
    public function getDesignation3()
    {
        return $this->designation3;
    }

    /**
     * @param mixed $designation3
     */
    public function setDesignation3($designation3): void
    {
        $this->designation3 = $designation3;
    }

    /**
     * @return mixed
     */
    public function getDesignation4()
    {
        return $this->designation4;
    }

    /**
     * @param mixed $designation4
     */
    public function setDesignation4($designation4): void
    {
        $this->designation4 = $designation4;
    }

    /**
     * @return mixed
     */
    public function getDesignation5()
    {
        return $this->designation5;
    }

    /**
     * @param mixed $designation5
     */
    public function setDesignation5($designation5): void
    {
        $this->designation5 = $designation5;
    }

    /**
     * @return mixed
     */
    public function getTitle1()
    {
        return $this->title1;
    }

    /**
     * @param mixed $title1
     */
    public function setTitle1($title1): void
    {
        $this->title1 = $title1;
    }

    /**
     * @return mixed
     */
    public function getTitle2()
    {
        return $this->title2;
    }

    /**
     * @param mixed $title2
     */
    public function setTitle2($title2): void
    {
        $this->title2 = $title2;
    }

    /**
     * @return mixed
     */
    public function getTitle3()
    {
        return $this->title3;
    }

    /**
     * @param mixed $title3
     */
    public function setTitle3($title3): void
    {
        $this->title3 = $title3;
    }

    /**
     * @return mixed
     */
    public function getTitle4()
    {
        return $this->title4;
    }

    /**
     * @param mixed $title4
     */
    public function setTitle4($title4): void
    {
        $this->title4 = $title4;
    }

    /**
     * @return mixed
     */
    public function getTitle5()
    {
        return $this->title5;
    }

    /**
     * @param mixed $title5
     */
    public function setTitle5($title5): void
    {
        $this->title5 = $title5;
    }

    /**
     * @return mixed
     */
    public function getTitle6()
    {
        return $this->title6;
    }

    /**
     * @param mixed $title6
     */
    public function setTitle6($title6): void
    {
        $this->title6 = $title6;
    }

    /**
     * @return mixed
     */
    public function getTitle7()
    {
        return $this->title7;
    }

    /**
     * @param mixed $title7
     */
    public function setTitle7($title7): void
    {
        $this->title7 = $title7;
    }

    /**
     * @return mixed
     */
    public function getExt1()
    {
        return $this->ext1;
    }

    /**
     * @param mixed $ext1
     */
    public function setExt1($ext1): void
    {
        $this->ext1 = $ext1;
    }

    /**
     * @return mixed
     */
    public function getCustomName1()
    {
        return $this->customName1;
    }

    /**
     * @param mixed $customName1
     */
    public function setCustomName1($customName1): void
    {
        $this->customName1 = $customName1;
    }

    /**
     * @return mixed
     */
    public function getContactType1()
    {
        return $this->contactType1;
    }

    /**
     * @param mixed $contactType1
     */
    public function setContactType1($contactType1): void
    {
        $this->contactType1 = $contactType1;
    }

    /**
     * @return mixed
     */
    public function getContactInfo1()
    {
        return $this->contactInfo1;
    }

    /**
     * @param mixed $contactInfo1
     */
    public function setContactInfo1($contactInfo1): void
    {
        $this->contactInfo1 = $contactInfo1;
    }

    /**
     * @return mixed
     */
    public function getExt2()
    {
        return $this->ext2;
    }

    /**
     * @param mixed $ext2
     */
    public function setExt2($ext2): void
    {
        $this->ext2 = $ext2;
    }

    /**
     * @return mixed
     */
    public function getCustomName2()
    {
        return $this->customName2;
    }

    /**
     * @param mixed $customName2
     */
    public function setCustomName2($customName2): void
    {
        $this->customName2 = $customName2;
    }

    /**
     * @return mixed
     */
    public function getContactType2()
    {
        return $this->contactType2;
    }

    /**
     * @param mixed $contactType2
     */
    public function setContactType2($contactType2): void
    {
        $this->contactType2 = $contactType2;
    }

    /**
     * @return mixed
     */
    public function getContactInfo2()
    {
        return $this->contactInfo2;
    }

    /**
     * @param mixed $contactInfo2
     */
    public function setContactInfo2($contactInfo2): void
    {
        $this->contactInfo2 = $contactInfo2;
    }

    /**
     * @return mixed
     */
    public function getExt3()
    {
        return $this->ext3;
    }

    /**
     * @param mixed $ext3
     */
    public function setExt3($ext3): void
    {
        $this->ext3 = $ext3;
    }

    /**
     * @return mixed
     */
    public function getCustomName3()
    {
        return $this->customName3;
    }

    /**
     * @param mixed $customName3
     */
    public function setCustomName3($customName3): void
    {
        $this->customName3 = $customName3;
    }

    /**
     * @return mixed
     */
    public function getContactType3()
    {
        return $this->contactType3;
    }

    /**
     * @param mixed $contactType3
     */
    public function setContactType3($contactType3): void
    {
        $this->contactType3 = $contactType3;
    }

    /**
     * @return mixed
     */
    public function getContactInfo3()
    {
        return $this->contactInfo3;
    }

    /**
     * @param mixed $contactInfo3
     */
    public function setContactInfo3($contactInfo3): void
    {
        $this->contactInfo3 = $contactInfo3;
    }

    /**
     * @return mixed
     */
    public function getExt4()
    {
        return $this->ext4;
    }

    /**
     * @param mixed $ext4
     */
    public function setExt4($ext4): void
    {
        $this->ext4 = $ext4;
    }

    /**
     * @return mixed
     */
    public function getCustomName4()
    {
        return $this->customName4;
    }

    /**
     * @param mixed $customName4
     */
    public function setCustomName4($customName4): void
    {
        $this->customName4 = $customName4;
    }

    /**
     * @return mixed
     */
    public function getContactType4()
    {
        return $this->contactType4;
    }

    /**
     * @param mixed $contactType4
     */
    public function setContactType4($contactType4): void
    {
        $this->contactType4 = $contactType4;
    }

    /**
     * @return mixed
     */
    public function getContactInfo4()
    {
        return $this->contactInfo4;
    }

    /**
     * @param mixed $contactInfo4
     */
    public function setContactInfo4($contactInfo4): void
    {
        $this->contactInfo4 = $contactInfo4;
    }

    /**
     * @return mixed
     */
    public function getExt5()
    {
        return $this->ext5;
    }

    /**
     * @param mixed $ext5
     */
    public function setExt5($ext5): void
    {
        $this->ext5 = $ext5;
    }

    /**
     * @return mixed
     */
    public function getCustomName5()
    {
        return $this->customName5;
    }

    /**
     * @param mixed $customName5
     */
    public function setCustomName5($customName5): void
    {
        $this->customName5 = $customName5;
    }

    /**
     * @return mixed
     */
    public function getContactType5()
    {
        return $this->contactType5;
    }

    /**
     * @param mixed $contactType5
     */
    public function setContactType5($contactType5): void
    {
        $this->contactType5 = $contactType5;
    }

    /**
     * @return mixed
     */
    public function getContactInfo5()
    {
        return $this->contactInfo5;
    }

    /**
     * @param mixed $contactInfo5
     */
    public function setContactInfo5($contactInfo5): void
    {
        $this->contactInfo5 = $contactInfo5;
    }

    /**
     * @return mixed
     */
    public function getExt6()
    {
        return $this->ext6;
    }

    /**
     * @param mixed $ext6
     */
    public function setExt6($ext6): void
    {
        $this->ext6 = $ext6;
    }

    /**
     * @return mixed
     */
    public function getCustomName6()
    {
        return $this->customName6;
    }

    /**
     * @param mixed $customName6
     */
    public function setCustomName6($customName6): void
    {
        $this->customName6 = $customName6;
    }

    /**
     * @return mixed
     */
    public function getContactType6()
    {
        return $this->contactType6;
    }

    /**
     * @param mixed $contactType6
     */
    public function setContactType6($contactType6): void
    {
        $this->contactType6 = $contactType6;
    }

    /**
     * @return mixed
     */
    public function getContactInfo6()
    {
        return $this->contactInfo6;
    }

    /**
     * @param mixed $contactInfo6
     */
    public function setContactInfo6($contactInfo6): void
    {
        $this->contactInfo6 = $contactInfo6;
    }

    /**
     * @return mixed
     */
    public function getExt7()
    {
        return $this->ext7;
    }

    /**
     * @param mixed $ext7
     */
    public function setExt7($ext7): void
    {
        $this->ext7 = $ext7;
    }

    /**
     * @return mixed
     */
    public function getCustomName7()
    {
        return $this->customName7;
    }

    /**
     * @param mixed $customName7
     */
    public function setCustomName7($customName7): void
    {
        $this->customName7 = $customName7;
    }

    /**
     * @return mixed
     */
    public function getContactType7()
    {
        return $this->contactType7;
    }

    /**
     * @param mixed $contactType7
     */
    public function setContactType7($contactType7): void
    {
        $this->contactType7 = $contactType7;
    }

    /**
     * @return mixed
     */
    public function getContactInfo7()
    {
        return $this->contactInfo7;
    }

    /**
     * @param mixed $contactInfo7
     */
    public function setContactInfo7($contactInfo7): void
    {
        $this->contactInfo7 = $contactInfo7;
    }

    /**
     * @return mixed
     */
    public function getExt8()
    {
        return $this->ext8;
    }

    /**
     * @param mixed $ext8
     */
    public function setExt8($ext8): void
    {
        $this->ext8 = $ext8;
    }

    /**
     * @return mixed
     */
    public function getCustomName8()
    {
        return $this->customName8;
    }

    /**
     * @param mixed $customName8
     */
    public function setCustomName8($customName8): void
    {
        $this->customName8 = $customName8;
    }

    /**
     * @return mixed
     */
    public function getContactType8()
    {
        return $this->contactType8;
    }

    /**
     * @param mixed $contactType8
     */
    public function setContactType8($contactType8): void
    {
        $this->contactType8 = $contactType8;
    }

    /**
     * @return mixed
     */
    public function getContactInfo8()
    {
        return $this->contactInfo8;
    }

    /**
     * @param mixed $contactInfo8
     */
    public function setContactInfo8($contactInfo8): void
    {
        $this->contactInfo8 = $contactInfo8;
    }

    /**
     * @return mixed
     */
    public function getWebsite1()
    {
        return $this->website1;
    }

    /**
     * @param mixed $website1
     */
    public function setWebsite1($website1): void
    {
        $this->website1 = $website1;
    }

    /**
     * @return mixed
     */
    public function getWebsite2()
    {
        return $this->website2;
    }

    /**
     * @param mixed $website2
     */
    public function setWebsite2($website2): void
    {
        $this->website2 = $website2;
    }

    /**
     * @return mixed
     */
    public function getWebsite3()
    {
        return $this->website3;
    }

    /**
     * @param mixed $website3
     */
    public function setWebsite3($website3): void
    {
        $this->website3 = $website3;
    }

    /**
     * @return mixed
     */
    public function getOfficeName()
    {
        return $this->officeName;
    }

    /**
     * @param mixed $officeName
     */
    public function setOfficeName($officeName): void
    {
        $this->officeName = $officeName;
    }

    /**
     * @return mixed
     */
    public function getAgentAddress1()
    {
        return $this->agentAddress1;
    }

    /**
     * @param mixed $agentAddress1
     */
    public function setAgentAddress1($agentAddress1): void
    {
        $this->agentAddress1 = $agentAddress1;
    }

    /**
     * @return mixed
     */
    public function getAgentAddress2()
    {
        return $this->agentAddress2;
    }

    /**
     * @param mixed $agentAddress2
     */
    public function setAgentAddress2($agentAddress2): void
    {
        $this->agentAddress2 = $agentAddress2;
    }

    /**
     * @return mixed
     */
    public function getAgentCityStateZip()
    {
        return $this->agentCityStateZip;
    }

    /**
     * @param mixed $agentCityStateZip
     */
    public function setAgentCityStateZip($agentCityStateZip): void
    {
        $this->agentCityStateZip = $agentCityStateZip;
    }

    /**
     * @return mixed
     */
    public function getLicenseNumber()
    {
        return $this->licenseNumber;
    }

    /**
     * @param mixed $licenseNumber
     */
    public function setLicenseNumber($licenseNumber): void
    {
        $this->licenseNumber = $licenseNumber;
    }

    public function toArray() : array
    {
        return [
            'displayName' => $this->getDisplayName(),
            'contactBlockName2' => $this->getContactBlockName2(),
            'designations' => $this->getDesignations(),
            'designation1' => $this->getDesignation1(),
            'designation2' => $this->getDesignation2(),
            'designation3' => $this->getDesignation3(),
            'designation4' => $this->getDesignation4(),
            'designation5' => $this->getDesignation5(),
            'title1' => $this->getTitle1(),
            'title2' => $this->getTitle2(),
            'title3' => $this->getTitle3(),
            'title4' => $this->getTitle4(),
            'title5' => $this->getTitle5(),
            'title6' => $this->getTitle6(),
            'title7' => $this->getTitle7(),
            'ext1' => $this->getExt1(),
            'customName1' => $this->getCustomName1(),
            'contactType1' => $this->getContactType1(),
            'contactInfo1' => $this->getContactInfo1(),
            'ext2' => $this->getExt2(),
            'customName2' => $this->getCustomName2(),
            'contactType2' => $this->getContactType2(),
            'contactInfo2' => $this->getContactInfo2(),
            'ext3' => $this->getExt3(),
            'customName3' => $this->getCustomName3(),
            'contactType3' => $this->getContactType3(),
            'contactInfo3' => $this->getContactInfo3(),
            'ext4' => $this->getExt4(),
            'customName4' => $this->getCustomName4(),
            'contactType4' => $this->getContactType4(),
            'contactInfo4' => $this->getContactInfo4(),
            'ext5' => $this->getExt5(),
            'customName5' => $this->getCustomName5(),
            'contactType5' => $this->getContactType5(),
            'contactInfo5' => $this->getContactInfo5(),
            'ext6' => $this->getExt6(),
            'customName6' => $this->getCustomName6(),
            'contactType6' => $this->getContactType6(),
            'contactInfo6' => $this->getContactInfo6(),
            'ext7' => $this->getExt7(),
            'customName7' => $this->getCustomName7(),
            'contactType7' => $this->getContactType7(),
            'contactInfo7' => $this->getContactInfo7(),
            'ext8' => $this->getExt8(),
            'customName8' => $this->getCustomName8(),
            'contactType8' => $this->getContactType8(),
            'contactInfo8' => $this->getContactInfo8(),
            'website1' => $this->getWebsite1(),
            'website2' => $this->getWebsite2(),
            'website3' => $this->getWebsite3(),
            'officeName' => $this->getOfficeName(),
            'agentAddress1' => $this->getAgentAddress1(),
            'agentAddress2' => $this->getAgentAddress2(),
            'agentCityStateZip' => $this->getAgentCityStateZip(),
            'licenseNumber' => $this->getLicenseNumber(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}