<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class Recipient implements Arrayable, Jsonable
{
    /** @var int */
    private $id;

    /** @var string */
    private $firstName;

    /** @var string */
    private $lastName;

    /** @var string|null */
    private $street;

    /** @var string|null */
    private $city;

    /** @var string|null */
    private $state;

    /** @var string|null */
    private $zip;

    /** @var string|null */
    private $title;

    /** @var string|null */
    private $companyName;

    /** @var int */
    private $recipientGroupId;

    /** @var string|null */
    private $spouseFirstName;

    /** @var string|null */
    private $spouseLastName;

    /** @var string|null */
    private $note;

    /** @var string|null */
    private $letterSalutation;

    /** @var string|null */
    private $mailingSalutation;

    /** @var string|null */
    private $purchaseDate;

    /** @var string|null */
    private $purchasePrice;

    /** @var string|null */
    private $dateAdded;

    /** @var string|null */
    private $birthday;

    /** @var string|null */
    private $spouseBirthday;

    /** @var string|null */
    private $homePhone;

    /** @var string|null */
    private $homeExt;

    /** @var string|null */
    private $workPhone;

    /** @var string|null */
    private $workExt;

    /** @var string|null */
    private $cellPhone;

    /** @var string|null */
    private $cellExt;

    /** @var string|null */
    private $fax;

    /** @var string|null */
    private $email;

    /** @var string|null */
    private $email2;

    /** @var bool */
    private $payAhead;

    /** @var bool */
    private $error;

    /** @var bool */
    private $clientInfoError;

    /** @var bool */
    private $duplicate;

    /** @var bool */
    private $unavailable;

    /** @var bool */
    private $forceSend;

    /** @var bool */
    private $firstPrintRun;

    /** @var bool */
    private $secondPrintRun;

    /** @var bool */
    private $thirdOrGreaterPrintRun;

    /** @var */
    private $photo;

    public function getId() : int
    {
        return $this->id;
    }

    public function setId(int $id)
    {
        $this->id = $id;
    }

    public function getFirstName() : ?string
    {
        return $this->firstName;
    }

    public function setFirstName(?string $firstName)
    {
        $this->firstName = $firstName;
    }

    public function getLastName() : string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName)
    {
        $this->lastName = $lastName;
    }

    public function getStreet() : ?string
    {
        return $this->street;
    }

    public function setStreet(?string $street)
    {
        $this->street = $street;
    }

    public function getCity() : ?string
    {
        return $this->city;
    }

    public function setCity(?string $city)
    {
        $this->city = $city;
    }

    public function getState() : ?string
    {
        return $this->state;
    }

    public function setState(?string $state)
    {
        $this->state = $state;
    }

    public function getZip() : ?string
    {
        return $this->zip;
    }

    public function setZip(?string $zip)
    {
        $this->zip = $zip;
    }

    public function getTitle() : ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title)
    {
        $this->title = $title;
    }

    public function getCompanyName() : ?string
    {
        return $this->companyName;
    }

    public function setCompanyName(?string $companyName)
    {
        $this->companyName = $companyName;
    }

    public function getRecipientGroupId() : int
    {
        return $this->recipientGroupId;
    }

    public function setRecipientGroupId(int $recipientGroupId)
    {
        $this->recipientGroupId = $recipientGroupId;
    }

    public function getSpouseFirstName() : ?string
    {
        return $this->spouseFirstName;
    }

    public function setSpouseFirstName(?string $spouseFirstName)
    {
        $this->spouseFirstName = $spouseFirstName;
    }

    public function getSpouseLastName() : ?string
    {
        return $this->spouseLastName;
    }

    public function setSpouseLastName(?string $spouseLastName)
    {
        $this->spouseLastName = $spouseLastName;
    }

    public function getNote() : ?string
    {
        return $this->note;
    }

    public function setNote(?string $note)
    {
        $this->note = $note;
    }

    public function getLetterSalutation() : ?string
    {
        return $this->letterSalutation;
    }

    public function setLetterSalutation(?string $letterSalutation)
    {
        $this->letterSalutation = $letterSalutation;
    }

    public function getMailingSalutation() : ?string
    {
        return $this->mailingSalutation;
    }

    public function setMailingSalutation(?string $mailingSalutation)
    {
        $this->mailingSalutation = $mailingSalutation;
    }

    public function getPurchaseDate() : ?string
    {
        return $this->purchaseDate;
    }

    public function setPurchaseDate(?string $purchaseDate)
    {
        $this->purchaseDate = $purchaseDate;
    }

    public function getPurchasePrice() : ?string
    {
        return $this->purchasePrice;
    }

    public function setPurchasePrice(?string $purchasePrice)
    {
        $this->purchasePrice = $purchasePrice;
    }

    public function getDateAdded() : ?string
    {
        return $this->dateAdded;
    }

    public function setDateAdded(?string $dateAdded)
    {
        $this->dateAdded = $dateAdded;
    }

    public function getBirthday() : ?string
    {
        return $this->birthday;
    }

    public function setBirthday(?string $birthday)
    {
        $this->birthday = $birthday;
    }

    public function getSpouseBirthday() : ?string
    {
        return $this->spouseBirthday;
    }

    public function setSpouseBirthday(?string $spouseBirthday)
    {
        $this->spouseBirthday = $spouseBirthday;
    }

    public function getHomePhone() : ?string
    {
        return $this->homePhone;
    }

    public function setHomePhone(?string $homePhone)
    {
        $this->homePhone = $homePhone;
    }

    public function getHomeExt() : ?string
    {
        return $this->homeExt;
    }

    public function setHomeExt(?string $homeExt)
    {
        $this->homeExt = $homeExt;
    }

    public function getWorkPhone() : ?string
    {
        return $this->workPhone;
    }

    public function setWorkPhone(?string $workPhone)
    {
        $this->workPhone = $workPhone;
    }

    public function getWorkExt() : ?string
    {
        return $this->workExt;
    }

    public function setWorkExt(?string $workExt)
    {
        $this->workExt = $workExt;
    }

    public function getCellPhone() : ?string
    {
        return $this->cellPhone;
    }

    public function setCellPhone(?string $cellPhone)
    {
        $this->cellPhone = $cellPhone;
    }

    public function getCellExt() : ?string
    {
        return $this->cellExt;
    }

    public function setCellExt(?string $cellExt)
    {
        $this->cellExt = $cellExt;
    }

    public function getFax() : ?string
    {
        return $this->fax;
    }

    public function setFax(?string $fax)
    {
        $this->fax = $fax;
    }

    public function getEmail() : ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email)
    {
        $this->email = $email;
    }

    public function getEmail2() : ?string
    {
        return $this->email2;
    }

    public function setEmail2(?string $email2)
    {
        $this->email2 = $email2;
    }

    public function getPayAhead() : bool
    {
        return $this->payAhead;
    }

    public function setPayAhead(bool $payAhead)
    {
        $this->payAhead = $payAhead;
    }

    public function getError() : bool
    {
        return $this->error;
    }

    public function setError(bool $error)
    {
        $this->error = $error;
    }

    public function getClientInfoError() : bool
    {
        return $this->clientInfoError;
    }

    public function setClientInfoError(bool $clientInfoError)
    {
        $this->clientInfoError = $clientInfoError;
    }

    public function getDuplicate() : bool
    {
        return $this->duplicate;
    }

    public function setDuplicate(bool $duplicate)
    {
        $this->duplicate = $duplicate;
    }

    public function getUnavailable() : bool
    {
        return $this->unavailable;
    }

    public function setUnavailable(bool $unavailable)
    {
        $this->unavailable = $unavailable;
    }

    public function getForceSend() : bool
    {
        return $this->forceSend;
    }

    public function setForceSend(bool $forceSend)
    {
        $this->forceSend = $forceSend;
    }

    public function getFirstPrintRun() : bool
    {
        return $this->firstPrintRun;
    }

    public function setFirstPrintRun(bool $firstPrintRun)
    {
        $this->firstPrintRun = $firstPrintRun;
    }

    public function getSecondPrintRun() : bool
    {
        return $this->secondPrintRun;
    }

    public function setSecondPrintRun(bool $secondPrintRun)
    {
        $this->secondPrintRun = $secondPrintRun;
    }

    public function getThirdOrGreaterPrintRun() : bool
    {
        return $this->thirdOrGreaterPrintRun;
    }

    public function setThirdOrGreaterPrintRun(bool $thirdOrGreaterPrintRun)
    {
        $this->thirdOrGreaterPrintRun = $thirdOrGreaterPrintRun;
    }

    public function getPhoto()
    {
        return $this->photo;
    }

    public function setPhoto($photo)
    {
        $this->photo = $photo;
    }

    public function toArray() : array
    {
        return [
            'id' => $this->getId(),
            'first_name' => $this->getFirstName(),
            'last_name' => $this->getLastName(),
            'street' => $this->getStreet(),
            'city' => $this->getCity(),
            'state' => $this->getState(),
            'zip' => $this->getZip(),
            'title' => $this->getTitle(),
            'company_name' => $this->getCompanyName(),
            'recipient_group_id' => $this->getRecipientGroupId(),
            'spouse_first_name' => $this->getSpouseFirstName(),
            'spouse_last_name' => $this->getSpouseLastName(),
            'note' => $this->getNote(),
            'letter_salutation' => $this->getLetterSalutation(),
            'mailing_salutation' => $this->getMailingSalutation(),
            'purchase_date' => $this->getPurchaseDate(),
            'purchase_price' => $this->getPurchasePrice(),
            'date_added' => $this->getDateAdded(),
            'birthday' => $this->getBirthday(),
            'spouse_birthday' => $this->getSpouseBirthday(),
            'home_phone' => $this->getHomePhone(),
            'home_ext' => $this->getHomeExt(),
            'work_phone' => $this->getWorkPhone(),
            'work_ext' => $this->getWorkExt(),
            'cell_phone' => $this->getCellPhone(),
            'cell_ext' => $this->getCellExt(),
            'fax' => $this->getFax(),
            'email' => $this->getEmail(),
            'email2' => $this->getEmail2(),
            'pay_ahead' => $this->getPayAhead(),
            'error' => $this->getError(),
            'client_info_error' => $this->getClientInfoError(),
            'duplicate' => $this->getDuplicate(),
            'unavailable' => $this->getUnavailable(),
            'force_send' => $this->getForceSend(),
            'first_print_run' => $this->getFirstPrintRun(),
            'second_print_run' => $this->getSecondPrintRun(),
            'third_or_greater_print_run' => $this->getThirdOrGreaterPrintRun(),
            'photo' => $this->getPhoto(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}