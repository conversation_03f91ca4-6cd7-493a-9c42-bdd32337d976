<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;
use Modules\Xrms\Entities\AgentPhoto;

class ContactBlockPreview implements Arrayable, Jsonable
{
    /** @var \Illuminate\Support\Collection */
    private $agentPhotos;

    /** @var \Modules\Magazine\Entities\ContactBlock */
    private $contactBlock;

    /** @var \Modules\Magazine\Entities\OfficeLogo */
    private $officeLogo;

    /** @var \Modules\Magazine\Entities\OfficeDisplayOptions */
    private $officeDisplayOptions;

    public function __construct(
        iterable $agentPhotos,
        ContactBlock $contactBlock,
        ?OfficeLogo $officeLogo,
        OfficeDisplayOptions $officeDisplayOptions
    ) {
        $this->agentPhotos = collect($agentPhotos);
        $this->contactBlock = $contactBlock;
        $this->officeLogo = $officeLogo;
        $this->officeDisplayOptions = $officeDisplayOptions;
    }

    public function getAgentPhotos() : Collection
    {
        return $this->agentPhotos;
    }

    public function getContactBlock() : ContactBlock
    {
        return $this->contactBlock;
    }

    public function getOfficeLogo() : ?OfficeLogo
    {
        return $this->officeLogo;
    }

    public function getOfficeDisplayOptions() : OfficeDisplayOptions
    {
        return $this->officeDisplayOptions;
    }

    public function getMainAgentPhoto() : ?AgentPhoto
    {
        return $this->agentPhotos->first(function (AgentPhoto $agentPhoto) {
            return $agentPhoto->isMainPhoto();
        });
    }

    public function findAgentPhoto(int $photoId) : ?AgentPhoto
    {
        return $this->agentPhotos->first(function (AgentPhoto $agentPhoto) use ($photoId) {
            return $agentPhoto->getMagazineFileId() == $photoId;
        });
    }

    public function toArray() : array
    {
        return [
            'agent_photos' => $this->getAgentPhotos()->toArray(),
            'contact_block' => $this->getContactBlock()->toArray(),
            'main_agent_photo' => optional($this->getMainAgentPhoto())->toArray(),
            'office_logo' => optional($this->getOfficeLogo())->toArray(),
            'office_display_options' => $this->getOfficeDisplayOptions()->toArray(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}