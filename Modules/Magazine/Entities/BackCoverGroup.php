<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class BackCoverGroup implements Arrayable, Jsonable
{
    /** @var string */
    private $key;

    /** @var string */
    private $title;

    /** @var \Illuminate\Support\Collection */
    private $ads;

    public function __construct(
        string $key,
        string $title,
        iterable $ads
    ) {
        $this->key = $key;
        $this->title = $title;
        $this->ads = collect($ads);
    }

    public function getKey() : string
    {
        return $this->key;
    }

    public function getTitle() : string
    {
        return $this->title;
    }

    public function getAds() : Collection
    {
        return $this->ads;
    }

    public function toArray() : array
    {
        return [
            'key' => $this->getKey(),
            'title' => $this->getTitle(),
            'ads' => $this->getAds()->toArray(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}