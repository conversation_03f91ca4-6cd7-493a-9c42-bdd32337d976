<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class RecipientGroup implements Arrayable, Jsonable
{
    /** @var int */
    private $id;

    /** @var string */
    private $name;

    /** @var bool */
    private $isPrinting;

    /** @var bool */
    private $isNonPrinting;

    /** @var int */
    private $recipientCount;

    /** @var int */
    private $printableRecipientCount;

    public function __construct(
        int $id,
        string $name,
        bool $isPrinting,
        bool $isNonPrinting,
        int $recipientCount,
        int $printableRecipientCount
    ) {
        $this->id = $id;
        $this->name = $name;
        $this->isPrinting = $isPrinting;
        $this->isNonPrinting = $isNonPrinting;
        $this->recipientCount = $recipientCount;
        $this->printableRecipientCount = $printableRecipientCount;
    }

    public function getId() : int
    {
        return $this->id;
    }

    public function getName() : string
    {
        return $this->name;
    }

    public function isPrinting() : bool
    {
        return $this->isPrinting;
    }

    public function isNonPrinting() : bool
    {
        return $this->isNonPrinting;
    }

    public function getRecipientCount() : int
    {
        return $this->recipientCount;
    }

    public function getPrintableRecipientCount() : int
    {
        return $this->printableRecipientCount;
    }

    public function toArray() : array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'is_printing' => $this->isPrinting(),
            'is_non_printing' => $this->isNonPrinting(),
            'recipient_count' => $this->getRecipientCount(),
            'printable_recipient_count' => $this->getPrintableRecipientCount(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}