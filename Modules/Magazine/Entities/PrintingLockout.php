<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class PrintingLockout implements Arrayable, Jsonable
{
    /** @var string */
    private $endDate;

    /** @var bool */
    private $locked;

    /** @var bool */
    private $override;

    public function __construct(bool $locked, string $endDate, bool $override)
    {
        $this->endDate = $endDate;
        $this->locked = $locked;
        $this->override = $override;
    }

    public function getEndDate() : string
    {
        return $this->endDate;
    }

    public function isLocked() : bool
    {
        return $this->locked;
    }

    public function isOverride() : bool
    {
        return $this->override;
    }

    public function toArray() : array
    {
        return [
            'end_date' => $this->getEndDate(),
            'locked' => $this->isLocked(),
            'override' => $this->isOverride(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}