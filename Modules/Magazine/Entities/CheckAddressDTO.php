<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class CheckAddressDTO implements Arrayable, Jsonable
{
    /** @var string */
    private $status;

    /** @var \Illuminate\Support\Collection */
    private $errors;

    /** @var string */
    private $uspsQueryString;

    /** @var string */
    private $googleQueryString;

    /** @var array */
    private $validatedAddress;

    /** @var array */
    private $previousAddress;

    public function __construct(
        string $status,
        iterable $errors,
        string $uspsQueryString,
        string $googleQueryString,
        array $validatedAddress,
        array $previousAddress
    ) {
        $this->status = $status;
        $this->errors = collect($errors);
        $this->uspsQueryString = $uspsQueryString;
        $this->googleQueryString = $googleQueryString;
        $this->validatedAddress = $validatedAddress;
        $this->previousAddress = $previousAddress;
    }

    public function getStatus() : string
    {
        return $this->status;
    }

    public function getErrors() : Collection
    {
        return $this->errors;
    }

    public function getUspsQueryString() : string
    {
        return $this->uspsQueryString;
    }

    public function getGoogleQueryString() : string
    {
        return $this->googleQueryString;
    }

    public function getValidatedAddress() : array
    {
        return $this->validatedAddress;
    }

    public function getPreviousAddress() : array
    {
        return $this->previousAddress;
    }

    public function containsCorrection() : bool
    {
        return $this->getErrors()->contains(function ($error) {
            return array_get($error, 'isCorrection', false);
        });
    }

    public function toArray() : array
    {
        return [
            'status' => $this->getStatus(),
            'errors' => $this->getErrors()->toArray(),
            'usps_query_string' => $this->getUspsQueryString(),
            'google_query_string' => $this->getGoogleQueryString(),
            'validated_address' => $this->getValidatedAddress(),
            'previous_address' => $this->getPreviousAddress(),
            'contains_correction' => $this->containsCorrection(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}