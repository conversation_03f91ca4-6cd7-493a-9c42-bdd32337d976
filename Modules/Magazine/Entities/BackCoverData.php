<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;
use Modules\Xrms\Entities\AgentPhoto;

class BackCoverData implements Arrayable, Jsonable
{
    /** @var string */
    private $selectedCoverId;

    /** @var \Illuminate\Support\Collection */
    private $coverGroups;

    /** @var \Modules\Magazine\Entities\BackCoverGroup */
    private $specialEditionCoverGroup;

    /** @var \Illuminate\Support\Collection */
    private $coverPhotos;

    public function __construct(
        ?string $selectedCoverId,
        iterable $coverGroups,
        BackCoverGroup $specialEditionCoverGroup,
        iterable $coverPhotos
    ) {
        $this->selectedCoverId = $selectedCoverId;
        $this->coverGroups = collect($coverGroups);
        $this->specialEditionCoverGroup = $specialEditionCoverGroup;
        $this->coverPhotos = collect($coverPhotos);
    }

    public function getCoverGroups() : Collection
    {
        return $this->coverGroups;
    }

    public function getCoverPhotos() : Collection
    {
        return $this->coverPhotos;
    }

    public function getSelectedCoverId() : ?string
    {
        return $this->selectedCoverId;
    }

    public function getSelectedCoverPhoto() : ?AgentPhoto
    {
        return $this->coverPhotos->first(function (AgentPhoto $photo) {
            return $photo->isSelected();
        });
    }

    public function getSpecialEditionCoverGroup() : BackCoverGroup
    {
        return $this->specialEditionCoverGroup;
    }

    public function toArray() : array
    {
        return [
            'cover_groups' => $this->getCoverGroups()->toArray(),
            'cover_photos' => $this->getCoverPhotos()->toArray(),
            'selected_cover_id' => $this->getSelectedCoverId(),
            'selected_cover_photo' => $this->getSelectedCoverPhoto(),
            'special_edition_cover_group' => $this->getSpecialEditionCoverGroup()->toArray(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}