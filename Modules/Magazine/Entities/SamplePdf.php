<?php

namespace Modules\Magazine\Entities;

use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;

class SamplePdf
{
    /** @var int */
    private $issueId;

    /** @var string */
    private $path;

    public function __construct(int $issueId, string $path)
    {
        $this->issueId = $issueId;
        $this->path = $path;
    }

    public function getIssueId() : int
    {
        return $this->issueId;
    }

    public function getPath() : string
    {
        return $this->path;
    }

    public function download()
    {
        return response()->download(Storage::disk('uploaded_files')->path($this->getPath()));
    }

    public function streamDownload()
    {
        return response()->streamDownload(function () {
            echo Storage::disk('uploaded_files')->get($this->getPath());
        });
    }
}