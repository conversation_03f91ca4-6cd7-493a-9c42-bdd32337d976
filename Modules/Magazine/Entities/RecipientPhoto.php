<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class RecipientPhoto implements Arrayable, Jsonable
{
    /** @var int|null */
    private $id;

    /** @var string */
    private $status;

    /** @var string|null */
    private $imageUrl;

    /** @var string|null */
    private $thumbnailUrl;

    public function __construct(
        string $status,
        ?int $id = null,
        ?string $imageUrl = null,
        ?string $thumbnailUrl = null
    ) {
        $this->id = $id;
        $this->imageUrl = $imageUrl;
        $this->thumbnailUrl = $thumbnailUrl;
        $this->status = $status;
    }

    public function getId() : ?int
    {
        return $this->id;
    }

    public function setId(?int $id)
    {
        $this->id = $id;
    }

    public function getStatus() : string
    {
        return $this->status;
    }

    public function setStatus(string $status)
    {
        $this->status = $status;
    }

    public function getImageUrl() : ?string
    {
        return $this->imageUrl;
    }

    public function setImageUrl(?string $imageUrl)
    {
        $this->imageUrl = $imageUrl;
    }

    public function getThumbnailUrl() : ?string
    {
        return $this->thumbnailUrl;
    }

    public function setThumbnailUrl(?string $thumbnailUrl)
    {
        $this->thumbnailUrl = $thumbnailUrl;
    }

    public function toArray() : array
    {
        return [
            'id' => $this->getId(),
            'status' => $this->getStatus(),
            'image_url' => $this->getImageUrl(),
            'thumbnail_url' => $this->getThumbnailUrl(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}