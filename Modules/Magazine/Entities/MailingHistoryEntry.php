<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class MailingHistoryEntry implements Arrayable, Jsonable
{
    /** @var string */
    private $date;

    /** @var string */
    private $deliveryDateStart;

    /** @var string */
    private $deliveryDateEnd;

    /** @var int */
    private $quantity;

    /** @var int */
    private $promotionalCopiesCount;

    /** @var string */
    private $promotionalCopiesAddress;

    /** @var string|null */
    private $backInsideCoverThumbnail;

    /** @var string|null */
    private $backInsideCoverImage;

    /** @var string|null */
    private $backCoverThumbnail;

    /** @var string|null */
    private $backCoverImage;

    /** @var string|null */
    private $firstTearOutCardThumbnail;

    /** @var string|null */
    private $firstTearOutCardImage;

    /** @var string|null */
    private $secondTearOutCardThumbnail;

    /** @var string|null */
    private $secondTearOutCardImage;

    /** @var string|null */
    private $pdfPath;

    /** @var int */
    private $issueId;

    /** @var int */
    private $printAgentHistoryId;

    /** @var \Illuminate\Support\Collection */
    private $subscribers;

    public function getDate() : string
    {
        return $this->date;
    }

    public function setDate(string $date)
    {
        $this->date = $date;
    }

    public function getDeliveryDateStart() : ?string
    {
        return $this->deliveryDateStart;
    }

    public function setDeliveryDateStart(?string $deliveryDateStart)
    {
        $this->deliveryDateStart = $deliveryDateStart;
    }

    public function getDeliveryDateEnd() : ?string
    {
        return $this->deliveryDateEnd;
    }

    public function setDeliveryDateEnd(?string $deliveryDateEnd)
    {
        $this->deliveryDateEnd = $deliveryDateEnd;
    }

    public function getQuantity() : ?int
    {
        return $this->quantity;
    }

    public function setQuantity(?int $quantity)
    {
        $this->quantity = $quantity;
    }

    public function getPromotionalCopiesCount() : ?int
    {
        return $this->promotionalCopiesCount;
    }

    public function setPromotionalCopiesCount(?int $promotionalCopiesCount)
    {
        $this->promotionalCopiesCount = $promotionalCopiesCount;
    }

    public function getPromotionalCopiesAddress() : ?string
    {
        return $this->promotionalCopiesAddress;
    }

    public function setPromotionalCopiesAddress(?string $promotionalCopiesAddress)
    {
        $this->promotionalCopiesAddress = $promotionalCopiesAddress;
    }

    public function getBackInsideCoverThumbnail() : ?string
    {
        return $this->backInsideCoverThumbnail;
    }

    public function setBackInsideCoverThumbnail(?string $backInsideCoverThumbnail)
    {
        $this->backInsideCoverThumbnail = $backInsideCoverThumbnail;
    }

    public function getBackInsideCoverImage() : ?string
    {
        return $this->backInsideCoverImage;
    }

    public function setBackInsideCoverImage(?string $backInsideCoverImage)
    {
        $this->backInsideCoverImage = $backInsideCoverImage;
    }

    public function getBackCoverThumbnail() : ?string
    {
        return $this->backCoverThumbnail;
    }

    public function setBackCoverThumbnail(?string $backCoverThumbnail)
    {
        $this->backCoverThumbnail = $backCoverThumbnail;
    }

    public function getBackCoverImage() : ?string
    {
        return $this->backCoverImage;
    }

    public function setBackCoverImage(?string $backCoverImage)
    {
        $this->backCoverImage = $backCoverImage;
    }

    public function getFirstTearOutCardThumbnail() : ?string
    {
        return $this->firstTearOutCardThumbnail;
    }

    public function setFirstTearOutCardThumbnail(?string $firstTearOutCardThumbnail)
    {
        $this->firstTearOutCardThumbnail = $firstTearOutCardThumbnail;
    }

    public function getFirstTearOutCardImage() : ?string
    {
        return $this->firstTearOutCardImage;
    }

    public function setFirstTearOutCardImage(?string $firstTearOutCardImage)
    {
        $this->firstTearOutCardImage = $firstTearOutCardImage;
    }

    public function getSecondTearOutCardThumbnail() : ?string
    {
        return $this->secondTearOutCardThumbnail;
    }

    public function setSecondTearOutCardThumbnail(?string $secondTearOutCardThumbnail)
    {
        $this->secondTearOutCardThumbnail = $secondTearOutCardThumbnail;
    }

    public function getSecondTearOutCardImage() : ?string
    {
        return $this->secondTearOutCardImage;
    }

    public function setSecondTearOutCardImage(?string $secondTearOutCardImage)
    {
        $this->secondTearOutCardImage = $secondTearOutCardImage;
    }

    public function getPdfPath() : ?string
    {
        return $this->pdfPath;
    }

    public function setPdfPath(?string $pdfPath)
    {
        $this->pdfPath = $pdfPath;
    }

    public function getIssueId() : int
    {
        return $this->issueId;
    }

    public function setIssueId(int $issueId)
    {
        $this->issueId = $issueId;
    }

    public function getPrintAgentHistoryId() : ?int
    {
        return $this->printAgentHistoryId;
    }

    public function setPrintAgentHistoryId(?int $printAgentHistoryId)
    {
        $this->printAgentHistoryId = $printAgentHistoryId;
    }

    public function getSubscribers(): Collection
    {
        return $this->subscribers;
    }

    public function setSubscribers(iterable $subscribers)
    {
        $this->subscribers = collect($subscribers);
    }

    public function toArray() : array
    {
        return [
            'date' => $this->getDate(),
            'delivery_date_start' => $this->getDeliveryDateStart(),
            'delivery_date_end' => $this->getDeliveryDateEnd(),
            'quantity' => $this->getQuantity(),
            'promotional_copies_count' => $this->getPromotionalCopiesCount(),
            'promotional_copies_address' => $this->getPromotionalCopiesAddress(),
            'back_inside_cover_thumbnail' => $this->getBackInsideCoverThumbnail(),
            'back_inside_cover_image' => $this->getBackInsideCoverImage(),
            'back_cover_thumbnail' => $this->getBackCoverThumbnail(),
            'back_cover_image' => $this->getBackCoverImage(),
            'first_tear_out_card_thumbnail' => $this->getFirstTearOutCardThumbnail(),
            'first_tear_out_card_image' => $this->getFirstTearOutCardImage(),
            'second_tear_out_card_thumbnail' => $this->getSecondTearOutCardThumbnail(),
            'second_tear_out_card_image' => $this->getSecondTearOutCardImage(),
            'pdf_path' => $this->getPdfPath(),
            'issue_id' => $this->getIssueId(),
            'print_agent_history_id' => $this->getPrintAgentHistoryId(),
            'subscribers' => $this->getSubscribers()->toArray(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}