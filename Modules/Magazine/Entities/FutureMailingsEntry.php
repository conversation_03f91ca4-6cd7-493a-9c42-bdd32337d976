<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class FutureMailingsEntry implements Arrayable, Jsonable
{
    /** @var int */
    private $issueId;

    /** @var string */
    private $issueName;

    /** @var string */
    private $deadlineDate;

    /** @var int|null */
    private $deadlineDays;

    /** @var string */
    private $startDate;

    /** @var string */
    private $endDate;

    public function __construct(
        int $issueId,
        string $issueName,
        string $deadlineDate,
        ?int $deadlineDays,
        string $startDate,
        string $endDate
    ) {
        $this->issueId = $issueId;
        $this->issueName = $issueName;
        $this->deadlineDate = $deadlineDate;
        $this->deadlineDays = $deadlineDays;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    public function getIssueId() : int
    {
        return $this->issueId;
    }

    public function getIssueName() : string
    {
        return $this->issueName;
    }

    public function setIssueId(int $id)
    {
        $this->issueId = $id;
    }

    public function setIssueName(string $issueName)
    {
       $this->issueName = $issueName;
    }

    public function getDeadlineDate() : string
    {
        return $this->deadlineDate;
    }

    public function getDeadlineDays() : ?int
    {
        return $this->deadlineDays;
    }

    public function getStartDate() : string
    {
        return $this->startDate;
    }

    public function getEndDate() : string
    {
        return $this->endDate;
    }

    public function toArray() : array
    {
        return [
            'issue_id' => $this->getIssueId(),
            'issue_name' => $this->getIssueName(),
            'deadline_date' => $this->getDeadlineDate(),
            'deadline_days' => $this->getDeadlineDays(),
            'start_date' => $this->getStartDate(),
            'end_date' => $this->getEndDate(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}