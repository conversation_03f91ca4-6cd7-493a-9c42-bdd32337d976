<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class PromotionalCopyOrder implements Arrayable, Jsonable
{
    /** @var int */
    private $id;

    /** @var int */
    private $quantity;

    /** @var string */
    private $address1;

    /** @var string */
    private $address2;

    /** @var string|null */
    private $city;

    /** @var string|null */
    private $state;

    /** @var string|null */
    private $zip;

    /** @var string */
    private $run;

    /** @var string */
    private $greeting;

    public function __construct(
        int $id,
        int $quantity,
        string $address1,
        string $address2,
        ?string $city,
        ?string $state,
        ?string $zip,
        string $run,
        string $greeting
    ) {
        $this->id = $id;
        $this->quantity = $quantity;
        $this->address1 = $address1;
        $this->address2 = $address2;
        $this->city = $city;
        $this->state = $state;
        $this->zip = $zip;
        $this->run = $run;
        $this->greeting = $greeting;
    }

    public function getId() : int
    {
        return $this->id;
    }

    public function getQuantity() : int
    {
        return $this->quantity;
    }

    public function getAddress1() : string
    {
        return $this->address1;
    }

    public function getAddress2() : string
    {
        return $this->address2;
    }

    public function getCity() : ?string
    {
        return $this->city;
    }

    public function getState() : ?string
    {
        return $this->state;
    }

    public function getZip() : ?string
    {
        return $this->zip;
    }

    public function getRun() : string
    {
        return $this->run;
    }

    public function getGreeting() : string
    {
        return $this->greeting;
    }

    public function toArray() : array
    {
        return [
          'id' => $this->getId(),
          'quantity' => $this->getQuantity(),
          'address1' => $this->getAddress1(),
          'address2' => $this->getAddress2(),
          'city' => $this->getCity(),
          'state' => $this->getState(),
          'zip' => $this->getZip(),
          'run' => $this->getRun(),
          'greeting' => $this->getGreeting()

        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}