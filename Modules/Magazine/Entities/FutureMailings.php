<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class FutureMailings implements Arrayable, Jsonable
{
    /** @var \Illuminate\Support\Collection */
    private $mailings;

    public function __construct(iterable $mailings)
    {
        $this->mailings = collect($mailings);
    }

    public function getCurrentMailing() : ?FutureMailingsEntry
    {
        return $this->mailings->first();
    }

    public function getMailings() : Collection
    {
        return $this->mailings;
    }

    public function toArray() : array
    {
        return [
            'current_mailing' => $this->getCurrentMailing()->toArray(),
            'mailings' => $this->getMailings()->toArray(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}