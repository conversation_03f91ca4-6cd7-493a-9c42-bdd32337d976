<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class LetterSignatures implements Arrayable, Jsonable
{
    /** @var string */
    private $signatureText1;

    /** @var string */
    private $signatureText2;

    /** @var string */
    private $defaultSignature;

    /** @var string */
    private $signatureType;

    public function __construct(
        string $signatureText1,
        string $signatureText2,
        string $defaultSignature,
        string $signatureType
    ) {
        $this->signatureText1 = $signatureText1;
        $this->signatureText2 = $signatureText2;
        $this->defaultSignature = $defaultSignature;
        $this->signatureType = $signatureType;
    }

    public function getSignatureText1(): string
    {
        return $this->signatureText1;
    }

    public function getSignatureText2(): string
    {
        return $this->signatureText2;
    }

    public function getSignatureText3(): string
    {
        return $this->signatureText3;
    }

    public function getDefaultSignature(): string
    {
        return $this->defaultSignature;
    }

    public function getSignatureType(): string
    {
        return $this->signatureType;
    }

    public function toArray() : array
    {
        return [
            'signatureText1' => $this->getSignatureText1(),
            'signatureText2' => $this->getSignatureText2(),
            'defaultSignature' => $this->getDefaultSignature(),
            'signatureType' => $this->getSignatureType(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}
