<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class ContactBlockEditData implements Arrayable, Jsonable
{
    /** @var string */
    private $displayName;

    /** @var string */
    private $licenseNumber;

    /** @var array */
    private $websites;

    /** @var array */
    private $titles;

    /** @var array */
    private $designations;

    /** @var array */
    private $contactInfo;

    /** @var array */
    private $officeOptions;

    /** @var array */
    private $alternateOffice;

    /** @var string */
    private $alternateOfficeName;

    public function __construct(
        ?string $displayName,
        string $licenseNumber,
        string $alternateOfficeName,
        array $websites,
        array $titles,
        array $designations,
        array $contactInfo,
        array $officeOptions,
        array $alternateOffice

    ) {
        $this->displayName = $displayName;
        $this->licenseNumber = $licenseNumber;
        $this->alternateOfficeName = $alternateOfficeName;
        $this->websites = $websites;
        $this->titles = $titles;
        $this->designations = $designations;
        $this->contactInfo = $contactInfo;
        $this->officeOptions = $officeOptions;
        $this->alternateOffice = $alternateOffice;
    }

    public function getDisplayName() : ?string
    {
        return $this->displayName;
    }

    public function setDisplayName(string $displayName) : void
    {
        $this->displayName = $displayName;
    }

    public function getLicenseNumber() : string
    {
        return $this->licenseNumber;
    }

    public function setLicenseNumber(string $licenseNumber) : void
    {
        $this->licenseNumber = $licenseNumber;
    }

    public function getWebsites() : array
    {
        return $this->websites;
    }

    public function setWebsites(array $websites) : void
    {
        $this->websites = $websites;
    }

    public function getTitles() : array
    {
        return $this->titles;
    }

    public function setTitles(array $titles) : void
    {
        $this->titles = $titles;
    }

    public function getDesignations() : array
    {
        return $this->designations;
    }

    public function setDesignations(array $designations) : void
    {
        $this->designations = $designations;
    }

    public function getContactInfo() : array
    {
        return $this->contactInfo;
    }

    public function setContactInfo(array $contactInfo) : void
    {
        $this->contactInfo = $contactInfo;
    }

    public function getOfficeOptions() : array
    {
        return $this->officeOptions;
    }

    public function setOfficeOptions(array $officeOptions) : void
    {
        $this->officeOptions = $officeOptions;
    }

    public function getAlternateOffice() : array
    {
        return $this->alternateOffice;
    }

    public function setAlternateOffice(array $alternateOffice) : void
    {
        $this->alternateOffice = $alternateOffice;
    }

    public function getAlternateOfficeName() : string
    {
        return $this->alternateOfficeName;
    }

    public function setAlternateOfficeName(string $alternateOfficeName) : void
    {
        $this->alternateOfficeName = $alternateOfficeName;
    }

    public function toArray() : array
    {
        return [
            'displayName'         => $this->getDisplayName(),
            'licenseNumber'       => $this->getLicenseNumber(),
            'alternateOfficeName' => $this->getAlternateOfficeName(),
            'websites'            => $this->getWebsites(),
            'titles'              => $this->getTitles(),
            'designations'        => $this->getDesignations(),
            'contactInfo'         => $this->getContactInfo(),
            'officeOptions'       => $this->getOfficeOptions(),
            'alternateOffice'     => $this->getAlternateOffice(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}
