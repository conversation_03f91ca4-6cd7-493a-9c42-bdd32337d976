<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class BackCoverPreview implements Arrayable, Jsonable
{
    /** @var string */
    private $id;

    /** @var string|null */
    private $title;

    /** @var string|null */
    private $largeUrl;

    /** @var string|null */
    private $xLargeUrl;

    /** @var bool */
    private $isCustom;

    /** @var \Illuminate\Support\Collection */
    private $tags;

    public function __construct(
        string $id,
        ?string $title,
        ?string $largeUrl,
        ?string $xLargeUrl,
        bool $isCustom,
        iterable $tags = []
    ) {
        $this->id = $id;
        $this->title = $title;
        $this->largeUrl = $largeUrl;
        $this->xLargeUrl = $xLargeUrl;
        $this->isCustom = $isCustom;
        $this->tags = collect($tags);
    }

    public function getId() : string
    {
        return $this->id;
    }

    public function getTitle() : ?string
    {
        return $this->title;
    }

    public function getLargeUrl() : ?string
    {
        return $this->largeUrl;
    }

    public function getXLargeUrl() : ?string
    {
        return $this->xLargeUrl;
    }

    public function isCustom() : bool
    {
        return $this->isCustom;
    }

    public function getAdType() : string
    {
        return $this->isCustom ? 'static' : 'variable';
    }

    public function getTags() : Collection
    {
        return $this->tags;
    }

    public function toArray() : array
    {
        return [
            'id' => $this->getId(),
            'title' => $this->getTitle(),
            'large_url' => $this->getLargeUrl(),
            'x_large_url' => $this->getXLargeUrl(),
            'is_custom' => $this->isCustom(),
            'ad_type' => $this->getAdType(),
            'tags' => $this->getTags()->toArray(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}