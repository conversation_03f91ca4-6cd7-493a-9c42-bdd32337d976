<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class TearOutCardPreview implements Arrayable, Jsonable
{
    /** @var string */
    private $id;

    /** @var string */
    private $description;

    /** @var string */
    private $backImage;

    /** @var string */
    private $frontImage;

    /** @var bool */
    private $isCustom;

    public function __construct(
        string $id,
        string $description,
        string $backImage,
        string $frontImage,
        bool $isCustom
    ) {
        $this->id = $id;
        $this->description = $description;
        $this->backImage = $backImage;
        $this->frontImage = $frontImage;
        $this->isCustom = $isCustom;
    }

    public function getId() : string
    {
        return $this->id;
    }

    public function getDescription() : string
    {
        return $this->description;
    }

    public function getBackImage() : string
    {
        return $this->backImage;
    }

    public function getFrontImage() : string
    {
        return $this->frontImage;
    }

    public function isCustom() : bool
    {
        return $this->isCustom;
    }

    public function getAdType() : string
    {
        return $this->isCustom ? 'static' : 'variable';
    }

    public function toArray() : array
    {
        return [
            'id' => $this->getId(),
            'description' => $this->getDescription(),
            'back_image' => $this->getBackImage(),
            'front_image' => $this->getFrontImage(),
            'is_custom' => $this->isCustom(),
            'ad_type' => $this->getAdType(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}