<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class SelectedTearOutCards implements Arrayable, Jsonable
{
    /** @var string */
    private $firstTocId;

    /** @var string */
    private $secondTocId;

    /** @var string|null */
    private $defautlFirstTocId;

    /** @var string|null */
    private $defaultSecondTocId;

    public function __construct(
        string $firstTocId,
        string $secondTocId,
        ?string $defautlFirstTocId,
        ?string $defaultSecondTocId
    ) {
        $this->firstTocId = $firstTocId;
        $this->secondTocId = $secondTocId;
        $this->defautlFirstTocId = $defautlFirstTocId;
        $this->defaultSecondTocId = $defaultSecondTocId;
    }

    public function getFirstTocId() : string
    {
        return $this->firstTocId;
    }

    public function getSecondTocId() : string
    {
        return $this->secondTocId;
    }

    public function getDefaultFirstTocId() : ?string
    {
        return $this->defautlFirstTocId;
    }

    public function getDefaultSecondTocId() : ?string
    {
        return $this->defaultSecondTocId;
    }

    public function toArray() : array
    {
        return [
            'first_toc_id' => $this->getFirstTocId(),
            'second_toc_id' => $this->getSecondTocId(),
            'default_first_toc_id' => $this->getDefaultFirstTocId(),
            'default_second_toc_id' => $this->getDefaultSecondTocId(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}