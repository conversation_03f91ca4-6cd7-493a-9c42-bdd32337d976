<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class BackCoverMetadata implements Arrayable, Jsonable
{
    /** @var string */
    private $name;

    /** @var array */
    private $images;

    public function __construct(string $name)
    {
        $this->name = $name;
    }

    public function getAbbr() : string
    {
        return strInitials($this->name);
    }

    public function getAbbrLowercase() : string
    {
        return strtolower($this->getAbbr());
    }

    public function getFieldName() : string
    {
        $str = ucfirst($this->getAbbrLowercase());

        return "{$str}Field";
    }

    public function getKebabCase() : string
    {
        return kebab_case($this->name);
    }

    public function getPageName() : string
    {
        return strtoupper($this->getAbbr());
    }

    public function getPlural() : string
    {
        return str_plural($this->name);
    }

    public function getNames() : array
    {
        return [
            'kebab_case' => $this->getKebabCase(),
            'field_name' => $this->getFieldName(),
            'page_name' => $this->getPageName(),
            'plural' => $this->getPlural(),
            'title' => $this->name,
        ];
    }

    public function getImages() : array
    {
        return $this->images;
    }

    public function setImages(array $images)
    {
        $this->images = $images;
    }

    public function toArray() : array
    {
        return [
            'names' => $this->getNames(),
            'images' => $this->getImages(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}