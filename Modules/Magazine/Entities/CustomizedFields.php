<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class CustomizedFields implements Arrayable, Jsonable
{
    /** @var int */
    const MAX_CONTACT_INFO_FIELDS = 8;

    /** @var int */
    const MAX_DESIGNATION_FIELDS = 5;

    /** @var int */
    const MAX_TITLE_FIELDS = 7;

    /** @var int */
    const MAX_WEBSITE_FIELDS = 3;

    private $title1;
    private $title2;
    private $title3;
    private $title4;
    private $title5;
    private $title6;
    private $title7;
    private $contactInfo1;
    private $contactInfo2;
    private $contactInfo3;
    private $contactInfo4;
    private $contactInfo5;
    private $contactInfo6;
    private $contactInfo7;
    private $contactInfo8;
    private $website1;
    private $website2;
    private $website3;
    private $designation1;
    private $designation2;
    private $designation3;
    private $designation4;
    private $designation5;
    private $displayName;
    private $licenseNumber;
    private $officeAltName;
    private $officeAltAddressLine1;
    private $officeAltAddressLine2;
    private $officeAltAddressCityStateZip;
    private $bcField1;
    private $bcField2;
    private $bcField3;
    private $bcField4;
    private $bcField5;
    private $bcField6;
    private $bcPhoto;
    private $bicField1;
    private $bicField2;
    private $bicField3;
    private $bicField4;
    private $bicField5;
    private $bicField6;
    private $bicPhoto;
    private $frontCoverSelection;

    /**
     * @return mixed
     */
    public function getTitle1()
    {
        return $this->title1;
    }

    /**
     * @param mixed $title1
     */
    public function setTitle1($title1): void
    {
        $this->title1 = $title1;
    }

    /**
     * @return mixed
     */
    public function getTitle2()
    {
        return $this->title2;
    }

    /**
     * @param mixed $title2
     */
    public function setTitle2($title2): void
    {
        $this->title2 = $title2;
    }

    /**
     * @return mixed
     */
    public function getTitle3()
    {
        return $this->title3;
    }

    /**
     * @param mixed $title3
     */
    public function setTitle3($title3): void
    {
        $this->title3 = $title3;
    }

    /**
     * @return mixed
     */
    public function getTitle4()
    {
        return $this->title4;
    }

    /**
     * @param mixed $title4
     */
    public function setTitle4($title4): void
    {
        $this->title4 = $title4;
    }

    /**
     * @return mixed
     */
    public function getTitle5()
    {
        return $this->title5;
    }

    /**
     * @param mixed $title5
     */
    public function setTitle5($title5): void
    {
        $this->title5 = $title5;
    }

    /**
     * @return mixed
     */
    public function getTitle6()
    {
        return $this->title6;
    }

    /**
     * @param mixed $title6
     */
    public function setTitle6($title6): void
    {
        $this->title6 = $title6;
    }

    /**
     * @return mixed
     */
    public function getTitle7()
    {
        return $this->title7;
    }

    /**
     * @param mixed $title7
     */
    public function setTitle7($title7): void
    {
        $this->title7 = $title7;
    }

    /**
     * @return mixed
     */
    public function getContactInfo1()
    {
        return $this->contactInfo1;
    }

    /**
     * @param mixed $contactInfo1
     */
    public function setContactInfo1($contactInfo1): void
    {
        $this->contactInfo1 = $contactInfo1;
    }

    /**
     * @return mixed
     */
    public function getContactInfo2()
    {
        return $this->contactInfo2;
    }

    /**
     * @param mixed $contactInfo2
     */
    public function setContactInfo2($contactInfo2): void
    {
        $this->contactInfo2 = $contactInfo2;
    }

    /**
     * @return mixed
     */
    public function getContactInfo3()
    {
        return $this->contactInfo3;
    }

    /**
     * @param mixed $contactInfo3
     */
    public function setContactInfo3($contactInfo3): void
    {
        $this->contactInfo3 = $contactInfo3;
    }

    /**
     * @return mixed
     */
    public function getContactInfo4()
    {
        return $this->contactInfo4;
    }

    /**
     * @param mixed $contactInfo4
     */
    public function setContactInfo4($contactInfo4): void
    {
        $this->contactInfo4 = $contactInfo4;
    }

    /**
     * @return mixed
     */
    public function getContactInfo5()
    {
        return $this->contactInfo5;
    }

    /**
     * @param mixed $contactInfo5
     */
    public function setContactInfo5($contactInfo5): void
    {
        $this->contactInfo5 = $contactInfo5;
    }

    /**
     * @return mixed
     */
    public function getContactInfo6()
    {
        return $this->contactInfo6;
    }

    /**
     * @param mixed $contactInfo6
     */
    public function setContactInfo6($contactInfo6): void
    {
        $this->contactInfo6 = $contactInfo6;
    }

    /**
     * @return mixed
     */
    public function getContactInfo7()
    {
        return $this->contactInfo7;
    }

    /**
     * @param mixed $contactInfo7
     */
    public function setContactInfo7($contactInfo7): void
    {
        $this->contactInfo7 = $contactInfo7;
    }

    /**
     * @return mixed
     */
    public function getContactInfo8()
    {
        return $this->contactInfo8;
    }

    /**
     * @param mixed $contactInfo8
     */
    public function setContactInfo8($contactInfo8): void
    {
        $this->contactInfo8 = $contactInfo8;
    }

    /**
     * @return mixed
     */
    public function getWebsite1()
    {
        return $this->website1;
    }

    /**
     * @param mixed $website1
     */
    public function setWebsite1($website1): void
    {
        $this->website1 = $website1;
    }

    /**
     * @return mixed
     */
    public function getWebsite2()
    {
        return $this->website2;
    }

    /**
     * @param mixed $website2
     */
    public function setWebsite2($website2): void
    {
        $this->website2 = $website2;
    }

    /**
     * @return mixed
     */
    public function getWebsite3()
    {
        return $this->website3;
    }

    /**
     * @param mixed $website3
     */
    public function setWebsite3($website3): void
    {
        $this->website3 = $website3;
    }

    /**
     * @return mixed
     */
    public function getDesignation1()
    {
        return $this->designation1;
    }

    /**
     * @param mixed $designation1
     */
    public function setDesignation1($designation1): void
    {
        $this->designation1 = $designation1;
    }

    /**
     * @return mixed
     */
    public function getDesignation2()
    {
        return $this->designation2;
    }

    /**
     * @param mixed $designation2
     */
    public function setDesignation2($designation2): void
    {
        $this->designation2 = $designation2;
    }

    /**
     * @return mixed
     */
    public function getDesignation3()
    {
        return $this->designation3;
    }

    /**
     * @param mixed $designation3
     */
    public function setDesignation3($designation3): void
    {
        $this->designation3 = $designation3;
    }

    /**
     * @return mixed
     */
    public function getDesignation4()
    {
        return $this->designation4;
    }

    /**
     * @param mixed $designation4
     */
    public function setDesignation4($designation4): void
    {
        $this->designation4 = $designation4;
    }

    /**
     * @return mixed
     */
    public function getDesignation5()
    {
        return $this->designation5;
    }

    /**
     * @param mixed $designation5
     */
    public function setDesignation5($designation5): void
    {
        $this->designation5 = $designation5;
    }

    /**
     * @return mixed
     */
    public function getDisplayName()
    {
        return $this->displayName;
    }

    /**
     * @param mixed $displayName
     */
    public function setDisplayName($displayName): void
    {
        $this->displayName = $displayName;
    }

    /**
     * @return mixed
     */
    public function getLicenseNumber()
    {
        return $this->licenseNumber;
    }

    /**
     * @param mixed $licenseNumber
     */
    public function setLicenseNumber($licenseNumber): void
    {
        $this->licenseNumber = $licenseNumber;
    }

    /**
     * @return mixed
     */
    public function getOfficeAltName()
    {
        return $this->officeAltName;
    }

    /**
     * @param mixed $officeAltName
     */
    public function setOfficeAltName($officeAltName): void
    {
        $this->officeAltName = $officeAltName;
    }

    /**
     * @return mixed
     */
    public function getOfficeAltAddressLine1()
    {
        return $this->officeAltAddressLine1;
    }

    /**
     * @param mixed $officeAltAddressLine1
     */
    public function setOfficeAltAddressLine1($officeAltAddressLine1): void
    {
        $this->officeAltAddressLine1 = $officeAltAddressLine1;
    }

    /**
     * @return mixed
     */
    public function getOfficeAltAddressLine2()
    {
        return $this->officeAltAddressLine2;
    }

    /**
     * @param mixed $officeAltAddressLine2
     */
    public function setOfficeAltAddressLine2($officeAltAddressLine2): void
    {
        $this->officeAltAddressLine2 = $officeAltAddressLine2;
    }

    /**
     * @return mixed
     */
    public function getOfficeAltAddressCityStateZip()
    {
        return $this->officeAltAddressCityStateZip;
    }

    /**
     * @param mixed $officeAltAddressCityStateZip
     */
    public function setOfficeAltAddressCityStateZip($officeAltAddressCityStateZip): void
    {
        $this->officeAltAddressCityStateZip = $officeAltAddressCityStateZip;
    }

    /**
     * @return mixed
     */
    public function getBcField1()
    {
        return $this->bcField1;
    }

    /**
     * @param mixed $bcField1
     */
    public function setBcField1($bcField1): void
    {
        $this->bcField1 = $bcField1;
    }

    /**
     * @return mixed
     */
    public function getBcField2()
    {
        return $this->bcField2;
    }

    /**
     * @param mixed $bcField2
     */
    public function setBcField2($bcField2): void
    {
        $this->bcField2 = $bcField2;
    }

    /**
     * @return mixed
     */
    public function getBcField3()
    {
        return $this->bcField3;
    }

    /**
     * @param mixed $bcField3
     */
    public function setBcField3($bcField3): void
    {
        $this->bcField3 = $bcField3;
    }

    /**
     * @return mixed
     */
    public function getBcField4()
    {
        return $this->bcField4;
    }

    /**
     * @param mixed $bcField4
     */
    public function setBcField4($bcField4): void
    {
        $this->bcField4 = $bcField4;
    }

    /**
     * @return mixed
     */
    public function getBcField5()
    {
        return $this->bcField5;
    }

    /**
     * @param mixed $bcField5
     */
    public function setBcField5($bcField5): void
    {
        $this->bcField5 = $bcField5;
    }

    /**
     * @return mixed
     */
    public function getBcField6()
    {
        return $this->bcField6;
    }

    /**
     * @param mixed $bcField6
     */
    public function setBcField6($bcField6): void
    {
        $this->bcField6 = $bcField6;
    }

    /**
     * @return mixed
     */
    public function getBcPhoto()
    {
        return $this->bcPhoto;
    }

    /**
     * @param mixed $bcPhoto
     */
    public function setBcPhoto($bcPhoto): void
    {
        $this->bcPhoto = $bcPhoto;
    }

    /**
     * @return mixed
     */
    public function getBicField1()
    {
        return $this->bicField1;
    }

    /**
     * @param mixed $bicField1
     */
    public function setBicField1($bicField1): void
    {
        $this->bicField1 = $bicField1;
    }

    /**
     * @return mixed
     */
    public function getBicField2()
    {
        return $this->bicField2;
    }

    /**
     * @param mixed $bicField2
     */
    public function setBicField2($bicField2): void
    {
        $this->bicField2 = $bicField2;
    }

    /**
     * @return mixed
     */
    public function getBicField3()
    {
        return $this->bicField3;
    }

    /**
     * @param mixed $bicField3
     */
    public function setBicField3($bicField3): void
    {
        $this->bicField3 = $bicField3;
    }

    /**
     * @return mixed
     */
    public function getBicField4()
    {
        return $this->bicField4;
    }

    /**
     * @param mixed $bicField4
     */
    public function setBicField4($bicField4): void
    {
        $this->bicField4 = $bicField4;
    }

    /**
     * @return mixed
     */
    public function getBicField5()
    {
        return $this->bicField5;
    }

    /**
     * @param mixed $bicField5
     */
    public function setBicField5($bicField5): void
    {
        $this->bicField5 = $bicField5;
    }

    /**
     * @return mixed
     */
    public function getBicField6()
    {
        return $this->bicField6;
    }

    /**
     * @param mixed $bicField6
     */
    public function setBicField6($bicField6): void
    {
        $this->bicField6 = $bicField6;
    }

    /**
     * @return mixed
     */
    public function getBicPhoto()
    {
        return $this->bicPhoto;
    }

    /**
     * @param mixed $bicPhoto
     */
    public function setBicPhoto($bicPhoto): void
    {
        $this->bicPhoto = $bicPhoto;
    }

    /**
     * @return mixed
     */
    public function getFrontCoverSelection()
    {
        return $this->frontCoverSelection;
    }

    /**
     * @param mixed $frontCoverSelection
     */
    public function setFrontCoverSelection($frontCoverSelection): void
    {
        $this->frontCoverSelection = $frontCoverSelection;
    }

    public function toArray() : array
    {
        return [
            'Title1' => $this->getTitle1(),
            'Title2' => $this->getTitle2(),
            'Title3' => $this->getTitle3(),
            'Title4' => $this->getTitle4(),
            'Title5' => $this->getTitle5(),
            'Title6' => $this->getTitle6(),
            'Title7' => $this->getTitle7(),
            'ContactInfo1' => $this->getContactInfo1(),
            'ContactInfo2' => $this->getContactInfo2(),
            'ContactInfo3' => $this->getContactInfo3(),
            'ContactInfo4' => $this->getContactInfo4(),
            'ContactInfo5' => $this->getContactInfo5(),
            'ContactInfo6' => $this->getContactInfo6(),
            'ContactInfo7' => $this->getContactInfo7(),
            'ContactInfo8' => $this->getContactInfo8(),
            'Website1' => $this->getWebsite1(),
            'Website2' => $this->getWebsite2(),
            'Website3' => $this->getWebsite3(),
            'Designation1' => $this->getDesignation1(),
            'Designation2' => $this->getDesignation2(),
            'Designation3' => $this->getDesignation3(),
            'Designation4' => $this->getDesignation4(),
            'Designation5' => $this->getDesignation5(),
            'ContactAltName' => $this->getDisplayName(),
            'LicenseNumber' => $this->getLicenseNumber(),
            'OfficeAltName' => $this->getOfficeAltName(),
            'OfficeAltAddressLine1' => $this->getOfficeAltAddressLine1(),
            'OfficeAltAddressLine2' => $this->getOfficeAltAddressLine2(),
            'OfficeAltAddressCityStateZip' => $this->getOfficeAltAddressCityStateZip(),
            'BcField1' => $this->getBcField1(),
            'BcField2' => $this->getBcField2(),
            'BcField3' => $this->getBcField3(),
            'BcField4' => $this->getBcField4(),
            'BcField5' => $this->getBcField5(),
            'BcField6' => $this->getBcField6(),
            'BcPhoto' => $this->getBcPhoto(),
            'BicField1' => $this->getBicField1(),
            'BicField2' => $this->getBicField2(),
            'BicField3' => $this->getBicField3(),
            'BicField4' => $this->getBicField4(),
            'BicField5' => $this->getBicField5(),
            'BicField6' => $this->getBicField6(),
            'BicPhoto' => $this->getBicPhoto(),
            'FrontCoverSelection' => $this->getFrontCoverSelection(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}