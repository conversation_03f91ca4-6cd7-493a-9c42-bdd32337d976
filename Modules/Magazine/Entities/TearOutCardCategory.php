<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class TearOutCardCategory implements Arrayable, Jsonable
{
    /** @var int */
    private $id;

    /** @var string */
    private $name;

    /** @var \Modules\Xrms\Entities\TearOutCardCategory[]|\Illuminate\Support\Collection */
    private $childCategories;

    /** @var \Illuminate\Support\Collection */
    private $ads;

    public function __construct(
        int $id,
        string $name,
        iterable $childCategories,
        iterable $ads
    ) {
        $this->id = $id;
        $this->name = $name;
        $this->childCategories = collect($childCategories);
        $this->ads = collect($ads);
    }

    public function getId() : int
    {
        return $this->id;
    }

    public function getName() : string
    {
        return $this->name;
    }

    public function getChildCategories() : Collection
    {
        return $this->childCategories;
    }

    public function getAds() : Collection
    {
        return $this->ads;
    }

    public function toArray() : array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'child_categories' => $this->getChildCategories()->toArray(),
            'ads' => $this->getAds(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}