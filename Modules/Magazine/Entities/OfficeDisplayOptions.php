<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class OfficeDisplayOptions implements Arrayable, Jsonable
{
    /** @var bool */
    private $showAddress;

    /** @var bool */
    private $showLogo;

    /** @var bool */
    private $showName;

    public function __construct(bool $showAddress, bool $showLogo, bool $showName)
    {
        $this->showAddress = $showAddress;
        $this->showLogo = $showLogo;
        $this->showName = $showName;
    }

    public function getShowAddress() : bool
    {
        return $this->showAddress;
    }

    public function getShowLogo() : bool
    {
        return $this->showLogo;
    }

    public function getShowName() : bool
    {
        return $this->showName;
    }

    public function toArray() : array
    {
        return [
            'show_address' => $this->getShowAddress(),
            'show_logo' => $this->getShowLogo(),
            'show_name' => $this->getShowName(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}