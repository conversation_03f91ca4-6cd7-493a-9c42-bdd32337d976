<?php

namespace Modules\Magazine\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class SaveRecipientDTO implements Arrayable, Jsonable
{
    /** @var array */
    private $address;

    /** @var int */
    private $groupId;

    /** @var iterable */
    private $additionalInfo;

    /** @var bool */
    private $forceSend;

    /** @var bool */
    private $saveWithErrors;

    /** @var string|null */
    private $lastName;

    /** @var string|null */
    private $letterSalutation;

    /** @var string|null */
    private $mailingSalutation;

    /**
     * @var int|null
     */
    private $contactId;

    public function __construct(
        array $address,
        int $groupId,
        iterable $additionalInfo,
        bool $forceSend,
        bool $saveWithErrors,
        ?string $lastName = null,
        ?string $letterSalutation = null,
        ?string $mailingSalutation = null,
        ?int $contactId = null
    ) {
        $this->address = $address;
        $this->groupId = $groupId;
        $this->additionalInfo = collect($additionalInfo);
        $this->forceSend = $forceSend;
        $this->saveWithErrors = $saveWithErrors;
        $this->lastName = $lastName;
        $this->letterSalutation = $letterSalutation;
        $this->mailingSalutation = $mailingSalutation;
        $this->contactId = $contactId;
    }

    public function getAddress() : array
    {
        return $this->address;
    }

    public function getGroupId() : int
    {
        return $this->groupId;
    }

    public function getAdditionalInfo() : Collection
    {
        return $this->additionalInfo;
    }

    public function isForceSend() : bool
    {
        return $this->forceSend;
    }

    public function isSaveWithErrors() : bool
    {
        return $this->saveWithErrors;
    }

    public function getLastName() : ?string
    {
        return $this->lastName;
    }

    public function getLetterSalutation() : ?string
    {
        return $this->letterSalutation;
    }

    public function getMailingSalutation() : ?string
    {
        return $this->mailingSalutation;
    }

    public function getContactId(): ?int
    {
        return $this->contactId;
    }

    public function toArray() : array
    {
        return [
            'address' => $this->getAddress(),
            'group_id' => $this->getGroupId(),
            'additional_info' => $this->getAdditionalInfo()->toArray(),
            'force_send' => $this->isForceSend(),
            'save_with_errors' => $this->isSaveWithErrors(),
            'last_name' => $this->getLastName(),
            'letter_salutation' => $this->getLetterSalutation(),
            'mailing_salutation' => $this->getMailingSalutation(),
            'contact_id' => $this->getContactId()
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}
