<?php

namespace Modules\Magazine\AggregateRoots;

use App\EventSourcing\AggregateRoot;
use Modules\Magazine\DTO\EmailProofDTO;
use Modules\Magazine\Events\CustomerRequestedProof;

class EmailProofAggregateRoot extends AggregateRoot
{
    public function customerRequestedProof(EmailProofDTO $emailProofDTO): self
    {
        $this->recordThat(CustomerRequestedProof::fromEmailProofDTO($emailProofDTO));

        return $this;
    }
}
