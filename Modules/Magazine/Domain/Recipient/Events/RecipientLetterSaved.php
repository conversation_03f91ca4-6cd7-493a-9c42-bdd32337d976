<?php
namespace Modules\Magazine\Domain\Recipient\Events;

use Illuminate\Support\Arr;
use Infrastructure\Events\AccountAwareEvent;
use Modules\Magazine\DTO\RecipientLetterDTO;
use ReminderMedia\Messaging\ShouldBroadcast;

class RecipientLetterSaved extends AccountAwareEvent implements ShouldBroadcast
{
    /** @var string */
    protected $globalName = 'recipients.recipient-letter-saved';

    /** @var mixed */
    public $recipientLetter;

    /** @var mixed */
    public $recipientGroupName;

    /** @var mixed */
    public $recipientGroupId;

    //recieve data from RabbitmQ
    protected function setData(array $data) : void
    {
        $this->recipientGroupId = Arr::get($data, 'recipientGroupId');
        $this->recipientGroupName = Arr::get($data, 'recipientGroupName');
        $this->recipientLetter = Arr::get($data, 'recipientLetter');
    }

    //send data to RabbitmQ
    protected function getData() : array
    {
        return [
            'recipientGroupId' => $this->recipientGroupId,
            'recipientGroupName' => $this->recipientGroupName,
            'recipientLetter' =>  $this->recipientLetter,
        ];
    }

    public static function fromRecipientLetterDTO(RecipientLetterDTO $dto) : self
    {
        $event = new self;

        $event->setData($dto->toArray());

        return $event;
    }
}

