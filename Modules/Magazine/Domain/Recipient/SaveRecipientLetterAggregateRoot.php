<?php

namespace Modules\Magazine\Domain\Recipient;

use App\EventSourcing\AggregateRoot;
use Modules\Magazine\DTO\RecipientLetterDTO;
use Modules\Magazine\Domain\Recipient\Events\RecipientLetterSaved;

final class SaveRecipientLetterAggregateRoot extends AggregateRoot
{
    public function recipientLetterSaved(int $id, string $groupName, string $letterContent) : AggregateRoot
    {
        $data = [
            'recipientLetter' => $letterContent,
            'recipientGroupName' => $groupName,
            'recipientGroupId' => $id,
        ];

        return $this->recordThat(RecipientLetterSaved::fromRecipientLetterDTO($dto = RecipientLetterDTO::fromArray($data)));
    }
}
