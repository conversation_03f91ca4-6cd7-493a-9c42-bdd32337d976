<?php

namespace Modules\Magazine\Domain\ContactBlock\Projectors;

use Domain\ContactBlock\Repositories\ContactBlockRepository;
use App\EventSourcing\Projectors\Projector;
use App\EventSourcing\Projectors\ProjectsEvents;
use App\Models\ContactBlock;
use Illuminate\Support\Arr;
use Modules\Magazine\Domain\ContactBlock\Events\ContactBlockCreated;
use Modules\Magazine\Domain\ContactBlock\Events\ContactBlockUpdated;

class ContactBlockProjector implements Projector
{
    use ProjectsEvents;

    /** @var \Domain\ContactBlock\Repositories\ContactBlockRepository */
    private $repository;

    public function __construct(ContactBlockRepository $repository)
    {
        $this->repository = $repository;
    }

    public function onContactBlockCreated(ContactBlockCreated $event) : void
    {
        $this->repository->saveContactBlock(
            ContactBlock::PRODUCT_MAGAZINE,
            collect($event->getItems())->map(function ($item) {
                return Arr::only($item, ['item_id', 'item_type', 'custom', 'section']);
            })->toArray()
        );
    }

    public function onContactBlockUpdated(ContactBlockUpdated $event) : void
    {
        $this->repository->saveContactBlock(
            ContactBlock::PRODUCT_MAGAZINE,
            collect($event->getItems())->map(function ($item) {
                return Arr::only($item, ['item_id', 'item_type', 'custom', 'section']);
            })->toArray()
        );
    }
}
