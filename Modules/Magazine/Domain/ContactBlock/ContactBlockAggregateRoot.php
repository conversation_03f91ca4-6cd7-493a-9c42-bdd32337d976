<?php

namespace Modules\Magazine\Domain\ContactBlock;

use Domain\ContactBlock\ContactBlockAggregateRoot as BaseAggregateRoot;
use App\Models\ContactBlock;
use App\Models\ContactBlockItem;
use Modules\Magazine\Domain\ContactBlock\Events\ContactBlockCreated;
use Modules\Magazine\Domain\ContactBlock\Events\ContactBlockUpdated;

final class ContactBlockAggregateRoot extends BaseAggregateRoot
{
    /** @var string */
    protected static $product = ContactBlock::PRODUCT_MAGAZINE;

    protected static $updateEvent = ContactBlockUpdated::class;

    public static function createInitialContactBlock() : ContactBlockAggregateRoot
    {
        /** @var \Modules\Magazine\Domain\ContactBlock\ContactBlockAggregateRoot $ar */
        $ar = app(self::class);

        $lines = [];

        // Business Rule - Default contact block for magazine contains:
        // - Display name
        // - Primary phone number
        // - Primary email address
        // - Primary location
        // - Show office logo

        //Display Name
        if ($ar->account->display_name) {
            $lines[] = $ar->makeItem(ContactBlockItem::ITEM_TYPE_DISPLAY_NAME, $ar->account->id, null,ContactBlockItem::ITEM_SECTION_DISPLAY_NAME);
        }

        //Primary Phone Number
        /** @var \App\Models\PhoneNumber|null $primaryPhoneNumber */
        $primaryPhoneNumber = $ar->account->phoneNumbers()->orderBy('is_primary', 'DESC')->first();

        if ($primaryPhoneNumber) {
            $lines[] = $ar->makeItem(ContactBlockItem::ITEM_TYPE_PHONE_NUMBER, $primaryPhoneNumber->id, null, ContactBlockItem::ITEM_SECTION_CONTACT_INFOS);
        }

        //Primary Email Address
        /** @var \App\Models\EmailAddress|null $primaryEmailAddress */
        $primaryEmailAddress = $ar->account->emailAddresses()->orderBy('is_primary', 'DESC')->first();

        if ($primaryEmailAddress) {
            $lines[] = $ar->makeItem(ContactBlockItem::ITEM_TYPE_EMAIL_ADDRESS, $primaryEmailAddress->id, null, ContactBlockItem::ITEM_SECTION_CONTACT_INFOS);
        }

        //Primary Location
        /** @var \App\Models\Location|null $location */
        $location = $ar->account->locations()->orderBy('is_primary', 'DESC')->first();

        if ($location) {
            $lines[] = $ar->makeItem(ContactBlockItem::ITEM_TYPE_LOCATION, $location->id, null, ContactBlockItem::ITEM_SECTION_LOCATION);
        }

        //Show Office Logo
        $lines[] = $ar->makeItem(
            ContactBlockItem::ITEM_TYPE_LOCATION,
            null,
            json_encode(['showOfficeLogo' => true]),
            ContactBlockItem::ITEM_SECTION_OFFICE_LOGO
        );

        //Save the contact block
        $ar->recordThat(new ContactBlockCreated($lines));

        return $ar;
    }

    public function update(array $items) : ContactBlockAggregateRoot
    {
        $this->recordThat(new ContactBlockUpdated($items));

        return $this;
    }
}
