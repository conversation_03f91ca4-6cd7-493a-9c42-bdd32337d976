<?php

namespace Modules\Magazine\Domain\ContactBlock\Actions;

use Domain\ContactBlock\Repositories\ContactBlockRepository;
use App\Models\ContactBlock;
use App\Models\ContactBlockItem;
use Modules\Magazine\Domain\ContactBlock\ContactBlockAggregateRoot;

class SaveContactBlock
{
    /** @var \Domain\ContactBlock\Repositories\ContactBlockRepository */
    private $repository;

    public function __construct(ContactBlockRepository $repository)
    {
        $this->repository = $repository;
    }

    public function execute(array $items) : ContactBlock
    {
        /** @var \Modules\Magazine\Domain\ContactBlock\ContactBlockAggregateRoot $ar */
        $ar = resolve(ContactBlockAggregateRoot::class);

        $ar->update($this->prepareItems($items))
            ->persist();

        return $this->repository->findByProduct(ContactBlock::PRODUCT_MAGAZINE);
    }

    private function prepareItems(array $items) : array
    {
        return collect($items)->map(function ($item) {
            return ContactBlockItem::make($item)->toArray();
        })->toArray();
    }
}
