<?php

namespace Modules\Magazine\Domain\ContactBlock\Reactors;

use App\Events\AccountEnrolledInMagazine;
use App\EventSourcing\EventHandlers\EventHandler;
use App\EventSourcing\EventHandlers\HandlesEvents;
use App\Models\ContactBlock;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use Modules\Magazine\Domain\ContactBlock\ContactBlockAggregateRoot;

class CreateDefaultContactBlock implements EventHandler, ShouldQueue
{
    use HandlesEvents;

    public function onAccountEnrolledInMagazine(AccountEnrolledInMagazine $event)
    {
        $accountId = $event->getAccountId();

        if (ContactBlock::where('product', ContactBlock::PRODUCT_MAGAZINE)->exists()) {
            return;
        }

        /** @var \Domain\DigitalEdition\Aggregate\ContactBlockAggregateRoot $ar */
        $ar = ContactBlockAggregateRoot::createInitialContactBlock();

        $ar->persist();

        Log::debug("Default Contact Block created for {$accountId}.");
    }
}
