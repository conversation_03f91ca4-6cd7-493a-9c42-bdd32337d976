<?php

namespace Modules\Magazine\Domain\ContactBlock\Reactors;

use Domain\ContactBlock\Reactors\ContactBlockReactor as BaseReactor;
use App\Models\ContactBlock;
use Illuminate\Database\Eloquent\Collection;
use Modules\Magazine\Domain\ContactBlock\Events\ContactBlockUpdated;

class ContactBlockReactor extends BaseReactor
{
    public function getProductName() : String
    {
        return ContactBlock::PRODUCT_MAGAZINE;
    }

    public function getUpdateEventName() : String
    {
        return ContactBlockUpdated::class;
    }

    public function findContactBlockItems(string $className, int $itemId) : Collection
    {
        return $this->contactBlockRepository->findMagazineContactBlockItemsByClassNameAndItemId(
            $className,
            $itemId
        );
    }
}
