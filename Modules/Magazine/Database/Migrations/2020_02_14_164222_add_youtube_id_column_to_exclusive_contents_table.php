<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddYoutubeIdColumnToExclusiveContentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('exclusive_contents', function (Blueprint $table) {
            $table->string('youtube_id')
                ->nullable()
                ->default(null)
                ->after('banner_image_src');;
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('exclusive_contents', function (Blueprint $table) {
            $table->dropColumn('youtube_id');
        });
    }
}
