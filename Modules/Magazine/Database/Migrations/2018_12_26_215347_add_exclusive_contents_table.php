<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddExclusiveContentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('exclusive_contents', function (Blueprint $table) {
            $table->increments('id');

            $table->integer('issue_id');
            $table->integer('start_healthy_issue_id');

            $table->string('banner_image_src')->nullable();
            $table->string('carousel_img_src')->nullable();
            $table->string('fc_image_src');
            $table->string('bic_image_src');
            $table->string('bc_image_src');
            $table->string('toc_image_src');
            $table->string('social_shares_image_src');
            $table->string('issue_icon_image_src');

            $table->string('banner_target_url');

            $table->string('section_heading');
            $table->string('fc_heading');
            $table->string('design_options_heading');
            $table->string('social_shares_heading');

            $table->text('section_content');
            $table->text('design_options_content');
            $table->text('social_shares_content');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('exclusive_contents');
    }
}
