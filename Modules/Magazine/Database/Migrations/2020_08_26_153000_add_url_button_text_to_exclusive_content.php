<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddUrlButtonTextToExclusiveContent extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('exclusive_contents', function (Blueprint $table) {
            $table->string('social_shares_link')->after('social_shares_content');
            $table->string('social_shares_btn_text')->after('social_shares_link');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('exclusive_contents', function (Blueprint $table) {
            $table->dropColumn('social_shares_link');
            $table->dropColumn('social_shares_btn_text');
        });
    }
}
