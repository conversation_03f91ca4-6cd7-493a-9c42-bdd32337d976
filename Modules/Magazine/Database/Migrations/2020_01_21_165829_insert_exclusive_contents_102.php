<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

class InsertExclusiveContents102 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('exclusive_contents', 'carousel_img_src')) {
            return;
        }

        Schema::table('exclusive_contents', function (Blueprint $table) {
            $table->string('carousel_img_src')
                ->nullable()
                ->after('banner_image_src');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('exclusive_contents', function (Blueprint $table) {
            $table->dropColumn('carousel_img_src');
        });
    }
}
