<?php

namespace Modules\Magazine\Http\Requests\Api\LetterEditor;

use Illuminate\Foundation\Http\FormRequest;

class UpdateRecipientLetterRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'letterContent' => ['string', 'nullable', 'min:0', 'max:2799'],
            'issueId' => ['required', 'int'],
            'recipientGroupIds' => ['present', 'array'],
            'signatureType' => ['required', 'string'],
            'signatureText1' => ['present', 'string', 'nullable'],
            'signatureText2' => ['present', 'string', 'nullable'],
            'defaultSignature' => ['present', 'string', 'nullable'],
            'addTrademark' => ['present', 'boolean']
        ];
    }

    public function getLetterContent() : string
    {
        $letterContent = $this->request->get('letterContent') ?? '';
        return trim($letterContent);
    }

    public function getIssueId() : int
    {
        return $this->request->get('issueId');
    }

    public function getRecipientGroupIds() : array
    {
        return $this->request->get('recipientGroupIds');
    }

    public function getSignatureType() : string
    {
        return $this->request->get('signatureType');
    }

    public function getSignatureText1() : string
    {
        return $this->request->get('signatureText1') ?? '';
    }

    public function getSignatureText2() : string
    {
        return $this->request->get('signatureText2') ?? '';
    }

    public function getDefaultSignature() : string
    {
        return $this->request->get('defaultSignature') ?? '';
    }

    public function getAddTrademark() : bool
    {
        return $this->request->get('addTrademark');
    }
}
