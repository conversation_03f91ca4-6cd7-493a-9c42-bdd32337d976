<?php

namespace Modules\Magazine\Http\Controllers\MailingHistory;

use App\Http\Controllers\Controller;
use Domain\Mailings\DTO\MagazineMailingListDTO;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Modules\Magazine\Repositories\MailingHistoryRepository;

class DownloadMailingListController extends Controller
{
    /** @var MailingHistoryRepository */
    private $mailingHistoryRepository;

    public function __construct(MailingHistoryRepository $mailingHistoryRepository)
    {
        $this->mailingHistoryRepository = $mailingHistoryRepository;
    }

    public function __invoke(Request $request)
    {
        $fileName = $this->getMagazineMailingListFileName($request->get('issueId'));

        $headers = [
            'Content-Encoding' => 'none',
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename='. $fileName,
            'Content-Description' => 'File Transfer',
        ];

        $column = [ 'Recipient', 'Street Address', 'City', 'State', 'Zip Code', 'Recipient Group' ];

        return response()->stream(function() use($request, $column){
            $file = fopen('php://output', 'w+');
            fputcsv($file, $column);

            $recipients = $this->getRecipientsToExport($request);

            foreach ($recipients as $recipient) {
                fputcsv($file, $recipient->toArray());
            }

            fclose($file);
        }, 200, $headers);
    }

    private function getRecipientsToExport(Request $request): array
    {
        $issueId = $request->get('issueId');
        $printAgentHistoryId = $request->get('printAgentHistoryId');

        $recipients = $this->mailingHistoryRepository->getMailingHistoryDetails($issueId, $printAgentHistoryId)->getSubscribers();

        $recipientsToExport = [];
        foreach ($recipients as $recipient) {
            $recipientsToExport[] = new MagazineMailingListDTO([
                'recipient'      => (Arr::get($recipient, 'name')),
                'streetAddress'  => (Arr::get($recipient, 'address')),
                'city'           => (Arr::get($recipient, 'city')),
                'state'          => (Arr::get($recipient, 'state')),
                'zipCode'        => (Arr::get($recipient, 'zip')),
                'recipientGroup' => (Arr::get($recipient, 'recipientGroupName'))
            ]);
        }

        return $recipientsToExport;
    }

    private function getMagazineMailingListFileName(int $issueId): string
    {
        return 'magazine-mailing-recipient-list'. '_' . $issueId . '.csv';
    }
}
