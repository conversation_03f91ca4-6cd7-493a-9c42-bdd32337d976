<?php

namespace Modules\Magazine\Http\Controllers\MailingHistory;

use Illuminate\Http\Request;
use Modules\Magazine\Contracts\SampleMagazineRepository;
use Modules\Magazine\Http\Controllers\Controller;

class DownloadSamplePdfController extends Controller
{
    public function __invoke(Request $request, SampleMagazineRepository $repository)
    {
        $sample = $repository->getForIssueId($request->route('issueId'));

        return $sample->download();
    }
}