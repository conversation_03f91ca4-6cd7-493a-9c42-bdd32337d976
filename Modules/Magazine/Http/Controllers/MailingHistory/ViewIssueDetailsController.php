<?php

namespace Modules\Magazine\Http\Controllers\MailingHistory;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Modules\Magazine\Http\Controllers\Controller;

class ViewIssueDetailsController extends Controller
{
    public function __invoke(Request $request): Response
    {
        return Inertia::render('Magazine/MailingHistory', [
            'issueId'             => (int) $request->route('issueId'),
            'printAgentHistoryId' => (int) $request->route('printAgentHistoryId'),
        ]);
    }
}
