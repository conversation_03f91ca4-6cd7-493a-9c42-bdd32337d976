<?php

namespace Modules\Magazine\Http\Controllers\MailingHistory;

use Modules\Magazine\Http\Controllers\Controller;
use Modules\Xrms\Contracts\AccountRepository;

class MailingHistoryController extends Controller
{
    /** @var AccountRepository */
    private $accountRepository;

    public function __construct(AccountRepository $accountRepository)
    {
        $this->accountRepository = $accountRepository;
    }

    public function index()
    {
        $accountHasBMPlan = $this->accountRepository->currentAccount()->hasBrandedMagazinePlan();

        return view('magazine::mailing-history.index', [
            'page_title' => $accountHasBMPlan ? 'Mailing History Archive' : 'Mailing History',
        ]);
    }
}
