<?php

namespace Modules\Magazine\Http\Controllers;

use Illuminate\Http\Request;
use App\Context\AccountId;
use Domain\Account\Models\AccountPlan;
use Modules\Magazine\Contracts\FrontCoverRepository;

class FrontCoverLargePreviewController extends Controller
{
    public function index(
        Request $request,
        FrontCoverRepository $frontCoverRepository
    ) {
        $coverId = (int) $request->route('coverId');

        $frontCover = $frontCoverRepository->getFrontCoverPreviewById($coverId);

        return view('magazine::front-cover-large-preview', [
            'contactId' => AccountId::current()->id(),
            'frontCover' => $frontCover
        ]);
    }
}
