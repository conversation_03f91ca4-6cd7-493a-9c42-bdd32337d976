<?php

namespace Modules\Magazine\Http\Controllers;

use App\Models\Plan;
use Domain\Contacts\Services\ContactViewService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Modules\Magazine\Contracts\MagazineRepository;
use Support\TextGeneration\Contracts\TextGeneratorPromptSettingsRepositoryContract;
use Support\TextGeneration\Models\Setting as TextPromptSetting;

class PromotionalCopiesController extends Controller
{
    /** @var \Modules\Magazine\Contracts\MagazineRepository */
    private $repository;

    /** @var \Support\TextGeneration\Contracts\TextGeneratorPromptSettingsRepositoryContract */
    private $textPromptRepo;

    public function __construct(
        MagazineRepository $repository,
        TextGeneratorPromptSettingsRepositoryContract $textPromptRepo
    ) {
        $this->repository = $repository;
        $this->textPromptRepo = $textPromptRepo;
    }

    public function index(Request $request, ContactViewService $contactViewService)
    {
        $printingLockout = $this->repository->getPrintingLockout($request->user());

        return Inertia::render(
            'Magazine/LetterEditor',
            [
                'pageTitle'              => 'Promotional Copies',
                'isPromotionalCopies'    => true,
                'sessionLifetimeMinutes' => (int)config('session.lifetime'), // todo revisit why this is in place
                'printingLockout'        => $printingLockout->toArray(),
                'mailingList'            => $this->getMailingListFromRequest($request, $contactViewService),
                'defaultTextPrompt'      => $this->textPromptRepo->get(TextPromptSetting::PROMPT_NAME_RECIPIENT_LETTER)
            ]
        );
    }

    protected function getMailingListFromRequest(Request $request, ContactViewService $contactViewService): array
    {
        $contactGroupId = $request->get('contact_group_id');
        $direction = $request->get('direction');
        $orderBy = $request->get('order_by');
        $paginate = $request->get('per_page', 0);

        $planIds = Plan::MAGAZINE_PRODUCTS;

        return $contactViewService->fetchData($contactGroupId, $planIds, $direction, $orderBy, $paginate);
    }
}
