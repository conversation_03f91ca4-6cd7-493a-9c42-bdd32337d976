<?php

namespace Modules\Magazine\Http\Controllers\Recipients;

use Illuminate\Http\Request;
use Modules\Magazine\Http\Controllers\Controller;

class ManageRecipientController extends Controller
{
    public function __invoke(Request $request)
    {
        $user = $request->user();
        $recipientId = $request->route('recipientId');

        $canEdit = $user->can('Alm.MyMailingList.EditRecipient');
        $groupId = $request->get('group_id');

        $mode = $recipientId
            ? $canEdit ? 'edit' : 'view'
            : 'add';

        return view('magazine::recipients.recipient', [
            'id' => $recipientId,
            'groupId' => $groupId ? (int) $groupId : null,
            'mode' => $mode,
            'canEditRecipientAdditionalInfo' => $user->can('Alm.MyMailingList.EditSubscriberAdditionalInfo'),
            'canEditRecipientContactInfo' => $user->can('Alm.MyMailingList.EditSubscriberContactInfo'),
            'canManageRecipientPhotos' => $user->can('contacts.upload-photo'),
            'viewOnly' => ! $canEdit,
        ]);
    }
}