<?php

namespace Modules\Magazine\Http\Controllers\Recipients;

use Illuminate\Http\Request;
use Modules\Magazine\Http\Controllers\Controller;

class IndexController extends Controller
{
    public function __invoke(Request $request)
    {
        $user = $request->user();

        return view('magazine::recipients.index', [
            'canAddRecipient' => $user->can('Alm.MyMailingList.AddRecipient'),
            'canDeleteRecipient' => $user->can('Alm.MyMailingList.DeleteRecipient'),
            'canEditRecipient' => $user->can('Alm.MyMailingList.EditRecipient'),
            'canManageClientGroups' => $user->can('Alm.MyMailingList.ManageClientGroups'),
            'canManageRecipientPhotos' => $user->can('Alm.MyMailingList.ManageRecipientPhotos'),
            'isEmployee' => $user->isEmployee(),
        ]);
    }
}
