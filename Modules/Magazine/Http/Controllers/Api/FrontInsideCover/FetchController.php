<?php

namespace Modules\Magazine\Http\Controllers\Api\FrontInsideCover;

use Modules\Magazine\Http\Controllers\Controller;
use Modules\Magazine\Contracts\FrontInsideCoverRepository;

class FetchController extends Controller
{
    public function __invoke(int $recipientGroupId, FrontInsideCoverRepository $repository)
    {
        if (! $recipientGroupId) {
            return $repository->getPromotionalCopiesLetter();
        }

        return $repository->getFrontInsideCoverData($recipientGroupId);
    }
}
