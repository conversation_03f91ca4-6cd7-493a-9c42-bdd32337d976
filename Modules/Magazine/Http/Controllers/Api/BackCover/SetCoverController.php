<?php

namespace Modules\Magazine\Http\Controllers\Api\BackCover;

use Modules\Magazine\Http\Controllers\Controller;
use Modules\Magazine\Http\Requests\Api\SetBackCoverRequest;
use Modules\Magazine\Contracts\BackOutsideCoverRepository;

class SetCoverController extends Controller
{
    public function __invoke(SetBackCoverRequest $request, BackOutsideCoverRepository $repository)
    {
        $result = $repository->setCover(
            $request->getCoverId(),
            $request->isCustom()
        );

        return ['status' => $result];
    }
}