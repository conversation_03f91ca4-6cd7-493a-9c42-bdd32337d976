<?php

namespace Modules\Magazine\Http\Controllers\Api\BackCover;

use Modules\Magazine\Http\Controllers\Controller;
use Modules\Magazine\Contracts\BackOutsideCoverRepository;

class FetchCoverGroupsController extends Controller
{
    public function __invoke(BackOutsideCoverRepository $repository)
    {
        $data = $repository->getCoverData();

        return [
            'cover_groups' => $data->getCoverGroups()->toArray(),
            'special_edition_cover_group' => $data->getSpecialEditionCoverGroup()->toArray(),
        ];
    }
}