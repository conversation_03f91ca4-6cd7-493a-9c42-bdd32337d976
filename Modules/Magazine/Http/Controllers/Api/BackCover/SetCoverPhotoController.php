<?php

namespace Modules\Magazine\Http\Controllers\Api\BackCover;

use Illuminate\Http\Request;
use Modules\Magazine\Http\Controllers\Controller;
use Modules\Magazine\Contracts\BackOutsideCoverRepository;
use Modules\Magazine\Contracts\MagazineRepository;

class SetCoverPhotoController extends Controller
{
    public function __invoke(
        Request $request,
        BackOutsideCoverRepository $bcRepository,
        MagazineRepository $magRepository
    ) {
        $result = $magRepository->setCoverPhoto(
            $bcRepository->getMetadata()->getPageName(),
            $request->get('magazine_file_group_id')
        );

        return ['status' => $result];
    }
}