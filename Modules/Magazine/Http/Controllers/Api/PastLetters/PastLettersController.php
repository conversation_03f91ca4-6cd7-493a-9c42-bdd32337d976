<?php

namespace Modules\Magazine\Http\Controllers\Api\PastLetters;

use Modules\Magazine\Http\Controllers\Controller;
use App\Http\Requests\Api\LetterEditor\UpdatePastLetterRequest;
use Modules\Magazine\Contracts\FrontInsideCoverRepository;

class PastLettersController extends Controller
{
    public function show(int $recipientGroupId, FrontInsideCoverRepository $repository)
    {
        return $repository->getPastLetters($recipientGroupId);
    }

    public function update(
        UpdatePastLetterRequest $request,
        int $recipientGroupId,
        FrontInsideCoverRepository $repository
    ) {
        return ["success" => $repository->updatePastLetter($recipientGroupId, $request->getLetterContent())];
    }
}