<?php

namespace Modules\Magazine\Http\Controllers\Api\PastLetters;

use Modules\Magazine\Contracts\FrontInsideCoverRepository;

class FetchController
{
    public function __invoke(int $recipientGroupId, FrontInsideCoverRepository $repository)
    {
        $recipientGroups = $repository->getRecipientGroups();
        $pastLetters = $repository->getPastLetters($recipientGroupId);

        return ["groups" => $recipientGroups, "pastLetters" => $pastLetters];
    }

}