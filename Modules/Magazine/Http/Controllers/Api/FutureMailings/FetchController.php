<?php

namespace Modules\Magazine\Http\Controllers\Api\FutureMailings;

use Illuminate\Http\Request;
use Modules\Magazine\Http\Controllers\Controller;
use Modules\Magazine\Contracts\FutureMailingsRepository;

class FetchController extends Controller
{
    public function __invoke(Request $request, FutureMailingsRepository $repository)
    {
        $maxMailings = $request->get('max_mailings');

        $data = $repository->getFutureMailings($maxMailings);

        return $data->toArray();
    }
}