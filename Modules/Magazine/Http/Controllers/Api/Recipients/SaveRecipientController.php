<?php

namespace Modules\Magazine\Http\Controllers\Api\Recipients;

use Illuminate\Http\Request;
use Modules\Magazine\Contracts\RecipientsRepository;
use Modules\Magazine\Entities\SaveRecipientDTO;
use Modules\Magazine\Http\Controllers\Controller;

class SaveRecipientController extends Controller
{
    public function __invoke(Request $request, RecipientsRepository $repository)
    {
        $id = $request->recipient ?? $request->get('id');
        $contactId = $request->account->id ?? $request->get('contact_id');
        $groupId = $request->get('recipient_group_id');
        $lastName = $request->get('last_name');
        $letterSalutation = $request->get('letter_salutation');
        $mailingSalutation = $request->get('mailing_salutation');
        $forceSend = $request->get('force_send') ?? false;
        $saveWithErrors = $request->get('save_with_errors') ?? false;

        $address = [
            'street' => strtoupper($request->get('street')),
            'city' => strtoupper($request->get('city')),
            'state' => strtoupper($request->get('state')),
            'zip' => $request->get('zip'),
        ];

        $conditionalInfo = $this->conditionalInfo($request);

        $additionalInfo = [
            'firstName' => $request->get('first_name'),
            'title' => $request->get('title'),
            'officeName' => $request->get('company_name'),
            'spouseFirstName' => $request->get('spouse_first_name'),
            'spouseLastName' => $request->get('spouse_last_name'),
            'note' => $request->get('note'),
        ];

        if ($id) {
            $additionalInfo = array_merge([
                'lastName' => $lastName,
                'letterSalutation' => $letterSalutation,
                'mailingSalutation' => $mailingSalutation,
            ], $additionalInfo, $conditionalInfo);

            $success = $repository->updateRecipient(
                $id,
                new SaveRecipientDTO(
                    $address,
                    $groupId,
                    $additionalInfo,
                    $forceSend,
                    $saveWithErrors
                )
            );
        } else {
            $additionalInfo = array_merge($additionalInfo, $conditionalInfo);

            $success = $repository->createRecipient(
                new SaveRecipientDTO(
                    $address,
                    $groupId,
                    $additionalInfo,
                    $forceSend,
                    $saveWithErrors,
                    $lastName,
                    $letterSalutation,
                    $mailingSalutation,
                    $contactId
                )
            );
        }

        return ['success' => $success];
    }

    private function conditionalInfo(Request $request)
    {
        $conditionalInfo = array();

        if ($request->has('purchase_date')) {
            $conditionalInfo['purchaseDate'] = $request->get('purchase_date');
        }
        if ($request->has('purchase_price')) {
            $conditionalInfo['purchasePrice'] = $request->get('purchase_price');
        }
        if ($request->has('birthday')) {
            $conditionalInfo['birthday'] = $request->get('birthday');
        }
        if ($request->has('spouse_birthday')) {
            $conditionalInfo['spouseBirthday'] = $request->get('spouse_birthday');
        }
        if ($request->has('home_phone')) {
            $conditionalInfo['homePhone'] = $request->get('home_phone');
        }
        if ($request->has('home_ext')) {
            $conditionalInfo['homeExt'] = $request->get('home_ext');
        }
        if ($request->has('work_phone')) {
            $conditionalInfo['workPhone'] = $request->get('work_phone');
        }
        if ($request->has('work_ext')) {
            $conditionalInfo['workExt'] = $request->get('work_ext');
        }
        if ($request->has('cell_phone')) {
            $conditionalInfo['cellPhone'] = $request->get('cell_phone');
        }
        if ($request->has('cell_ext')) {
            $conditionalInfo['cellExt'] = $request->get('cell_ext');
        }
        if ($request->has('fax')) {
            $conditionalInfo['faxNumber'] = $request->get('fax');
        }
        if ($request->has('email')) {
            $conditionalInfo['primaryEmail'] = $request->get('email');
        }
        if ($request->has('email2')) {
            $conditionalInfo['secondaryEmail'] = $request->get('email2');
        }

        return $conditionalInfo;
    }
}
