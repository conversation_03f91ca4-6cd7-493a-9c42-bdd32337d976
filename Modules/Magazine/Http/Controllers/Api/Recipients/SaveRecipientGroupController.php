<?php

namespace Modules\Magazine\Http\Controllers\Api\Recipients;

use Illuminate\Http\Request;
use Modules\Magazine\Contracts\RecipientsRepository;
use Modules\Magazine\Http\Controllers\Controller;

class SaveRecipientGroupController extends Controller
{
    public function __invoke(Request $request, RecipientsRepository $repository)
    {
        $id = $repository->saveRecipientGroup(
            $request->recipient_group ?? $request->get('id'),
            $request->get('name'),
            $request->account->id ?? $request->get('contact_id')
        );

        return ['id' => $id];
    }
}
