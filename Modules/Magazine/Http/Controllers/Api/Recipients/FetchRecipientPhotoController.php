<?php

namespace Modules\Magazine\Http\Controllers\Api\Recipients;

use Modules\Magazine\Contracts\RecipientsRepository;
use Modules\Magazine\Http\Controllers\Controller;

class FetchRecipientPhotoController extends Controller
{
    public function __invoke(int $id, RecipientsRepository $repository)
    {
        $photo = $repository->getRecipientPhoto($id);

        return [
            'photo' => $photo->toArray(),
        ];
    }
}