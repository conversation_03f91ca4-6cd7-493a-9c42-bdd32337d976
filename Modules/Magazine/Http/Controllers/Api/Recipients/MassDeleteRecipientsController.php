<?php

namespace Modules\Magazine\Http\Controllers\Api\Recipients;

use Illuminate\Http\Request;
use Modules\Magazine\Contracts\RecipientsRepository;
use Modules\Magazine\Http\Controllers\Controller;

class MassDeleteRecipientsController extends Controller
{
    public function __invoke(Request $request, RecipientsRepository $repository)
    {
        $success = $repository->massDeleteRecipients($request->get('ids'));

        return ['success' => $success];
    }
}
