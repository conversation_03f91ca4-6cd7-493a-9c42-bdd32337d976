<?php

namespace Modules\Magazine\Http\Controllers\Api\Recipients;

use Illuminate\Http\Request;
use Modules\Magazine\Contracts\RecipientsRepository;
use Modules\Magazine\Http\Controllers\Controller;

class CheckAddressController extends Controller
{
    /**
     * @param \Illuminate\Http\Request                         $request
     * @param \Modules\Magazine\Contracts\RecipientsRepository $repository
     *
     * @return \Modules\Magazine\Entities\CheckAddressDTO
     * @throws \Illuminate\Validation\ValidationException When validation fails
     */
    public function __invoke(Request $request, RecipientsRepository $repository)
    {
        $this->validate($request, [
            'line1' => 'present',
            'city'  => 'present',
            'state' => 'present',
            'zip'   => 'present'
        ], [
            'line1.present' => 'Must provide a street address',
            'city.present'  => 'Must provide a City',
            'state.present' => 'Must provide a State',
            'zip.present'   => 'Must provide a zip code'
        ]);

        return $repository->checkAddress(
            $request->get('line1') ?? '',
            $request->get('city') ?? '',
            $request->get('state') ?? '',
            $request->get('zip') ?? ''
        );
    }
}