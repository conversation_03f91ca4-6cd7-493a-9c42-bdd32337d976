<?php

namespace Modules\Magazine\Http\Controllers\Api\Recipients;

use Illuminate\Http\Request;
use Modules\Magazine\Contracts\RecipientsRepository;
use Modules\Magazine\Http\Controllers\Controller;

class FetchRecipientsThatMayLoseExclusivityIfTransferredToGroupController extends Controller
{
    public function __invoke(Request $request, int $id, RecipientsRepository $repository)
    {
        $recipientIds = $request->get('recipient_ids');

        $data = $repository->getRecipientsThatMayLoseExclusivityIfTransferredToGroup(
            $id,
            $recipientIds
        );

        return $data;
    }
}
