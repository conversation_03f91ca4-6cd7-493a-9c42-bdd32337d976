<?php

namespace Modules\Magazine\Http\Controllers\Api\Recipients;

use Illuminate\Http\Request;
use Modules\Magazine\Contracts\RecipientsRepository;
use Modules\Magazine\Http\Controllers\Controller;

class TogglePrintingGroupController extends Controller
{
    public function __invoke(Request $request, int $id, RecipientsRepository $repository)
    {
        $printing = $request->get('printing');

        $method = $printing ? 'enablePrintingGroup' : 'disablePrintingGroup';

        $success = $repository->{$method}($id);

        return ['success' => $success];
    }
}
