<?php

namespace Modules\Magazine\Http\Controllers\Api\Recipients;

use Illuminate\Http\Request;
use Modules\Magazine\Contracts\RecipientsRepository;
use Modules\Magazine\Http\Controllers\Controller;

class UploadPhotoController extends Controller
{
    public function __invoke(Request $request, int $id, RecipientsRepository $repository)
    {
        $success = $repository->uploadPhoto($id, $request->file('photo_file'));

        return ['success' => $success];
    }
}