<?php

namespace Modules\Magazine\Http\Controllers\Api\Recipients;

use Modules\Magazine\Contracts\RecipientsRepository;
use Modules\Magazine\Http\Controllers\Controller;
use Illuminate\Http\Request;

class FetchGroupsController extends Controller
{
    public function __invoke(RecipientsRepository $repository, Request $request)
    {
        return $repository->getMyMailingListReport($request->recipient_group ?? null, $request->account ? $request->account->id : null);
    }
}
