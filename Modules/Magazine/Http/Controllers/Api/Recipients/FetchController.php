<?php

namespace Modules\Magazine\Http\Controllers\Api\Recipients;

use Illuminate\Http\Request;
use Modules\Magazine\Contracts\RecipientsRepository;
use Modules\Magazine\Http\Controllers\Controller;

class FetchController extends Controller
{
    public function __invoke(RecipientsRepository $repository, Request $request)
    {
        return $repository->getMyMailingList(null, $request->account ? $request->account->id : null);
    }
}
