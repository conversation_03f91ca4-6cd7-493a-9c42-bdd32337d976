<?php

namespace Modules\Magazine\Http\Controllers\Api\Recipients;

use Illuminate\Http\Request;
use Modules\Magazine\Contracts\RecipientsRepository;
use Modules\Magazine\Http\Controllers\Controller;

class UseRecipientPhotoController extends Controller
{
    public function __invoke(Request $request, RecipientsRepository $repository)
    {
        return ['success' => $repository->toggleRecipientPhotoOnCovers($request->get('covers'))];
    }
}