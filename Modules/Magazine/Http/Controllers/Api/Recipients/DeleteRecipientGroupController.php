<?php

namespace Modules\Magazine\Http\Controllers\Api\Recipients;

use Illuminate\Http\Request;
use Modules\Magazine\Contracts\RecipientsRepository;
use Modules\Magazine\Http\Controllers\Controller;

class DeleteRecipientGroupController extends Controller
{
    public function __invoke($id, RecipientsRepository $repository, Request $request)
    {
        $success = $repository->deleteRecipientGroup(
            $request->recipient_group ?? $id
        );

        return ['success' => $success];
    }
}
