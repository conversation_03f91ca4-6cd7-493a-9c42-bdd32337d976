<?php

namespace Modules\Magazine\Http\Controllers\Api\Recipients;

use Illuminate\Http\Request;
use Modules\Magazine\Contracts\RecipientsRepository;
use Modules\Magazine\Http\Controllers\Controller;

class DeleteRecipientPhotoController extends Controller
{
    public function __invoke(Request $request, RecipientsRepository $repository)
    {
        $photoId = $request->get('photo_id');

        $success = $repository->deletePhoto($photoId);

        return ['success' => $success];
    }
}