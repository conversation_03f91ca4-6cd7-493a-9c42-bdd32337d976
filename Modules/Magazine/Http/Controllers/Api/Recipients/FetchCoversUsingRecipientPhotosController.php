<?php

namespace Modules\Magazine\Http\Controllers\Api\Recipients;

use Modules\Magazine\Contracts\RecipientsRepository;
use Modules\Magazine\Http\Controllers\Controller;

class FetchCoversUsingRecipientPhotosController extends Controller
{
    public function __invoke(RecipientsRepository $repository)
    {
        return [
            'covers' => $repository->getCoversUsingRecipientPhotos(),
        ];
    }
}