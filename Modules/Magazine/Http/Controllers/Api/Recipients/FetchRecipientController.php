<?php

namespace Modules\Magazine\Http\Controllers\Api\Recipients;

use Modules\Magazine\Contracts\RecipientsRepository;
use Modules\Magazine\Http\Controllers\Controller;
use Illuminate\Http\Request;

class FetchRecipientController extends Controller
{
    public function __invoke(RecipientsRepository $repository, Request $request)
    {
        $recipient = $repository->getRecipient($request->recipient ?? $request->id);

        return [
            'recipient' => $recipient->toArray(),
        ];
    }
}
