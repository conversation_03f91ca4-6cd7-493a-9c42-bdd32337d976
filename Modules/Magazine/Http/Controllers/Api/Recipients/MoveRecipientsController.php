<?php

namespace Modules\Magazine\Http\Controllers\Api\Recipients;

use Illuminate\Http\Request;
use Modules\Magazine\Contracts\RecipientsRepository;
use Modules\Magazine\Http\Controllers\Controller;

class MoveRecipientsController extends Controller
{
    public function __invoke(Request $request, RecipientsRepository $repository)
    {
        $success = $repository->moveRecipients(
            $request->get('recipient_ids'),
            $request->route('id')
        );

        return ['success' => $success];
    }
}