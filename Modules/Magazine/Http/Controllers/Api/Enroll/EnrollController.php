<?php

namespace Modules\Magazine\Http\Controllers\Api\Enroll;

use App\Models\Plan;
use Illuminate\Http\Request;
use Modules\Magazine\Http\Controllers\Controller;
use Illuminate\Http\Response;
use Modules\Magazine\Contracts\EnrollRepository;
use Modules\Xrms\Api\Exceptions\GeneralException;

class EnrollController extends Controller
{
    public function __invoke(Request $request, EnrollRepository $repository)
    {
        $product = $request->get('product');

        $plan = $product == 'alm' ? Plan::PLAN_ID_AMERICAN_LIFESTYLE_MAGAZINE : Plan::PLAN_ID_START_HEALTHY_MAGAZINE;

        try {
            return ['success' => $repository->enrollInProduct($plan)];
        } catch (GeneralException $e) {
            return response()->json($e->getResponseError(), $e->getResponseStatusCode());
        }
    }
}
