<?php

namespace Modules\Magazine\Http\Controllers\Api\MailingHistory;

use Illuminate\Http\Request;
use Modules\Magazine\Http\Controllers\Controller;
use Modules\Magazine\Contracts\MailingHistoryRepository;

class FetchIssueDetailsController extends Controller
{
    public function __invoke(Request $request, int $issueId, MailingHistoryRepository $repository)
    {
        $data = $repository->getMailingHistoryDetails(
            $issueId,
            $request->get('print_agent_history_id')
        );

        return [
            'issue_details' => $data->toArray(),
        ];
    }
}