<?php

namespace Modules\Magazine\Http\Controllers\Api\MailingHistory;

use Illuminate\Http\Request;
use Modules\Magazine\Http\Controllers\Controller;
use Modules\Magazine\Contracts\MailingHistoryRepository;

class FetchMailingHistoryController extends Controller
{
    public function __invoke(Request $request, MailingHistoryRepository $repository)
    {
        $data = $repository->getMailingHistory($request->get('year'));

        return [
            'mailing_history' => $data,
        ];
    }
}