<?php

namespace Modules\Magazine\Http\Controllers\Api\Dashboard;

use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Magazine\AggregateRoots\EmailProofAggregateRoot;
use Modules\Magazine\DTO\EmailProofDTO;
use Modules\Magazine\Http\Controllers\Controller;

class EmailProofController extends Controller
{
    public function __invoke(EmailProofAggregateRoot $aggregateRoot, Request $request): JsonResponse
    {
        $data = [
            'issue_id'          => $request->get('issue_id'),
            'product_id'        => $request->get('product_id'),
            'requested_date'    => Carbon::now()->toDateString(),
        ];

        $aggregateRoot->customerRequestedProof(EmailProofDTO::fromArray($data))->persist();

        return response()->json(['success' => true]);
    }
}
