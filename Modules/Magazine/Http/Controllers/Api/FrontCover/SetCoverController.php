<?php

namespace Modules\Magazine\Http\Controllers\Api\FrontCover;

use Illuminate\Http\Request;
use Modules\Magazine\Http\Controllers\Controller;
use Modules\Magazine\Contracts\FrontCoverRepository;

class SetCoverController extends Controller
{
    public function __invoke(Request $request, FrontCoverRepository $repository)
    {
        $result = $repository->setFrontCover(
            $request->get('front_cover_id'),
            $request->get('is_special_edition')
        );

        return ['success' => $result];
    }
}