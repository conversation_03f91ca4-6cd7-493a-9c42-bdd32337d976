<?php

namespace Modules\Magazine\Http\Controllers\Api\PromotionalCopies;

use Modules\Magazine\Http\Controllers\Controller;
use Modules\Magazine\Http\Requests\Api\LetterEditor\UpdatePromotionalCopiesOrderRequest;
use Modules\Magazine\Contracts\FrontInsideCoverRepository;
use Modules\Magazine\Entities\PromotionalCopyOrder;

class PromotionalCopiesOrderController extends Controller
{
    public function update(
        UpdatePromotionalCopiesOrderRequest $request,
        int $orderId,
        FrontInsideCoverRepository $repository
    ) {
        $result = $repository->updatePromotionalCopyOrder(new PromotionalCopyOrder(
            $orderId,
            $request->getQuantity(),
            $request->getAddress1(),
            $request->getAddress2(),
            $request->getCity(),
            $request->getState(),
            $request->getZip(),
            $request->getRun(),
            $request->getGreeting()
        ));

        return ["success" => $result];
    }
}