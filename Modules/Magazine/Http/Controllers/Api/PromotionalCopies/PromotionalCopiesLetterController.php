<?php

namespace Modules\Magazine\Http\Controllers\Api\PromotionalCopies;

use Modules\Magazine\Http\Controllers\Controller;
use Modules\Magazine\Http\Requests\Api\LetterEditor\UpdatePromotionalCopiesLetterRequest;
use Modules\Magazine\Contracts\FrontInsideCoverRepository;
use Modules\Magazine\Entities\LetterSignatures;
use Modules\Xrms\Entities\RecipientLetter;

class PromotionalCopiesLetterController extends Controller
{
    public function store(
        UpdatePromotionalCopiesLetterRequest $request,
        FrontInsideCoverRepository $repository
    ) {
        $letter = new RecipientLetter(
            $request->getIssueId(),
            0,
            $request->getLetterContent(),
            false
        );

        $addTrademark = $request->getAddTrademark();

        $letterSignatures = new LetterSignatures(
            $request->getSignatureText1(),
            $request->getSignatureText2(),
            $request->getDefaultSignature(),
            $request->getSignatureType()
        );
        $recipientGroupIds = $request->getRecipientGroupIds();

        $response = $repository->updatePromotionalLetter($letter, $letterSignatures,$addTrademark, $recipientGroupIds);

        return ['success' => $response];
    }

    public function reset(FrontInsideCoverRepository $repository)
    {
        return ['success' => $repository->resetPromotionalCopyLetterToDefault()];
    }
}
