<?php

namespace Modules\Magazine\Http\Controllers\Api\BackInsideCover;

use Modules\Magazine\Http\Controllers\Controller;
use Modules\Magazine\Http\Requests\Api\SetBackCoverRequest;
use Modules\Magazine\Contracts\BackInsideCoverRepository;

class SetCoverController extends Controller
{
    public function __invoke(SetBackCoverRequest $request, BackInsideCoverRepository $repository)
    {
        $result = $repository->setCover(
            $request->getCoverId(),
            $request->isCustom()
        );

        return ['status' => $result];
    }
}