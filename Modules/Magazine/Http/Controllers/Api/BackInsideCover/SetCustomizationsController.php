<?php

namespace Modules\Magazine\Http\Controllers\Api\BackInsideCover;

use Illuminate\Http\Request;
use Modules\Magazine\Http\Controllers\Controller;
use Modules\Xrms\Contracts\AccountRepository;
use Modules\Magazine\Services\PrepareBackCoverFields;

class SetCustomizationsController extends Controller
{
    /** @var PrepareBackCoverFields  */
    private $prepareFields;

    public function __construct(PrepareBackCoverFields $prepareFields)
    {
        $this->prepareFields = $prepareFields;
    }

    public function __invoke(
        Request $request,
        AccountRepository $repository
    ) {
        $result = $repository->customizeFields(
            $this->prepareFields->handle($request->only(['field1', 'field2', 'field3', 'field4', 'field5', 'field6']))
        );

        return ['status' => $result];
    }
}