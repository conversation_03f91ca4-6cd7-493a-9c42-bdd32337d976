<?php

namespace Modules\Magazine\Http\Controllers\Api\BackInsideCover;

use Illuminate\Http\Request;
use Modules\Magazine\Http\Controllers\Controller;
use Modules\Magazine\Contracts\BackInsideCoverRepository;
use Modules\Magazine\Contracts\MagazineRepository;

class SetCoverPhotoController extends Controller
{
    public function __invoke(
        Request $request,
        BackInsideCoverRepository $bcRepository,
        MagazineRepository $magRepository
    ) {
        $result = $magRepository->setCoverPhoto(
            $bcRepository->getMetadata()->getPageName(),
            $request->get('magazine_file_group_id')
        );

        return ['status' => $result];
    }
}