<?php

namespace Modules\Magazine\Http\Controllers\Api\BackInsideCover;

use Modules\Magazine\Http\Controllers\Controller;
use Modules\Magazine\Contracts\BackInsideCoverRepository;

class FetchCoverGroupsController extends Controller
{
    public function __invoke(BackInsideCoverRepository $repository)
    {
        $data = $repository->getCoverData();

        return [
            'cover_groups' => $data->getCoverGroups()->toArray(),
            'special_edition_cover_group' => $data->getSpecialEditionCoverGroup()->toArray(),
        ];
    }
}