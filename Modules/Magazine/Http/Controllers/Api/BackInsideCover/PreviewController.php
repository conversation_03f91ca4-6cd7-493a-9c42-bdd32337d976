<?php

namespace Modules\Magazine\Http\Controllers\Api\BackInsideCover;

use App\Contracts\AccountRepository;
use Modules\Magazine\Http\Controllers\Controller;
use Modules\Magazine\Contracts\BackInsideCoverRepository;
use Modules\Magazine\Contracts\MagazineRepository;

class PreviewController extends Controller
{
    public function __invoke(
        string $id,
        BackInsideCoverRepository $bcRepository,
        MagazineRepository $magRepository,
        AccountRepository $accountRepository
    ) {
        $data = $bcRepository->getCoverData();
        $cover = $bcRepository->getCoverPreview(
            $id,
            $accountRepository->currentAccount()->isInGlobalAdsProgram()
        );
        $fields = $magRepository->getCustomizedFields();

        return [
            'cover' => $cover->toArray(),
            'customizedFields' => $fields->toArray(),
            'coverPhotos' => $data->getCoverPhotos()->toArray(),
            'selectedCoverPhoto' => $data->getSelectedCoverPhoto()->toArray(),
        ];
    }
}
