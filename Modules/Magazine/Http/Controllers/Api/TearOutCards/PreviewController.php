<?php

namespace Modules\Magazine\Http\Controllers\Api\TearOutCards;

use Illuminate\Support\Facades\Storage;
use Modules\Magazine\Contracts\ContactBlockRepository;
use Modules\Magazine\Contracts\TearOutCardsRepository;
use Modules\Magazine\Entities\OfficeLogo;
use Modules\Magazine\Http\Controllers\Controller;

class PreviewController extends Controller
{
    public function __invoke(string $id, TearOutCardsRepository $tocRepository, ContactBlockRepository $cbRepository)
    {
        $toc = $tocRepository->getTearOutCardPreview($id);
        $cb = $cbRepository->getContactBlockPreview();

        $mainAgentPhoto = $cb->getMainAgentPhoto();

        return [
            'agent_photo' => $mainAgentPhoto ? $mainAgentPhoto->toArray() : null,
            'contact_block' => $cb->getContactBlock()->toArray(),
            'tear_out_card' => $toc->toArray(),
            'office_logo' => ($cb->getOfficeLogo() ?? $this->makeNoLogo())->toArray(),
        ];
    }

    private function makeNoLogo()
    {
        return new OfficeLogo(Storage::disk('external_images')->url('officelogos/nologo.gif'));
    }
}