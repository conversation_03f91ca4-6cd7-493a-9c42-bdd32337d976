<?php

namespace Modules\Magazine\Http\Controllers\Api\TearOutCards;

use Modules\Magazine\Http\Controllers\Controller;
use App\Http\Requests\Api\TearOutCards\SetTearOutCardRequest;
use Modules\Magazine\Contracts\TearOutCardsRepository;

class SetTearOutCardController extends Controller
{
    public function __invoke(SetTearOutCardRequest $request, TearOutCardsRepository $repository)
    {
        return [
            'success' => $repository->setTearOutCard(
                $request->getCardNumber(),
                $request->getTocId(),
                $request->isCustom()
            )
        ];
    }
}