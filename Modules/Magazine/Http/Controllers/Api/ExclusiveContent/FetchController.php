<?php

namespace Modules\Magazine\Http\Controllers\Api\ExclusiveContent;

use App\Models\Plan;
use Domain\Account\Models\AccountPlan;
use Domain\Magazine\Exceptions\ExclusiveContentDoesNotExist;
use Domain\Magazine\Models\ExclusiveContent;
use Illuminate\Http\Response;
use Modules\Magazine\Contracts\MagazineRepository;
use Modules\Magazine\Http\Controllers\Controller;

class FetchController extends Controller
{
    public function __invoke(MagazineRepository $magazineRepository)
    {
        $issue = $magazineRepository->getCurrentIssue();

        try {
            $magazineId = AccountPlan::getMagazinePlanId() ?? Plan::PLAN_ID_AMERICAN_LIFESTYLE_MAGAZINE;
            $entry = ExclusiveContent::findByPlanAndIssueIds($magazineId, $issue->getId());

        } catch (ExclusiveContentDoesNotExist $e) {
            return Response::create(['message' => $e->getMessage()], 404);
        }

        return [
            'exclusive_content' => $entry->toArray(),
        ];
    }
}
