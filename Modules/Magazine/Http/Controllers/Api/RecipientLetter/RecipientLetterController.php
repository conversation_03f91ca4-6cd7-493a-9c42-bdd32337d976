<?php

namespace Modules\Magazine\Http\Controllers\Api\RecipientLetter;

use Illuminate\Http\Request;
use Modules\Magazine\Domain\Recipient\SaveRecipientLetterAggregateRoot;
use Modules\Magazine\Http\Controllers\Controller;
use Modules\Magazine\Http\Requests\Api\LetterEditor\UpdateRecipientLetterRequest;
use Modules\Magazine\Contracts\FrontInsideCoverRepository;
use Modules\Magazine\Entities\LetterSignatures;
use Modules\Xrms\Entities\RecipientLetter;

class RecipientLetterController extends Controller
{
    public function store(
        UpdateRecipientLetterRequest $request,
        SaveRecipientLetterAggregateRoot $ar,
        FrontInsideCoverRepository $repository
    ) {
        $letter = new RecipientLetter(
            $request->getIssueId(),
            0,
            $request->getLetterContent(),
            false
        );

        $addTrademark = $request->getAddTrademark();
        $letterSignatures = new LetterSignatures(
            $request->getSignatureText1(),
            $request->getSignatureText2(),
            $request->getDefaultSignature(),
            $request->getSignatureType()
        );

        $recipientGroupIds = $request->getRecipientGroupIds();

        $response = $repository->updateRecipientLetter($letter, $letterSignatures, $addTrademark, $recipientGroupIds);

        //call event for recipient letter saved
        foreach ($recipientGroupIds as $groupId) {
            foreach ($repository->getRecipientGroups() as $recipientGroup) {
                if ($recipientGroup->id === $groupId) {
                    $ar->recipientLetterSaved(
                        $groupId,
                        $recipientGroup->name,
                        $letter->getLetterText()
                    );
                }
            }
        }
        $ar->persist();
        return ['success' => $response];
    }

    public function reset(Request $request, FrontInsideCoverRepository $repository)
    {
        $success = $repository->resetRecipientGroupLetterToDefault(
            $request->get('issue_id'),
            $request->get('recipient_group_id')
        );

        return ['success' => $success];
    }
}
