<?php

namespace Modules\Magazine\Http\Controllers;

use App\Models\Plan;
use Domain\Contacts\Services\ContactViewService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Modules\Magazine\Contracts\FutureMailingsRepository;
use Modules\Magazine\Contracts\MagazineRepository;
use Modules\Xrms\Models\ClientGroup;
use Support\TextGeneration\Contracts\TextGeneratorPromptSettingsRepositoryContract;
use Support\TextGeneration\Models\Setting as TextPromptSetting;

class RecipientLetterController extends Controller
{
    /** @var \Modules\Magazine\Contracts\MagazineRepository */
    private $repository;

    /** @var \Modules\Magazine\Contracts\FutureMailingsRepository */
    private $futureMailingsRepository;

    /** @var \Support\TextGeneration\Contracts\TextGeneratorPromptSettingsRepositoryContract */
    private $textPromptRepo;

    public function __construct(
        MagazineRepository $repository,
        FutureMailingsRepository $futureMailingsRepository,
        TextGeneratorPromptSettingsRepositoryContract $textPromptRepo
    )
    {
        $this->repository = $repository;
        $this->futureMailingsRepository = $futureMailingsRepository;
        $this->textPromptRepo = $textPromptRepo;
    }

    public function index(Request $request, ContactViewService $contactViewService)
    {
        return Inertia::render(
            'Magazine/LetterEditor',
            [
                'pageTitle'               => 'Recipient Letter',
                'sessionLifetimeMinutes'  => (int)config('session.lifetime'),
                'printingLockout'         => $this->repository->getPrintingLockout($request->user()),
                'defaultRecipientGroupId' => $this->getTargetedRecipientGroupId($request),
                'nextMailing'             => $this->futureMailingsRepository->getNextMailing(),
                'mailingList'             => $this->getMailingListFromRequest($request, $contactViewService),
                'defaultTextPrompt'      => $this->textPromptRepo->get(TextPromptSetting::PROMPT_NAME_RECIPIENT_LETTER)
            ]
        );
    }

    protected function getTargetedRecipientGroupId(Request $request): int
    {
        $contactGroupUuid = $request->route('contact_group');

        $recipientGroup = $contactGroupUuid
            ? ClientGroup::findByUuid($contactGroupUuid)
            : new ClientGroup(['client_group_id' => 0]);

        return $recipientGroup->id();
    }

    protected function getMailingListFromRequest(Request $request, ContactViewService $contactViewService): array
    {
        $contactGroupId = $request->get('contact_group_id');
        $direction = $request->get('direction');
        $orderBy = $request->get('order_by');
        $paginate = $request->get('per_page', 0);

        $planIds = Plan::MAGAZINE_PRODUCTS;

        return $contactViewService->fetchData($contactGroupId, $planIds, $direction, $orderBy, $paginate);
    }
}
