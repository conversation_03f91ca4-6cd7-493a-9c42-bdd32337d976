<?php

namespace Modules\Magazine\Http\Controllers;

use Inertia\Inertia;
use Modules\Magazine\Contracts\FutureMailingsRepository;

class FrontInsideCoverController extends Controller
{
    /** @var \Modules\Magazine\Contracts\FutureMailingsRepository */
    private $futureMailingsRepository;

    public function __construct(FutureMailingsRepository $futureMailingsRepository)
    {
        $this->futureMailingsRepository = $futureMailingsRepository;
    }

    public function index()
    {
        return Inertia::render('Magazine/FrontInsideCover', [
            'pageTitle' => 'Front Inside Cover',
            'nextMailing' => $this->futureMailingsRepository->getNextMailing()
        ]);
    }
}
