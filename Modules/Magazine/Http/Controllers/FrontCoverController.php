<?php

namespace Modules\Magazine\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Modules\Magazine\Contracts\FutureMailingsRepository;
use Modules\Magazine\Contracts\MagazineRepository;

class FrontCoverController extends Controller
{
    /** @var \Modules\Magazine\Contracts\MagazineRepository */
    private $repository;

    /** @var \Modules\Magazine\Contracts\FutureMailingsRepository */
    private $futureMailingsRepository;

    public function __construct(MagazineRepository $repository, FutureMailingsRepository $futureMailingsRepository)
    {
        $this->repository = $repository;
        $this->futureMailingsRepository = $futureMailingsRepository;
    }

    public function __invoke(Request $request)
    {
        return Inertia::render(
            'Magazine/FrontCover',
            [
                'pageTitle'       => 'Front Cover',
                'printingLockout' => $this->repository->getPrintingLockout($request->user()),
                'nextMailing'     => $this->futureMailingsRepository->getNextMailing(),
            ]
        );
    }
}
