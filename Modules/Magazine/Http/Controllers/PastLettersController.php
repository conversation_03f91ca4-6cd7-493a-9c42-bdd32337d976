<?php

namespace Modules\Magazine\Http\Controllers;

use App\Context\AccountId;
use Illuminate\Http\Request;

class PastLettersController extends Controller
{
    public function index(Request $request)
    {
        return view('magazine::past-letters', [
            'contactId'        => AccountId::current(), // todo remove the need for this
            'recipientGroupId' => $request->route('recipientGroupId'),
        ]);
    }
}
