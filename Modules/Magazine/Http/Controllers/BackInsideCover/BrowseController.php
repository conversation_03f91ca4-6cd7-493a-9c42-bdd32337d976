<?php

namespace Modules\Magazine\Http\Controllers\BackInsideCover;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Modules\Magazine\Contracts\BackInsideCoverRepository;
use Modules\Magazine\Contracts\FutureMailingsRepository;
use Modules\Magazine\Contracts\MagazineRepository;
use Modules\Magazine\Http\Controllers\Controller;

class BrowseController extends Controller
{
    public function __invoke(
        Request $request,
        BackInsideCoverRepository $repository,
        MagazineRepository $magazineRepository,
        FutureMailingsRepository $futureMailingsRepository
    ): Response {
        return Inertia::render('Magazine/BackCoversBrowse', [
            'source'          => 'browse',
            'pageTitle'       => 'Select Back Inside Cover',
            'meta'            => $repository->getMetadata()->toArray(),
            'selectedCoverId' => $repository->getCoverData()->getSelectedCoverId(),
            'printingLockout' => $magazineRepository->getPrintingLockout($request->user()),
            'nextMailing'     => $futureMailingsRepository->getNextMailing(),
        ]);
    }
}
