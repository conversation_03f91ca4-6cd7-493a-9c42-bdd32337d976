<?php

namespace Modules\Magazine\Http\Controllers\BackInsideCover;

use App\Contracts\AccountRepository;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Modules\Magazine\Contracts\BackInsideCoverRepository;
use Modules\Magazine\Contracts\FutureMailingsRepository;
use Modules\Magazine\Contracts\MagazineRepository;
use Modules\Magazine\Http\Controllers\Controller;

class IndexController extends Controller
{
    public function __invoke(
        Request $request,
        AccountRepository $accountRepository,
        BackInsideCoverRepository $repository,
        FutureMailingsRepository $futureMailingsRepository,
        MagazineRepository $magazineRepository
    ): Response {
        return Inertia::render('Magazine/BackCovers', [
            'source'          => 'index',
            'pageTitle'       => 'Back Inside Cover',
            'isInGlobalAds'   => $accountRepository->currentAccount()->isInGlobalAdsProgram(),
            'meta'            => $repository->getMetadata()->toArray(),
            'selectedCoverId' => $repository->getCoverData()->getSelectedCoverId(),
            'printingLockout' => $magazineRepository->getPrintingLockout($request->user()),
            'nextMailing'     => $futureMailingsRepository->getNextMailing(),
        ]);
    }
}
