<?php

namespace Modules\Magazine\Http\Controllers\BackInsideCover;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Modules\Magazine\Contracts\BackInsideCoverRepository;
use Modules\Magazine\Contracts\MagazineRepository;
use Modules\Magazine\Http\Controllers\Controller;

class ConfirmController extends Controller
{
    public function __invoke(
        Request $request,
        BackInsideCoverRepository $repository,
        MagazineRepository $magazineRepository
    ): Response {
        return Inertia::render('Magazine/BackCoversConfirm', [
            'source'          => 'confirm',
            'pageTitle'       => 'Back Inside Cover Preview',
            'coverId'         => $request->route('id'),
            'meta'            => $repository->getMetadata()->toArray(),
            'printingLockout' => $magazineRepository->getPrintingLockout($request->user()),
        ]);


        return view('magazine::back-covers.confirm', [
            'coverId' => $request->route('id'),
            'meta' => $repository->getMetadata()->toArray(),
        ]);
    }
}
