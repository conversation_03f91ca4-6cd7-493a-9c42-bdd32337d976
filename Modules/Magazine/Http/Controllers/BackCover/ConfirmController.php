<?php

namespace Modules\Magazine\Http\Controllers\BackCover;

use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Http\Request;
use Modules\Magazine\Contracts\BackOutsideCoverRepository;
use Modules\Magazine\Contracts\MagazineRepository;
use Modules\Magazine\Http\Controllers\Controller;

class ConfirmController extends Controller
{
    public function __invoke(
        Request $request,
        BackOutsideCoverRepository $repository,
        MagazineRepository $magazineRepository
    ): Response {
        return Inertia::render('Magazine/BackCoversConfirm', [
            'source'          => 'confirm',
            'pageTitle'       => 'Back Cover Preview',
            'coverId'         => $request->route('id'),
            'meta'            => $repository->getMetadata()->toArray(),
            'printingLockout' => $magazineRepository->getPrintingLockout($request->user()),
        ]);
    }
}
