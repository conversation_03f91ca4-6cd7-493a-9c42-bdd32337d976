<?php

namespace Modules\Magazine\Http\Controllers\BackCover;

use App\Contracts\AccountRepository;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Modules\Magazine\Contracts\BackOutsideCoverRepository;
use Modules\Magazine\Contracts\FutureMailingsRepository;
use Modules\Magazine\Contracts\MagazineRepository;
use Modules\Magazine\Http\Controllers\Controller;

class IndexController extends Controller
{
    public function __invoke(
        Request $request,
        AccountRepository $accountRepository,
        MagazineRepository $magazineRepository,
        FutureMailingsRepository $futureMailingsRepository,
        BackOutsideCoverRepository $repository
    ): Response {
        return Inertia::render('Magazine/BackCovers', [
            'source'          => 'index',
            'pageTitle'       => 'Back Cover',
            'isInGlobalAds'   => $accountRepository->currentAccount()->isInGlobalAdsProgram(),
            'meta'            => $repository->getMetadata()->toArray(),
            'selectedCoverId' => $repository->getCoverData()->getSelectedCoverId(),
            'printingLockout' => $magazineRepository->getPrintingLockout($request->user()),
            'nextMailing'     => $futureMailingsRepository->getNextMailing(),
        ]);
    }
}
