<?php

namespace Modules\Magazine\Http\Controllers\BackCover;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Modules\Magazine\Contracts\BackOutsideCoverRepository;
use Modules\Magazine\Contracts\FutureMailingsRepository;
use Modules\Magazine\Contracts\MagazineRepository;
use Modules\Magazine\Http\Controllers\Controller;

class BrowseController extends Controller
{
    public function __invoke(
        Request $request,
        BackOutsideCoverRepository $repository,
        FutureMailingsRepository $futureMailingsRepository,
        MagazineRepository $magazineRepository
    ): Response {
        return Inertia::render('Magazine/BackCoversBrowse', [
            'source'          => 'browse',
            'pageTitle'       => 'Select Back Cover',
            'selectedCoverId' => $repository->getCoverData()->getSelectedCoverId(),
            'meta'            => $repository->getMetadata()->toArray(),
            'printingLockout' => $magazineRepository->getPrintingLockout($request->user()),
            'nextMailing'     => $futureMailingsRepository->getNextMailing()
        ]);
    }
}
