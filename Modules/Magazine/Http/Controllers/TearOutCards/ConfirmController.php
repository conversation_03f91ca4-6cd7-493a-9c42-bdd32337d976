<?php

namespace Modules\Magazine\Http\Controllers\TearOutCards;

use Illuminate\Http\Request;
use Modules\Magazine\Http\Controllers\Controller;

class ConfirmController extends Controller
{
    public function __invoke(Request $request)
    {
        return view('magazine::tear-out-cards.confirm', [
            'cardNumber' => $request->route('cardNumber'),
            'tocId'      => $request->route('tocId'),
        ]);
    }
}