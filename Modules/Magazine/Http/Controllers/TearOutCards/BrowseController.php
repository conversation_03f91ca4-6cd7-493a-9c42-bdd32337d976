<?php

namespace Modules\Magazine\Http\Controllers\TearOutCards;

use Illuminate\Http\Request;
use Modules\Magazine\Http\Controllers\Controller;
use Modules\Magazine\Contracts\TearOutCardsRepository;

class BrowseController extends Controller
{
    public function __invoke(Request $request, TearOutCardsRepository $repository)
    {
        $tocs = $repository->getSelectedTearOutCards();

        $cardNumber = (int) $request->route('cardNumber');

        return view('magazine::tear-out-cards.browse', [
            'cardNumber' => $cardNumber,
            'firstTocId' => $tocs->getFirstTocId(),
            'secondTocId' => $tocs->getSecondTocId(),
            'otherCardNumber' => $cardNumber === 1 ? 2 : 1,
        ]);
    }
}