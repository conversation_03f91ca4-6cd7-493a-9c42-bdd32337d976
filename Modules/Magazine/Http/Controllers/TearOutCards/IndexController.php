<?php

namespace Modules\Magazine\Http\Controllers\TearOutCards;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Modules\Magazine\Contracts\FutureMailingsRepository;
use Modules\Magazine\Contracts\MagazineRepository;
use Modules\Magazine\Contracts\TearOutCardsRepository;
use Modules\Magazine\Http\Controllers\Controller;

class IndexController extends Controller
{
    /** @var \Modules\Magazine\Contracts\MagazineRepository */
    private $repository;

    /** @var \Modules\Magazine\Contracts\FutureMailingsRepository */
    private $futureMailingsRepository;

    public function __construct(MagazineRepository $repository, FutureMailingsRepository $futureMailingsRepository)
    {
        $this->repository = $repository;
        $this->futureMailingsRepository = $futureMailingsRepository;
    }

    public function __invoke(Request $request, TearOutCardsRepository $repository)
    {
        $user = $request->user();
        $tocs = $repository->getSelectedTearOutCards();

        return Inertia::render(
            'Magazine/TearOutCards',
            [
                'pageTitle'          => 'Tear Out Cards',
                'firstTocId'         => $tocs->getFirstTocId(),
                'secondTocId'        => $tocs->getSecondTocId(),
                'defaultFirstTocId'  => $tocs->getDefaultFirstTocId(),
                'defaultSecondTocId' => $tocs->getDefaultSecondTocId(),
                'canBrowseFirstToc'  => $user->can('Alm.TearOutCards.SelectFirstToc'),
                'canBrowseSecondToc' => $user->can('Alm.TearOutCards.SelectSecondToc'),
                'printingLockout'    => $this->repository->getPrintingLockout($request->user()),
                'nextMailing'        => $this->futureMailingsRepository->getNextMailing(),
            ]
        );
    }
}
