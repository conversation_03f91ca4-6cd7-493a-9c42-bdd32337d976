<?php

namespace Modules\Magazine\Http\Middleware;

use App\Permissions\ViewBAM;
use Closure;
use Domain\Magazine\ValueObjects\Permissions\ViewALM;
use Domain\Magazine\ValueObjects\Permissions\ViewGTBHM;
use Domain\Magazine\ValueObjects\Permissions\ViewSHM;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\Magazine\Contracts\EnrollRepository;

class NotInMagazines
{
    /** @var \Modules\Magazine\Contracts\EnrollRepository */
    private $repository;

    /** @var Request */
    private $request;

    /** @var Closure */
    private $next;

    public function __construct(EnrollRepository $repository)
    {
        $this->repository = $repository;
    }

    public function handle(Request $request, Closure $next)
    {
        $this->request = $request;
        $this->next = $next;

        $hasSignedContracts = $this->repository->hasSignedContract();
        $canViewMagazines = $request->user()->can(ViewALM::toString()) ||
            $request->user()->can(ViewSHM::toString()) ||
            $request->user()->can(ViewGTBHM::toString()) ||
            $request->user()->can(ViewBAM::toString());

        if ($canViewMagazines && $hasSignedContracts) {
            return $this->redirectIfNeeded('magazine-dashboard.index');
        }

        if($canViewMagazines && !$hasSignedContracts) {
            if($request->user()->isEmployee()) {
                return $this->redirectIfNeeded('magazine-dashboard.index');
            } else {
                return $this->redirectIfNeeded('magazine.terms');
            }
        }

        if(!$canViewMagazines) {
            return $this->redirectIfNeeded('magazine.promo');
        }

        return $next($request);
    }

    public function redirectIfNeeded(string $destinationRoute)
    {
        $parameters = [];

        // If the current route is account bound, add the prefix
        if (Route::isAccountBound()) {
            $destinationRoute = 'account-bound.' . $destinationRoute;
            $parameters['account_slug'] = $this->request->route('account_slug');
        }

        // If the current route is NOT where we want to send the user to, redirect them there
        if (! Route::currentRouteNamed($destinationRoute)) {
            return redirect()->route($destinationRoute, $parameters);
        }

        // Continue on our merry way
        return ($this->next)($this->request);
    }
}
