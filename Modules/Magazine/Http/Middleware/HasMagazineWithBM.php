<?php

namespace Modules\Magazine\Http\Middleware;

use App\Permissions\ViewBAM;
use App\Permissions\ViewBrandedMagazine;
use Closure;
use Domain\Magazine\ValueObjects\Permissions\ViewALM;
use Domain\Magazine\ValueObjects\Permissions\ViewGTBHM;
use Domain\Magazine\ValueObjects\Permissions\ViewSHM;
use Illuminate\Http\Request;
use Modules\Magazine\Contracts\EnrollRepository;

class HasMagazineWithBM
{
    /** @var \Modules\Magazine\Contracts\EnrollRepository */
    private $repository;

    public function __construct(EnrollRepository $repository)
    {
        $this->repository = $repository;
    }

    public function handle(Request $request, Closure $next)
    {
        $hasSignedContracts = $this->repository->hasSignedContract();
        $canViewMagazines = $request->user()->can(ViewALM::toString()) ||
            $request->user()->can(ViewSHM::toString()) ||
            $request->user()->can(ViewGTBHM::toString()) ||
            $request->user()->can(ViewBAM::toString()) ||
            $request->user()->can(ViewBrandedMagazine::toString());

        if ($canViewMagazines && $hasSignedContracts) {
            return $next($request);
        }

        if ($canViewMagazines && !$hasSignedContracts) {
            if ($request->user()->isEmployee()) {
                return $next($request);
            } else {
                return redirect(rmc_route('magazine.terms'));
            }
        }

        //if the user can view Branded Magazine, we need to redirect to there
        if ($request->user()->can(ViewBrandedMagazine::toString())) {
            //TODO verify route
            return redirect(rmc_route('branded-magazine.index'));
        }

        return redirect(rmc_route('magazine.promo'));
    }
}
