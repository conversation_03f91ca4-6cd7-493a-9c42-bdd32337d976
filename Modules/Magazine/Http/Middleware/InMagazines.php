<?php

namespace Modules\Magazine\Http\Middleware;

use App\Context\AccountId;
use App\Permissions\ViewBAM;
use App\Permissions\ViewBrandedMagazine;
use Closure;
use Domain\Magazine\ValueObjects\Permissions\ViewALM;
use Domain\Magazine\ValueObjects\Permissions\ViewGTBHM;
use Domain\Magazine\ValueObjects\Permissions\ViewSHM;
use Illuminate\Http\Request;
use Infrastructure\Repositories\Printing\ApiPrintingRepository;
use Modules\Magazine\Contracts\EnrollRepository;

class InMagazines
{
    /** @var \Modules\Magazine\Contracts\EnrollRepository */
    private $repository;

    /** @var Infrastructure\Repositories\Printing\ApiPrintingRepository */
    private $printingRepository;

    public function __construct(EnrollRepository $repository, ApiPrintingRepository $printingRepository)
    {
        $this->repository = $repository;
        $this->printingRepository = $printingRepository;
    }

    public function handle(Request $request, Closure $next)
    {
        $hasSignedContracts = $this->repository->hasSignedContract();
        $canViewMagazines = $request->user()->can(ViewALM::toString()) ||
            $request->user()->can(ViewSHM::toString()) ||
            $request->user()->can(ViewGTBHM::toString()) ||
            $request->user()->can(ViewBAM::toString());
        $currentlyMigrating = false;
        if ($account = AccountId::current()) {
            $currentlyMigrating = $this->printingRepository->getUserIsCurrentlyMigrating($account->id());
        }

        if ($canViewMagazines && $hasSignedContracts) {
            if ($currentlyMigrating) {
                return redirect(rmc_route('ongoing-migration'));
            } else {
                return $next($request);
            }
        }

        if ($canViewMagazines && !$hasSignedContracts) {
            if ($request->user()->isEmployee()) {
                return $next($request);
            } else {
                return redirect(rmc_route('magazine.terms'));
            }
        }

        //if the user can view Branded Magazine, we can redirect to Mailing History Archive
        if ($request->user()->can(ViewBrandedMagazine::toString())) {
            return $next($request);
        }

        return redirect(rmc_route('magazine.promo'));
    }
}
