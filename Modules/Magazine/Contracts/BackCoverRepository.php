<?php

namespace Mo<PERSON>les\Magazine\Contracts;

use Modules\Magazine\Entities\BackCoverData;
use Modules\Magazine\Entities\BackCoverMetadata;
use Modules\Magazine\Entities\BackCoverPreview;

interface BackCoverRepository
{
    public function getCoverData() : BackCoverData;

    public function getMetadata() : BackCoverMetadata;

    public function getCoverPreview(?string $adName = null, bool $isInGlobalAds = false) : BackCoverPreview;

    public function setCover(string $adName, bool $isCustom) : bool;
}