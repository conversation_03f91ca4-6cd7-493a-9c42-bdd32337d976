<?php

namespace Modules\Magazine\Contracts;

use Modules\Magazine\Entities\FrontCover;

interface FrontCoverRepository
{
    public function getFrontCover($loadSinglePreviewId = null, $largePreviews = false, $selectedOnly = false) : FrontCover;

    public function getSelectedFrontCover($largePreviews = false) : ?iterable;

    public function getFrontCoverPreviewById($id) : ?iterable;

    public function setFrontCover(int $frontCoverId, bool $isSpecialEdition) : bool;
}
