<?php

namespace Modules\Magazine\Contracts;

use Carbon\Carbon;
use Domain\Contacts\DTO\ContactGroupUuid;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;
use Modules\Magazine\Entities\CheckAddressDTO;
use Modules\Magazine\Entities\MailingList;
use Modules\Magazine\Entities\Recipient;
use Modules\Magazine\Entities\RecipientPhoto;
use Modules\Magazine\Entities\SaveRecipientDTO;

interface RecipientsRepository
{
    public function getMyMailingList(?int $groupId = null, ?int $accountId = null) : MailingList;

    public function getMyMailingListReport(?int $groupId = null, ?int $accountId = null) : array;

    public function getRecipient(int $id) : Recipient;

    public function getRecipientsThatMayLoseExclusivityIfTransferredToGroup(int $newGroupId, iterable $recipientIds) : array;

    public function saveRecipientGroup(?int $id, string $name, ?int $accountId = null) : int;

    public function deleteRecipientGroup(int $id) : bool;

    public function disablePrintingGroup(int $groupId) : bool;

    public function enablePrintingGroup(int $groupId) : bool;

    public function massDeleteRecipients(iterable $ids) : bool;

    public function moveRecipients(iterable $recipientIds, int $groupId) : bool;

    public function createRecipient(SaveRecipientDTO $dto) : bool;

    public function updateRecipient(int $id, SaveRecipientDTO $dto) : bool;

    public function checkAddress(string $line1, string $city, string $state, string $zip) : CheckAddressDTO;

    public function getCoversUsingRecipientPhotos() : array;

    public function getRecipientPhoto(int $recipientId) : RecipientPhoto;

    public function uploadPhoto(int $recipientId, UploadedFile $file) : bool;

    public function deletePhoto(int $photoId) : bool;

    public function toggleRecipientPhotoOnCovers(iterable $covers = []) : bool;

    public function getRecipientsForMailingBetweenDates(Carbon $startDate, Carbon $endDate): Collection;

    public function getLastIssueRecipients(): Collection;
}
