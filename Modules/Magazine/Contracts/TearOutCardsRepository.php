<?php

namespace Modules\Magazine\Contracts;

use Illuminate\Support\Collection;
use Modules\Magazine\Entities\SelectedTearOutCards;
use Modules\Magazine\Entities\TearOutCardPreview;

interface TearOutCardsRepository
{
    public function getSelectedTearOutCards() : SelectedTearOutCards;

    public function getTearOutCardPreview(string $tocId) : TearOutCardPreview;

    public function getCategories() : Collection;

    public function getAds(int $categoryId) : Collection;

    public function setTearOutCard(int $cardNumber, string $tocId, bool $isCustom) : bool;
}