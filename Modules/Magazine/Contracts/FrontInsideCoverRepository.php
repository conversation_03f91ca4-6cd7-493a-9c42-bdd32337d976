<?php

namespace Modules\Magazine\Contracts;

use Modules\Magazine\Entities\LetterSignatures;
use Modules\Magazine\Entities\PromotionalCopyOrder;
use Modules\Xrms\Entities\RecipientLetter;

interface FrontInsideCoverRepository
{
    public function getFrontInsideCoverData(int $recipientGroupId) : array;

    public function getPromotionalCopiesLetter(): array;

    public function getPromotionalCopyData() : ?PromotionalCopyOrder;

    public function getRecipientGroups() : array;

    public function getPastLetters(int $recipientGroupId) : array;

    public function updatePastLetter(int $recipientGroupId, string $letterContent) : bool;

    public function updateRecipientLetter(RecipientLetter $letter, LetterSignatures $signatures, bool $addTrademark, iterable $recipientGroupIds) : bool;

    public function updatePromotionalCopyOrder(PromotionalCopyOrder $order) : bool;

    public function updatePromotionalLetter(RecipientLetter $letter, LetterSignatures $signatures, bool $addTrademark, array $recipientGroupIds) : bool;

    public function resetPromotionalCopyLetterToDefault() : bool;

    public function resetRecipientGroupLetterToDefault(int $issueId, int $recipientGroupId) : bool;
}
