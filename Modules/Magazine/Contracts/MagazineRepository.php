<?php

namespace Modules\Magazine\Contracts;

use App\Models\Plan;
use Illuminate\Contracts\Auth\Access\Authorizable;
use Carbon\Carbon;
use Modules\Magazine\Entities\CustomizedFields;
use Modules\Magazine\Entities\Issue;
use Modules\Magazine\Entities\PrintingLockout;

interface MagazineRepository
{
    public function getCurrentIssue() : Issue;

    public function getCustomizedFields() : CustomizedFields;

    public function setCoverPhoto(string $page, ?int $magazineFileGroupId) : bool;

    public function getPrintingLockout(Authorizable $user = null) : PrintingLockout;

    public function getMagazineProduct(): ?Plan;

    public function getEarliestDeadlineBetweenDates(Carbon $startDate, Carbon $endDate): ?Carbon;
}
