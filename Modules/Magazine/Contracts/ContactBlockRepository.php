<?php

namespace Modules\Magazine\Contracts;

use Modules\Magazine\Entities\ContactBlockEditData;
use Mo<PERSON>les\Magazine\Entities\ContactBlockPreview;
use Modules\Magazine\Entities\OfficeLogo;

interface ContactBlockRepository
{
    public function updateContactBlockEditData(ContactBlockEditData $contactBlockEditData) : bool;

    public function getContactBlockPreview() : ContactBlockPreview;

    public function getContactOfficeLogoPath() : ?OfficeLogo;
}