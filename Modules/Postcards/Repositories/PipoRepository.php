<?php

namespace Mo<PERSON>les\Postcards\Repositories;

use App\Models\PrintNowAccount;
use GuzzleHttp\Exception\BadResponseException;
use GuzzleHttp\Exception\TransferException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Modules\Postcards\Entities\Customer;
use Modules\Postcards\Entities\RegisteredAccount;
use Modules\Postcards\Entities\UnregisteredAccount;
use Modules\Postcards\Exceptions\PipoRepository\CannotCreateUserException;
use Modules\Postcards\Exceptions\PipoRepository\CustomerNotFoundException;
use Modules\Postcards\Exceptions\PipoRepository\PipoLoginException;
use Modules\Postcards\Exceptions\PipoRepository\PipoLogoutException;
use Modules\Postcards\Exceptions\PipoRepository\PipoTokenException;
use Modules\Postcards\ValueObjects\CustomerId;
use Modules\Postcards\ValueObjects\PipoToken;

class PipoRepository extends PrintNowRepository
{
    public function createUser(UnregisteredAccount $user) : RegisteredAccount
    {
        $parameters = array_merge(
            [
                'storefront_id' => $this->storefront->getValue(),
            ],
            $user->toArray()
        );

        try {
            $this->client->post(
                "pipo/user/create",
                $parameters
            );
        } catch (TransferException $e) {
            if (! $e instanceof BadResponseException) {
                Log::info(sprintf("Could not create user %s", $e->getMessage()));
            } else if ($e->getResponse()->getStatusCode() === Response::HTTP_CONFLICT) {
                /**
                 * This means the account already exists in PrintNow and since we dictate the
                 * login credentials, just create the account record.
                 */
                Log::error(sprintf("Duplicate PrintNow Customer: %s", $e->getMessage()));
                return new RegisteredAccount(
                    $user->getUsername(),
                    $user->getPassword()
                );
            }
            throw CannotCreateUserException::cannotCreateUser($e->getResponse()->getBody());
        }

        return new RegisteredAccount(
            $user->getUsername(),
            $user->getPassword()
        );
    }

    public function loginUser(PrintNowAccount $account) : PipoToken
    {
        $parameters = array_merge(
            [
                'storefront_id' => $this->storefront->getValue(),
            ],
            [
                'username' => $account->username,
                'password' => $account->getReadablePassword(),
            ]
        );

        try {
            $response = $this->client->post(
                "pipo/user/login",
                $parameters
            );
        } catch (TransferException $e) {
            if (! $e instanceof BadResponseException) {
                Log::info(sprintf("Could not log customer in %s", $e->getMessage()));
            }
            throw PipoLoginException::loginFailed($e->getResponse()->getBody());
        }

        $data = $response->getBody();

        return new PipoToken(array_get($data, 'value'));
    }

    public function logoutUser(PrintNowAccount $account) : void
    {
        try {
            $this->client->get(
                'pipo/user/logout',
                [],
                $this->addPrintNowUserToken($account)
            );
        } catch (TransferException $e) {
            if (! $e instanceof BadResponseException) {
                Log::info(sprintf("Could not log customer out %s", $e->getMessage()));
            }
            $message = $e->getResponse() ? $e->getResponse()->getBody() : $e->getMessage();
            throw PipoLogoutException::logoutFailed($message);
        }
    }

    public function getCustomerInfo(PrintNowAccount $account) : Customer
    {
        try {
            $response = $this->client->get(
                'pipo/user/info',
                [],
                $this->addPrintNowUserToken($account)
            );
        } catch (TransferException $e) {
            if (! $e instanceof BadResponseException) {
                Log::info(sprintf("Could not get customer info %s", $e->getMessage()));
            }
            throw CustomerNotFoundException::notFound($e->getResponse()->getBody());
        }

        $data = $response->getBody();

        return new Customer(
            new CustomerId(array_get($data, 'id')),
            array_get($data, 'username'),
            array_get($data, 'first_name'),
            array_get($data, 'last_name')
        );
    }

    /**
     * @throws CustomerNotFoundException
     */
    public function getCustomerInfoById(CustomerId $customerId): Customer
    {
        try {
            $response = $this->client->get('customer/'.$customerId->getValue(),[]);
        } catch (TransferException $e) {
            if (! $e instanceof BadResponseException) {
                Log::info(sprintf("Could not get customer info by id %s", $e->getMessage()));
            }
            throw CustomerNotFoundException::notFound($e->getResponse()->getBody());
        }

        $data = $response->getBody();

        return new Customer(
            new CustomerId(array_get($data, 'id')),
            array_get($data, 'username'),
            array_get($data, 'first_name'),
            array_get($data, 'last_name')
        );
    }

    public function tokenValid(PrintNowAccount $account) : bool
    {
        try {
            $response = $this->client->get(
                'pipo/user/loggedin',
                [],
                $this->addPrintNowUserToken($account)
            );
        } catch (TransferException $e) {
            if (! $e instanceof BadResponseException) {
                Log::info(sprintf("Could not validate Token %s", $e->getMessage()));
            }
            $message = $e->getResponse() ? $e->getResponse()->getBody() : $e->getMessage();
            throw PipoTokenException::tokenValidationFailed($message);
        }

        $data = $response->getBody();

        return array_get($data, 'value');
    }

    private function addPrintNowUserToken(PrintNowAccount $account)
    {
        return [
            'X-PN-TOKEN' => "{$account->pipo_token}",
        ];
    }
}
