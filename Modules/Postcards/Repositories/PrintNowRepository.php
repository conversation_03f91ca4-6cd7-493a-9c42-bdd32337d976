<?php

namespace Modules\Postcards\Repositories;

use Modules\Postcards\Api\PrintNowApi;
use Modules\Postcards\Contracts\PrintNowRepositoryContract;
use Modules\Postcards\ValueObjects\PrintNowStoreFront;

abstract class PrintNowRepository implements PrintNowRepositoryContract
{
    /**
     * @var PrintNowApi
     */
    protected $client;

    /**
     * @var PrintNowStoreFront
     */
    protected $storefront;

    public function __construct(PrintNowApi $client, PrintNowStoreFront $printNowStoreFront)
    {
        $this->client = $client;

        $this->storefront = $printNowStoreFront;
    }
}