<?php

namespace Modules\Postcards\Providers;

use Infrastructure\Http\AbstractRouteServiceProvider;

class RouteServiceProvider extends AbstractRouteServiceProvider
{
    /**
     * This namespace is applied to your controller routes.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'Modules\Postcards\Http\Controllers';

    public function routeDirectory() : string
    {
        return module_path('Postcards') . '/routes';
    }
}
