<?php

namespace Modules\Postcards\Providers;

use Faker\Generator;
use Illuminate\Database\Eloquent\Factory;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;
use Modules\Postcards\Api\ApiResponseParser;
use Infrastructure\Repositories\RestRepository\RestClient;
use Infrastructure\Traits\CreatesGuzzleClients;
use Modules\Postcards\Api\PrintNowApi;
use Modules\Postcards\Contracts\PrintNowRepositoryContract;
use Modules\Postcards\ValueObjects\AuthorizationKey;
use Modules\Postcards\ValueObjects\PrintNowRedirectUrl;
use Modules\Postcards\ValueObjects\PrintNowStoreFront;

class PostcardsServiceProvider extends ServiceProvider
{
    use CreatesGuzzleClients;

    /**
     * Indicates if loading of the provider is deferred.
     *
     * @var bool
     */
    protected $defer = false;

    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->registerFactories();
        $this->loadMigrationsFrom(__DIR__ . '/../Database/Migrations');
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind(
            PrintNowStoreFront::class,
            function () {
                $storefrontId = config('postcards.storefront_id');

                return new PrintNowStoreFront($storefrontId);
            }
        );

        $this->app->bind(
            AuthorizationKey::class,
            function () {
                $token = config('postcards.api.token');
                $key = config('postcards.api.key');

                return new AuthorizationKey($token, $key);
            }
        );

        $this->app->bind(
            PrintNowRedirectUrl::class,
            function () {
                $url = config('postcards.redirect.default');

                return new PrintNowRedirectUrl($url);
            }
        );

        $this->app->bind(
            PrintNowApi::class,
            function () {
                $key = $this->app->make(AuthorizationKey::class);

                $options = array_merge(
                    config('postcards.api.rest_options'),
                    [
                        'headers' => [
                            'Authorization' => "Basic " . (string)$key,
                        ],
                        'debug'   => false,
                    ]
                );

                return new PrintNowApi(
                    new RestClient($this->buildClient($options), new ApiResponseParser),
                    Log::getFacadeRoot()
                );
            }
        );

        $this->app->when(PrintNowRepositoryContract::class)
            ->needs(PrintNowApi::class)
            ->give(
                function () {
                    return $this->app->make(PrintNowApi::class);
                }
            );
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes(
            [
                __DIR__ . '/../Config/config.php' => config_path('postcards.php'),
            ],
            'config'
        );
        $this->mergeConfigFrom(
            __DIR__ . '/../Config/config.php',
            'postcards'
        );
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews()
    {
        $viewPath = resource_path('views/modules/postcards');

        $sourcePath = __DIR__ . '/../Resources/views';

        $this->publishes(
            [
                $sourcePath => $viewPath,
            ],
            'views'
        );

        $paths = collect(Config::get('view.paths'))
            ->map(function ($path) {
                return $path . '/modules/postcards';
            })
            ->merge([$sourcePath])
            ->filter(function ($path) {
                return is_dir($path);
            })->toArray();

        $this->loadViewsFrom($paths, 'postcards');
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/postcards');

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'postcards');
        } else {
            $this->loadTranslationsFrom(__DIR__ . '/../Resources/lang', 'postcards');
        }
    }

    /**
     * Register an additional directory of factories.
     *
     * @return void
     */
    public function registerFactories()
    {
        if (! class_exists(Generator::class)) {
            return;
        }

        app(Factory::class)->load(__DIR__ . '/../Database/factories');
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }
}
