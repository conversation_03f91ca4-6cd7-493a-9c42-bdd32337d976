<?php

namespace Modules\Postcards\Services;

use App\Models\Account;
use App\Models\PrintNowAccount;
use App\Repositories\PrintNowAccountRepository;
use Exception;
use Modules\Postcards\Entities\UnregisteredAccount;
use Modules\Postcards\Exceptions\PipoRepository\CustomerNotFoundException;
use Modules\Postcards\Exceptions\PipoRepository\PipoException;
use Modules\Postcards\Exceptions\PrintNowService\PrintNowServiceException;
use Modules\Postcards\Repositories\PipoRepository;
use Modules\Postcards\ValueObjects\CustomerId;
use Modules\Postcards\ValueObjects\PipoToken;

class PrintNowService
{
    /** @var PipoRepository $pipoRepository */
    private $pipoRepository;

    /** @var PrintNowAccountRepository $accountRepository */
    private $accountRepository;

    public function __construct(PipoRepository $pipoRepository, PrintNowAccountRepository $accountRepository)
    {
        $this->pipoRepository = $pipoRepository;

        $this->accountRepository = $accountRepository;
    }

    /**
     * @param Account $account
     * @return PrintNowAccount
     * @throws PrintNowServiceException
     */
    public function createAccountOrFindExisting(Account $account) : PrintNowAccount
    {
        if ($printNowAccount = $this->accountRepository->findFromAccount($account)) {
            return $printNowAccount;
        }

        $owner = $account->getOwner();

        $unregisteredAccount = new UnregisteredAccount(
            $account->getAttribute('slug'),
            $account->getAttribute('id'),
            $owner->getEmail(),
            $owner->getFirstName(),
            $owner->getLastName()
        );

        try {
            $registeredAccount = $this->pipoRepository->createUser($unregisteredAccount);
        } catch (PipoException $e) {
            throw new PrintNowServiceException($e->getMessage());
        }

        $printNowAccount = $this->accountRepository->addPrintNowAccount($account, $registeredAccount);

        $this->ensureAccountHasCustomerId($printNowAccount);

        return $printNowAccount;
    }

    /**
     * @throws Exception
     */
    public function updatePrintNowAccount(Account $account, CustomerId $customerId): bool
    {
        $printNowAccount = $this->accountRepository->findByCustomerId($customerId);

        //if a record is found by customerId update the account ID to the current account
        // and if the current account has a record already, remove it
        if ($printNowAccount) {
            // remove the old print now account record for the current account
            if (!$this->accountRepository->deletePrintNowAccount($account)) {
                return false;
            }
            // assign the current account to the printNowAccount found by customerId
            return $this->accountRepository->setAccountId($printNowAccount, $account);
        }

        // if a record is not found and the current account has a record,
        // then update the current record with the customer ID
        $printNowAccount = $this->accountRepository->findFromAccount($account);

        return $this->accountRepository->setCustomerIdFor($printNowAccount, $customerId);
    }

    public function verifyCustomerId(CustomerId $customerId): bool
    {
        try {
            return (bool) $this->pipoRepository->getCustomerInfoById($customerId);
        } catch (CustomerNotFoundException $e) {
            return false;
        }
    }

    /**
     * @param Account $account
     * @return PipoToken
     * @throws PrintNowServiceException
     */
    public function loginUser(Account $account) : PipoToken
    {
        $printNowAccount = $this->createAccountOrFindExisting($account);
        try {
            if (! $this->pipoRepository->tokenValid($printNowAccount)) {
                return $this->retrievePipoToken($printNowAccount);
            }
        } catch (PipoException $e) {
            throw new PrintNowServiceException($e->getMessage());
        }

        if ($token = $printNowAccount->getPipoToken()) {
            return $token;
        }

        return $this->retrievePipoToken($printNowAccount);
    }

    /**
     * @param Account $account
     * @throws PrintNowServiceException
     */
    public function logoutUser(Account $account) : void
    {
        // If they don't have a print now account, we don't need to log them out
        if (! $printNowAccount = $this->accountRepository->findFromAccount($account)) {
            return;
        }

        // If they don't have PipoToken, then they never logged in, so we don't need to do anything.
        if (! $printNowAccount->getPipoToken()) {
            return;
        }

        try {
            $this->pipoRepository->logoutUser($printNowAccount);
        } catch (PipoException $e) {
            throw new PrintNowServiceException($e->getMessage());
        }

        $this->accountRepository->removePipoToken($printNowAccount);
    }

    /**
     * @param PrintNowAccount $printNowAccount
     * @return PipoToken
     * @throws PrintNowServiceException
     */
    protected function retrievePipoToken(PrintNowAccount $printNowAccount) : PipoToken
    {
        try {
            $token = $this->pipoRepository->loginUser($printNowAccount);
        } catch (PipoException $e) {
            throw new PrintNowServiceException($e->getMessage());
        }

        $this->accountRepository->updatePipoToken($printNowAccount, $token);

        return $token;
    }

    /**
     * @param PrintNowAccount
     * @throws PrintNowServiceException
     */
    protected function ensureAccountHasCustomerId(PrintNowAccount $printNowAccount) : void
    {
        if ($printNowAccount->hasCustomerId()) {
            return;
        }

        if (! $printNowAccount->getPipoToken()) {
            $this->retrievePipoToken($printNowAccount);
        }

        try {
            $customer = $this->pipoRepository->getCustomerInfo($printNowAccount);
        } catch (PipoException $e) {
            throw new PrintNowServiceException($e->getMessage());
        }

        $this->accountRepository->setCustomerIdFor($printNowAccount, $customer->getId());
    }
}
