<?php

namespace Modules\Postcards\EventListeners;

use Exception;
use Illuminate\Auth\Events\Logout;
use Illuminate\Contracts\Queue\ShouldQueue;
use Modules\Postcards\Services\PrintNowService;

class UserLoggedOutListener implements ShouldQueue
{
    /** @var PrintNowService */
    private $service;

    public function __construct(PrintNowService $service)
    {
        $this->service = $service;
    }

    public function handle(Logout $event) : void
    {
        try {
            $user = $event->user;

            $account = $user->getDefaultAccount();

            if (! $account) {
                return;
            }

            $this->service->logoutUser($account);
        } catch (Exception $exception) {
            // TODO: Log info
        }
    }
}