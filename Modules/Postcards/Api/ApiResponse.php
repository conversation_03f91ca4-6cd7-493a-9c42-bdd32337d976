<?php

namespace Modules\Postcards\Api;

use Psr\Http\Message\ResponseInterface;

class ApiResponse
{
    /** @var \Psr\Http\Message\ResponseInterface|null */
    private $response;

    /** @var bool */
    private $success;

    /** @var array */
    private $body;

    /** @var array */
    private $included;

    public function __construct(bool $success, $body, array $included = [])
    {
        $this->success = $success;
        $this->body = $body;
        $this->included = $included;
    }

    public function getResponse() : ?ResponseInterface
    {
        return $this->response;
    }

    public function isSuccessful() : bool
    {
        return $this->success;
    }

    public function getBody() : array
    {
        return $this->body;
    }

    public function getIncluded() : array
    {
        return $this->included;
    }

    public static function fromResponse(ResponseInterface $response) : self
    {
        $data = self::extractBody($response);

        if (empty($data)) {
            $success = true;
            $body = [];
            $included = [];
        } else {
            if ($response->getStatusCode() < 400) {
                $success = true;
                $body = array_get($data, 'data') ?? $data;
                $included = array_get($data, 'included') ?? [];
            } else {
                $success = false;
                $body = $data;
                $included = [];
            }
        }

        return new self($success, $body, $included);
    }

    private static function extractBody(ResponseInterface $response) : array
    {
        $data = json_decode($response->getBody(), true);

        if (is_bool($data)) {
            return [
                'value' => filter_var($data, FILTER_VALIDATE_BOOLEAN)
            ];
        }

        if (json_last_error() === JSON_ERROR_SYNTAX || is_string($data)) {
            // If the response is not JSON, then create an array and put the data in the 'value' index.
            return [
                'value' => $data,
            ];
        }

        return $data;
    }
}