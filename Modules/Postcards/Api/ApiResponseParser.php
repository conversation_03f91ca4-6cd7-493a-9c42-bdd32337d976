<?php

namespace Modules\Postcards\Api;

use Infrastructure\Api\Exceptions\GeneralException;
use Infrastructure\Repositories\RestRepository\ResponseParser;
use Psr\Http\Message\ResponseInterface;

class ApiResponseParser implements ResponseParser
{
    public function parse(ResponseInterface $response) : ApiResponse
    {
        $apiResponse = ApiResponse::fromResponse($response);

        if (! $apiResponse->isSuccessful()) {
            throw new GeneralException("{$response->getBody()}");
        }

        return $apiResponse;
    }
}