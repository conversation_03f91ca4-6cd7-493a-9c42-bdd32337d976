<?php

namespace Modules\Postcards\Entities;

use Illuminate\Contracts\Support\Arrayable;

class UnregisteredAccount implements Arrayable
{
    /**
     * @var string
     */
    private $username;

    /**
     * @var int
     */
    private $accountId;

    /**
     * @var string
     */
    private $email;

    /**
     * @var string
     */
    private $firstName;

    /**
     * @var string
     */
    private $lastName;

    public function __construct(string $username, int $accountId, string $email, string $firstName, string $lastName)
    {
        $this->username = $username;

        $this->accountId = $accountId;

        $this->email = $email;

        $this->firstName = $firstName;

        $this->lastName = $lastName;
    }

    public function getUsername() : string
    {
        return $this->username;
    }

    public function getPassword() : string
    {
        $password = str_replace(" ", "", $this->username) . $this->accountId;

        return $password;
    }

    public function getEmail() : string
    {
        return $this->email;
    }

    public function getFirstName() : string
    {
        return $this->firstName;
    }

    public function getLastName() : string
    {
        return $this->lastName;
    }

    public function toArray() : array
    {
        return [
            'username'   => $this->username,
            'password'   => $this->getPassword(),
            'email'      => $this->email,
            'first_name' => $this->firstName,
            'last_name'  => $this->lastName,
        ];
    }
}