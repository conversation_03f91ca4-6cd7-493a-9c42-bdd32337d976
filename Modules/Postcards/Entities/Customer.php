<?php

namespace Modules\Postcards\Entities;

use Infrastructure\Entities\Entity;
use Modules\Postcards\ValueObjects\CustomerId;

class Customer extends Entity
{
    /** @var string $username */
    private $username;

    /** @var string $firstName */
    private $firstName;

    /** @var string $lastName */
    private $lastName;

    public function __construct(CustomerId $id, string $username, string $firstName, string $lastName)
    {
        $this->id = $id;

        $this->username = $username;

        $this->firstName = $firstName;

        $this->lastName = $lastName;
    }

    public function getId() : CustomerId
    {
        return $this->id;
    }

    public function getUsername() : string
    {
        return $this->username;
    }

    public function getFirstName() : string
    {
        return $this->firstName;
    }

    public function getLastName() : string
    {
        return $this->lastName;
    }
}