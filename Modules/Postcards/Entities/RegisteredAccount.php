<?php

namespace Modules\Postcards\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Infrastructure\Contracts\EntityContract;
use Infrastructure\Entities\Entity;
use Modules\Postcards\ValueObjects\AccountToken;

class RegisteredAccount extends Entity implements Arrayable, EntityContract
{
    /** @var string $username */
    private $username;

    /** @var string $password */
    private $password;

    /** @var \Modules\Postcards\ValueObjects\AccountToken $authKey */
    private $authKey;

    public function __construct(string $username, string $password)
    {
        $this->username = $username;

        $this->password = $password;

        $this->authKey = AccountToken::fromCredentials($username, $password);
    }

    public function getUsername() : string
    {
        return $this->username;
    }

    public function getBase64Password() : string
    {
        return base64_encode($this->password);
    }

    public function getKey() : AccountToken
    {
        return $this->authKey;
    }

    public function sameIdentityAs(EntityContract $entity) : bool
    {
        return ($entity instanceof self)
               && $this->password == $entity->password
               && $this->username == $entity->username;
    }

    public function toArray() : array
    {
        return [
            'username' => $this->username,
            'key'      => $this->getKey(),
        ];
    }
}