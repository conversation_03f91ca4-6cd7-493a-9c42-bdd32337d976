<?php

namespace Modules\Postcards\Http\Controllers;

use App\Contracts\AccountRepository;
use App\Contracts\UserRepository;
use Illuminate\Routing\Controller;
use Inertia\Inertia;
use Modules\Postcards\Exceptions\PrintNowService\PrintNowServiceException;
use Modules\Postcards\Http\Requests\PostcardsLoginRequest;
use Modules\Postcards\Services\PrintNowService;

class PostcardsController extends Controller
{
    private $service;

    /** @var \App\Contracts\AccountRepository */
    private $accountRepository;

    public function __construct(PrintNowService $service, AccountRepository $accountRepository)
    {
        $this->service = $service;
        $this->accountRepository = $accountRepository;
    }

    public function index(PostcardsLoginRequest $request, UserRepository $userRepository)
    {
        $account = $this->accountRepository->currentAccount();

        try {
            $token = $this->service->loginUser($account);
        } catch (PrintNowServiceException $e) {
            return redirect($account->slug . "/dashboard")
                ->with(
                    'error',
                    "We are currently experiencing login issue with our Postcard storefront. " .
                    "Please contact your customer support representative for further assistance."
                );
        }

        return redirect($request->redirectTo($token));
    }
}
