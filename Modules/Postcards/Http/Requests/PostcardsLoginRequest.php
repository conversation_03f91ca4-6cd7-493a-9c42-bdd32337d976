<?php

namespace Modules\Postcards\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\Postcards\ValueObjects\PipoToken;
use Modules\Postcards\ValueObjects\PrintNowRedirectUrl;

class PostcardsLoginRequest extends FormRequest
{
    public function rules() : array
    {
        return [];
    }

    public function authorize() : bool
    {
        return true;
    }

    public function redirectTo(PipoToken $token) : string
    {
        if ($redirect = $this->get('return_url')) {
            // Don't redirect to these pages and instead redirect to the default URL
            if ($this->isInvalidUrl($redirect)) {
                return $this->getDefaultRedirectUrl($token);
            }

            return $this->addTokenQueryParameter($redirect) . (string)$token;
        }

        return $this->getDefaultRedirectUrl($token);
    }

    private function addTokenQueryParameter(string $url) : string
    {
        if (!empty(parse_url($url, PHP_URL_QUERY))) {
            return "{$url}&token=";
        }

        return "{$url}?token=";
    }

    private function isInvalidUrl(string $url) : bool
    {
        $invalidPaths = [
            "my-account" // This page will display and 'logout' modal if we attempt to redirect here.
        ];

        $path = parse_url($url, PHP_URL_PATH);

        return in_array($path, $invalidPaths) && !$this->hasSubPages($path);
    }

    private function getDefaultRedirectUrl(PipoToken $token) : string
    {
        $url = (string)$this->container->make(PrintNowRedirectUrl::class);

        return $this->addTokenQueryParameter($url) . (string)$token;
    }

    private function hasSubPages(string $path) : bool
    {
        $parts = explode("/", $path);

        $parts = array_filter($parts, function ($part) {
            return !empty($part);
        });

        return count($parts) > 1;
    }
}
