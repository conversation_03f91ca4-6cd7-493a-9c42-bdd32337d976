<?php

namespace Modules\Postcards\ValueObjects;

use Infrastructure\Contracts\ValueObjectContract;
use Infrastructure\ValueObjects\ValueObject;

class PipoToken extends ValueObject implements ValueObjectContract
{
    public function __construct(string $value)
    {
        $this->value = self::cleanToken($value);
    }

    public function getValue() : string
    {
        return $this->value;
    }

    private static function cleanToken(string $token) : string
    {
        return str_replace('"', "", trim($token));
    }

    public function __toString() : string
    {
        return $this->value;
    }
}