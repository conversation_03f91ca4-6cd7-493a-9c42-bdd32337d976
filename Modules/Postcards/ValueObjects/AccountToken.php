<?php

namespace Modules\Postcards\ValueObjects;

use Infrastructure\ValueObjects\ValueObject;

class AccountToken extends ValueObject
{
    private function __construct(string $username, string $password)
    {
        $this->value = base64_encode("{$username}:{$password}");
    }

    public static function fromCredentials(string $username, string $password) : self
    {
        return new self($username, $password);
    }

    public function __toString() : string
    {
        return $this->value;
    }

    public function getValue() : string
    {
        return $this->value;
    }
}