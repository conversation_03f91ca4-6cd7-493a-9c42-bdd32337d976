<?php

namespace Modules\Postcards\ValueObjects;

use Infrastructure\Contracts\EntityContract;
use Infrastructure\Entities\Entity;

class AuthorizationKey extends Entity
{
    /** @var string */
    private $token;

    /** @var string */
    private $key;

    public function __construct(string $token, string $key)
    {
        $this->token = $token;

        $this->key = $key;
    }

    public function __toString() : string
    {
        return base64_encode("{$this->token}:{$this->key}");
    }

    public function sameIdentityAs(EntityContract $entity) : bool
    {
        return ($entity instanceof self)
               && $this->key == $entity->key
               && $this->token == $entity->token;
    }
}