<?php

namespace Modules\Postcards\ValueObjects;

use Infrastructure\Contracts\ValueObjectContract;
use Infrastructure\ValueObjects\ValueObject;

class PrintNowStoreFront extends ValueObject
{
    public function __construct(int $value)
    {
        $this->value = $value;
    }

    public function getValue() : int
    {
        return $this->value;
    }

    public function sameValueAs(ValueObjectContract $object) : bool
    {
        return ($object instanceof self)
               && $this->value == $object->value;
    }
}