<?php

namespace Modules\Postcards\Tests\Feature;

use Tests\RefreshDatabase;
use App\Models\Account;
use App\Models\PrintNowAccount;
use App\Models\User;
use App\Repositories\PrintNowAccountRepository;
use Modules\Postcards\Services\PrintNowService;
use Modules\Postcards\ValueObjects\AccountToken;
use Modules\Postcards\ValueObjects\PipoToken;
use Tests\TestCase;

class PrintNowServiceTest extends TestCase
{
    use RefreshDatabase;

    /**
     * @test
     */
    public function it_can_create_a_user()
    {
        $this->markTestSkipped('This creates a print now user, so be careful when removing the skip.');
        $account = \Mockery::mock(Account::class);
        $user = \Mockery::mock(User::class);
        $printNowAccount = new PrintNowAccount(
            [
                'username' => 'testing',
                'password' => 'testing1',
            ]
        );

        $user->shouldReceive('getEmail')
             ->andReturn("<EMAIL>");

        $user->shouldReceive('getFirstName')
             ->andReturn("Andrewtest");

        $user->shouldReceive('getLastName')
             ->andReturn('CampbellTest');

        $account->shouldReceive('getAccountOwner')
                ->andReturn($user);

        $account->shouldReceive('getAttribute')
                ->with('slug')
                ->andReturn('testing');

        $account->shouldReceive('getAttribute')
                ->with('id')
                ->andReturn(1);

        $repo = \Mockery::mock(PrintNowAccountRepository::class);

        $repo->shouldReceive('findFromAccount')
             ->andReturn(
                 $printNowAccount
             );

        $service = $this->getService($repo);

        $service->createAccountOrFindExisting($account);
    }

    /**
     * @test
     */
    public function it_can_get_an_account_token()
    {
        $this->markTestSkipped('This will generate a real print now token, so be careful removing this skip.');
        $repo = \Mockery::mock(PrintNowAccountRepository::class);
        $account = \Mockery::mock(Account::class);
        $printNowAccount = new PrintNowAccount(
            [
                'username' => 'testing',
                'password' => base64_encode('testing1'),
            ]
        );

        $repo->shouldReceive('findFromAccount')
             ->andReturn(
                 $printNowAccount
             );

        $repo->shouldReceive('updatePipoToken');

        $service = $this->getService($repo);

        $token = $service->loginUser($account);

        $this->assertInstanceOf(PipoToken::class, $token);
    }

    /**
     * @test
     */
    public function it_can_logout_an_account()
    {
        $this->markTestSkipped(
            'This reaches out to print now, but should not cause issues if this is removed other than the test failing'
        );
        $repo = \Mockery::mock(PrintNowAccountRepository::class);
        $account = \Mockery::mock(Account::class);
        $printNowAccount = new PrintNowAccount(
            [
                'username'          => 'testing',
                'password'          => base64_encode('testing1'),
                'pipo_token'        => 'bf4b5c6ff15a4119b961307f20e062974fc86680d78846d99668319b52f9c44e',
                'authorization_key' => (string)AccountToken::fromCredentials('testing', 'testing1'),
            ]
        );

        $repo->shouldReceive('findFromAccount')
             ->andReturn(
                 $printNowAccount
             );

        $repo->shouldReceive('removePipoToken');

        $service = $this->getService($repo);

        $service->logoutUser($account);

        $this->assertTrue(true); // Exception will be thrown if this fails
    }

    private function getService($accountRepo) : PrintNowService
    {
        return $this->app->makeWith(
            PrintNowService::class,
            [
                'accountRepository' => $accountRepo,
            ]
        );
    }
}