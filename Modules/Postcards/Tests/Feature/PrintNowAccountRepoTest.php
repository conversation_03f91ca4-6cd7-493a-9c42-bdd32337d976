<?php

namespace Modules\Postcards\Tests\Feature;

use App\Models\PrintNowAccount;
use Modules\Postcards\ValueObjects\CustomerId;
use Tests\LandlordTestCase;
use Tests\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use App\Models\Account;
use App\Repositories\PrintNowAccountRepository;
use Modules\Postcards\Entities\RegisteredAccount;

class PrintNowAccountRepoTest extends LandlordTestCase
{
    use WithFaker, RefreshDatabase;

    /**
     * @test
     */
    public function it_can_create_a_print_now_account()
    {
        $account = factory(Account::class)->create();

        $username = $this->faker->userName;
        $password = $this->faker->password;

        $attempt = new RegisteredAccount($username, $password);

        $repo = $this->getRepository();
        $repo->addPrintNowAccount($account, $attempt);

        $this->assertDatabaseHas(
            "print_now_accounts",
            [
                'username' => $username,
                'password' => base64_encode($password),
            ]
        );
    }

    /**
     * @test
     */
    public function it_can_find_a_print_now_account()
    {
        $account = factory(Account::class)->create();

        $username = $this->faker->userName;
        $password = $this->faker->password;
        $authorizationKey = base64_encode("{$username}:{$password}");

        $attempt = new RegisteredAccount($username, $password);

        $repo = $this->getRepository();
        $repo->addPrintNowAccount($account, $attempt);

        $found = $repo->findFromAccount($account);

        $this->assertNotNull($found);
        $this->assertEquals($username, $found->username);
        $this->assertEquals(base64_encode($password), $found->password);
        $this->assertEquals($authorizationKey, $found->authorization_key);
    }

    /** @test */
    public function it_can_find_a_print_now_account_by_customer_id()
    {
        $customerId = new CustomerId(1234);

        $printNowAccount = $this->getPrintNowAccount($customerId);

        $repo = $this->getRepository();

        $printNowAccountFound = $repo->findByCustomerId($customerId);

        $this->assertInstanceOf(PrintNowAccount::class, $printNowAccountFound);
        $this->assertEquals($printNowAccount->username, $printNowAccountFound->username);
        $this->assertEquals($printNowAccount->customer_id, $printNowAccountFound->customer_id);
    }

    /** @test */
    public function it_updates_the_print_now_account_customer_id()
    {
        $customerId = new CustomerId(1234);

        $printNowAccount = $this->getPrintNowAccount($customerId);

        $repo = $this->getRepository();

        $newCustomerId = new CustomerId(5678);

        $repo->setCustomerIdFor($printNowAccount, $newCustomerId);

        $printNowAccountFound = $repo->findByCustomerId($newCustomerId);

        $this->assertInstanceOf(PrintNowAccount::class, $printNowAccountFound);
        $this->assertEquals($newCustomerId->getValue(), $printNowAccountFound->customer_id);

    }

    /** @test */
    public function it_updates_the_print_now_account_account_id()
    {
        $customerId = new CustomerId(1234);

        $printNowAccount = $this->getPrintNowAccount($customerId);

        $repo = $this->getRepository();

        $newAccount = factory(Account::class)->create();

        $repo->setAccountId($printNowAccount, $newAccount);

        $printNowAccountFound = $repo->findFromAccount($newAccount);

        $this->assertInstanceOf(PrintNowAccount::class, $printNowAccountFound);
        $this->assertEquals($newAccount->id, $printNowAccountFound->account_id);
    }

    private function getRepository() : PrintNowAccountRepository
    {
        return $this->app->make(PrintNowAccountRepository::class);
    }

    /**
     * @param CustomerId $customerId
     * @return mixed
     */
    private function getPrintNowAccount(CustomerId $customerId)
    {
        $account = factory(Account::class)->create();
        $username = $this->faker->userName;
        $password = $this->faker->password;

        return PrintNowAccount::create([
            'username'          => $username,
            'password'          => $password,
            'authorization_key' => base64_encode("{$username}:{$password}"),
            'pipo_token'        => base64_encode("{$username}:{$password}"),
            'customer_id'       => $customerId->getValue(),
            'account_id'        => $account->id,
        ]);
    }
}
