<?php

namespace Modules\Mailing\Listeners;

use App\TeamsNotification\TeamsNotifiable;
use Carbon\Carbon;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\Facades\Cache;
use Modules\Mailing\BulkMailing\Events\MessageSent;
use Modules\Mailing\Events\Mailgun\DomainCheckPassed;
use Modules\Mailing\Events\Mailgun\DomainLocked;
use Modules\Mailing\Events\Mailgun\SendFailed;
use Modules\Mailing\Models\ExternalMessage;
use Modules\Mailing\Notifications\MailgunDomainHeartbeat;
use Modules\Mailing\Notifications\MailgunDomainLocked;
use Modules\Mailing\Notifications\MailingSendFailed;

class MailgunEventsSubscriber
{
    const MAILING_FAILURES_CACHE_KEY = 'mailgun:failed_mailings_counter';
    const MAILING_FAILURES_CACHE_TTL = 'mailgun:failed_mailings_ttl';

    /** @var \App\TeamsNotification\TeamsNotifiable */
    protected $notifiable;

    public function __construct(TeamsNotifiable $notifiable)
    {
        $this->notifiable = $notifiable;
    }

    public function onDomainLocked(DomainLocked $event)
    {
        $key = 'mailgun:notifications:domain-locked:' . $event->domain;

        if (Cache::has($key)) {
            return;
        }

        $this->notifiable->notify(
            new MailgunDomainLocked($event->domain, $event->occurredAt)
        );

        Cache::put($key, 'true', config('mailgun.notifications.locked'));
    }

    public function onDomainCheckPassed(DomainCheckPassed $event)
    {
        $key = 'mailgun:notifications:domain-passed:' . $event->domain;

        if (Cache::has($key)) {
            return;
        }

        $this->notifiable->notify(
            new MailgunDomainHeartbeat($event->domain, $event->occurredAt)
        );

        Cache::put($key, 'true', config('mailgun.notifications.heartbeat'));
    }

    public function onSendFailed(SendFailed $event)
    {
        $counter = $this->updateFailureCounter();

        $notifyAt = [25, 50, 100];

        if ($counter <= 10 || in_array($counter, $notifyAt) || $counter % 250 === 0) {
            $this->notifiable->notify(
                new MailingSendFailed($event->mailing, $event->occurredAt, $event->exception, $counter)
            );
        }
    }

    public function onMessageSent(MessageSent $event)
    {
        ExternalMessage::createForMailing(
            $event->message->getMailing(),
            $event->messageId
        );
    }

    /**
     * @param \Illuminate\Contracts\Events\Dispatcher $dispatcher
     *
     * @uses onDomainLocked
     * @uses onSendFailed
     * @uses onDomainCheckPassed
     * @uses onMessageSent
     */
    public function subscribe(Dispatcher $dispatcher): void
    {
        $dispatcher->listen(DomainLocked::class, get_class($this) . '@onDomainLocked');
        $dispatcher->listen(SendFailed::class, get_class($this) . '@onSendFailed');
        $dispatcher->listen(DomainCheckPassed::class, get_class($this) . '@onDomainCheckPassed');
        $dispatcher->listen(MessageSent::class, get_class($this) . '@onMessageSent');
    }

    private function updateFailureCounter()
    {
        //get the current fail counts, will return 0 if not set
        $failCounter = Cache::get(self::MAILING_FAILURES_CACHE_KEY, 0);

        //increment it
        $failCounter++;

        //get the current cached ttl, defaults 10 minutes
        //using a timestamp instead of the Carbon object because the Carbon object can't be mocked for tests
        $cachedTtl = Cache::get(self::MAILING_FAILURES_CACHE_TTL, now()->addMinutes(10));

        //Set both cache values (put for counter, add for ttl)
        Cache::put(self::MAILING_FAILURES_CACHE_KEY, $failCounter, $cachedTtl);
        Cache::add(self::MAILING_FAILURES_CACHE_TTL, $cachedTtl, $cachedTtl);

        return $failCounter;
    }
}
