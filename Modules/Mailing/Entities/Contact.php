<?php

namespace Modules\Mailing\Entities;

class Contact
{
    /** @var string **/
    public $uuid;

    /** @var string */
    public $email;

    /** @var string */
    public $firstName;

    /** @var string */
    public $lastName;

    public function __construct(string $uuid, string $email, ?string $firstName, ?string $lastName)
    {
        $this->uuid = $uuid;
        $this->email = $email;
        $this->firstName = $firstName ?? '';
        $this->lastName = $lastName ?? '';
    }

    public function fullName() : string
    {
        return sprintf('%s %s', $this->firstName, $this->lastName);
    }
}
