<?php

namespace Modules\Mailing\Entities;

class EmailAddress
{
    /** @var int */
    private $id;

    /** @var string */
    private $address;

    public function __construct(int $id, string $address)
    {
        $this->id = $id;
        $this->address = $address;
    }

    public function getId() : int
    {
        return $this->id;
    }

    public function getAddress() : string
    {
        return $this->address;
    }
}
