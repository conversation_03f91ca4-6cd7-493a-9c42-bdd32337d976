<?php

namespace Modules\Mailing\Entities\EmailEvents;

use Modules\Mailing\Models\ExternalMessage;
use Carbon\Carbon;

interface Event
{
    public function getId() : EventId;

    public function getExternalMessage() : ?ExternalMessage;

    public function getEmailAddress() : string;

    public function getEmailAddressId() : int;

    public function getRecipientId() : int;

    public function getOccurredAt() : Carbon;

    public function getType() : EventType;
}
