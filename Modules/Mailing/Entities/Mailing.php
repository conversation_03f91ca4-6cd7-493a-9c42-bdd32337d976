<?php

namespace Modules\Mailing\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class Mailing implements Arrayable, Jsonable
{
    /** @var int */
    private $id;

    /** @var int */
    private $accountId;

    /** @var string */
    private $state;

    /** @var string */
    private $fromName;

    /** @var string */
    private $fromEmail;

    /** @var string */
    private $replyEmail;

    /** @var string */
    private $subject;

    /** @var string */
    private $letter;

    /** @var string */
    private $htmlBody;

    /** @var int */
    private $bookId;

    /** @var string */
    private $sendAt;

    /** @var array */
    private $recipientGroupIds;

    /** @var string */
    private $createdAt;

    /** @var string */
    private $updatedAt;

    /** @var bool */
    private $canBeResent;

    /** @var array */
    private $properties;

    /** @var string */
    private $featured_image_url;

    /** @var int|null */
    private $product_schedule_id;

    /** @var int|null */
    private $resend_of;

    public function __construct(
        int $id,
        int $accountId,
        string $state,
        string $fromName,
        string $fromEmail,
        string $replyEmail,
        string $subject,
        string $letter,
        string $htmlBody,
        int $bookId,
        string $sendAt,
        array $recipientGroupUuids,
        string $createdAt,
        string $updatedAt,
        bool $canBeResent,
        array $properties,
        string $featured_image_url = null,
        int $product_schedule_id = null,
        int $resend_of = null
    ) {
        $this->id = $id;
        $this->accountId = $accountId;
        $this->state = $state;
        $this->fromName = $fromName;
        $this->fromEmail = $fromEmail;
        $this->replyEmail = $replyEmail;
        $this->subject = $subject;
        $this->letter = $letter;
        $this->htmlBody = $htmlBody;
        $this->bookId = $bookId;
        $this->sendAt = $sendAt;
        $this->recipientGroupUuids = $recipientGroupUuids;
        $this->createdAt = $createdAt;
        $this->updatedAt = $updatedAt;
        $this->canBeResent = $canBeResent;
        $this->properties = $properties;
        $this->featured_image_url = $featured_image_url;
        $this->product_schedule_id = $product_schedule_id;
        $this->resend_of = $resend_of;
    }

    public function getId() : int
    {
        return $this->id;
    }

    public function setId(int $id) : void
    {
        $this->id = $id;
    }

    public function getAccountId() : int
    {
        return $this->accountId;
    }

    public function setAccountId(int $accountId) : Mailing
    {
        $this->accountId = $accountId;

        return $this;
    }

    public function getState() : string
    {
        return $this->state;
    }

    public function setState(string $state) : void
    {
        $this->state = $state;
    }

    public function getFromName() : string
    {
        return $this->fromName;
    }

    public function setFromName(string $fromName) : void
    {
        $this->fromName = $fromName;
    }

    public function getFromEmail() : string
    {
        return $this->fromEmail;
    }

    public function setFromEmail(string $fromEmail) : void
    {
        $this->fromEmail = $fromEmail;
    }

    public function getReplyEmail() : string
    {
        return $this->replyEmail;
    }

    public function setReplyEmail(string $replyEmail) : void
    {
        $this->replyEmail = $replyEmail;
    }

    public function getSubject() : string
    {
        return $this->subject;
    }

    public function setSubject(string $subject) : void
    {
        $this->subject = $subject;
    }

    public function getLetter() : string
    {
        return $this->letter;
    }

    public function setLetter(string $letter) : void
    {
        $this->letter = $letter;
    }

    public function getHtmlBody() : string
    {
        return $this->htmlBody;
    }

    public function setHtmlBody(string $htmlBody) : void
    {
        $this->htmlBody = $htmlBody;
    }

    public function getBookId() : int
    {
        return $this->bookId;
    }

    public function setBookId(int $bookId) : void
    {
        $this->bookId = $bookId;
    }

    public function getSendAt() : string
    {
        return $this->sendAt;
    }

    public function setSendAt(string $sendAt) : void
    {
        $this->sendAt = $sendAt;
    }

    public function getRecipientGroupUuids() : array
    {
        return $this->recipientGroupUuids;
    }

    public function setRecipientGroupUuids(array $recipientGroupUuids) : void
    {
        $this->recipientGroupUuids = $recipientGroupUuids;
    }

    public function getCreatedAt() : string
    {
        return $this->createdAt;
    }

    public function setCreatedAt(string $createdAt) : void
    {
        $this->createdAt = $createdAt;
    }

    public function getUpdatedAt() : string
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(string $updatedAt) : void
    {
        $this->updatedAt = $updatedAt;
    }

    public function canBeResent() : bool
    {
        return $this->canBeResent;
    }

    public function setCanBeResent(bool $canBeResent) : void
    {
        $this->canBeResent = $canBeResent;
    }

    public function getProperties() : array
    {
        return $this->properties;
    }

    public function setProperties(array $properties) : void
    {
        $this->properties = $properties;
    }

    public function getFeaturedImageUrl(): ?string
    {
        return $this->featured_image_url;
    }

    public function setFeaturedImageUrl(string $featuredImageUrl): void
    {
        $this->featuredImageUrl = $featuredImageUrl;
    }

    public function getProductScheduleId(): ?int
    {
        return $this->product_schedule_id;
    }

    public function getResendOf(): ?int
    {
        return $this->resend_of;
    }

    public function toArray(): array
    {
        return [
            'id'                  => $this->getId(),
            'account_id'          => $this->getAccountId(),
            'state'               => $this->getState(),
            'from_name'           => $this->getFromName(),
            'from_email'          => $this->getFromEmail(),
            'reply_email'         => $this->getReplyEmail(),
            'subject'             => $this->getSubject(),
            'letter'              => $this->getLetter(),
            'html_body'           => $this->getHtmlBody(),
            'book_id'             => $this->getBookId(),
            'send_at'             => $this->getSendAt(),
            'product_schedule_id' => $this->getProductScheduleId(),
            'recipient_group_uuids' => $this->getRecipientGroupUuids(),
            'properties'          => $this->getProperties(),
            'featured_image_url'  => $this->getFeaturedImageUrl(),
            'created_at'          => $this->getCreatedAt(),
            'updated_at'          => $this->getUpdatedAt(),
            'can_be_resent'       => $this->canBeResent(),
            'resend_of'           => $this->getResendOf()
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}
