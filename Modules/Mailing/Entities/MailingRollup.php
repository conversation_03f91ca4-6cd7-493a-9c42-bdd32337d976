<?php

namespace Modules\Mailing\Entities;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class MailingRollup implements Arrayable, Jsonable
{
    /** @var int */
    private $mailingId;

    /** @var int */
    private $deliveryAttempt;

    /** @var int */
    private $deliverySuccess;

    /** @var int */
    private $deliveryBounceTotal;

    /** @var int */
    private $deliveryBounceHard;

    /** @var int */
    private $deliveryBounceSoft;

    /** @var int */
    private $uniqueOpen;

    /** @var int */
    private $uniqueClick;

    /** @var int */
    private $uniqueUnsubscribe;

    /** @var int */
    private $uniqueSpamComplaint;

    public function __construct(
        int $mailingId = 0,
        int $deliveryAttempt = 0,
        int $deliverySuccess = 0,
        int $deliveryBounceHard = 0,
        int $deliveryBounceSoft = 0,
        int $uniqueOpen = 0,
        int $uniqueClick = 0,
        int $uniqueUnsubscribe = 0,
        int $uniqueSpamComplaint = 0
    ) {
        $this->mailingId = $mailingId;
        $this->deliveryAttempt = $deliveryAttempt;
        $this->deliverySuccess = $deliverySuccess;
        $this->deliveryBounceTotal = $deliveryBounceHard + $deliveryBounceSoft;
        $this->deliveryBounceHard = $deliveryBounceHard;
        $this->deliveryBounceSoft = $deliveryBounceSoft;
        $this->uniqueOpen = $uniqueOpen;
        $this->uniqueClick = $uniqueClick;
        $this->uniqueUnsubscribe = $uniqueUnsubscribe;
        $this->uniqueSpamComplaint = $uniqueSpamComplaint;
    }

    public function getMailingId() : int
    {
        return $this->mailingId;
    }

    public function getDeliveryAttempt() : int
    {
        return $this->deliveryAttempt;
    }

    public function setDeliveryAttempt(int $deliveryAttempt) : void
    {
        $this->deliveryAttempt = $deliveryAttempt;
    }

    public function getDeliverySuccess() : int
    {
        return $this->deliverySuccess;
    }

    public function setDeliverySuccess(int $deliverySuccess) : void
    {
        $this->deliverySuccess = $deliverySuccess;
    }

    public function getDeliveryBounceTotal() : int
    {
        return $this->deliveryBounceTotal;
    }

    public function setDeliveryBounceTotal(int $deliveryBounceTotal) : void
    {
        $this->deliveryBounceTotal = $deliveryBounceTotal;
    }

    public function getDeliveryBounceHard() : int
    {
        return $this->deliveryBounceHard;
    }

    public function setDeliveryBounceHard(int $deliveryBounceHard) : void
    {
        $this->deliveryBounceHard = $deliveryBounceHard;
    }

    public function getDeliveryBounceSoft() : int
    {
        return $this->deliveryBounceSoft;
    }

    public function setDeliveryBounceSoft(int $deliveryBounceSoft) : void
    {
        $this->deliveryBounceSoft = $deliveryBounceSoft;
    }

    public function getUniqueOpen() : int
    {
        return $this->uniqueOpen;
    }

    public function setUniqueOpen(int $uniqueOpen) : void
    {
        $this->uniqueOpen = $uniqueOpen;
    }

    public function getUniqueClick() : int
    {
        return $this->uniqueClick;
    }

    public function setUniqueClick(int $uniqueClick) : void
    {
        $this->uniqueClick = $uniqueClick;
    }

    public function getUniqueUnsubscribe() : int
    {
        return $this->uniqueUnsubscribe;
    }

    public function setUniqueUnsubscribe(int $uniqueUnsubscribe) : void
    {
        $this->uniqueUnsubscribe = $uniqueUnsubscribe;
    }

    public function getUniqueSpamComplaint() : int
    {
        return $this->uniqueSpamComplaint;
    }

    public function setUniqueSpamComplaint(int $uniqueSpamComplaint) : void
    {
        $this->uniqueSpamComplaint = $uniqueSpamComplaint;
    }

    public function toArray() : array
    {
        return [
            'mailingId' => $this->getMailingId(),
            'deliveryAttempt' => $this->getDeliveryAttempt(),
            'deliverySuccess' => $this->getDeliverySuccess(),
            'deliveryBounceTotal' => $this->getDeliveryBounceTotal(),
            'deliveryBounceHard' => $this->getDeliveryBounceHard(),
            'deliveryBounceSoft' => $this->getDeliveryBounceSoft(),
            'uniqueOpen' => $this->getUniqueOpen(),
            'uniqueClick' => $this->getUniqueClick(),
            'uniqueUnsubscribe' => $this->getUniqueUnsubscribe(),
            'uniqueSpamComplaint' => $this->getUniqueSpamComplaint(),
        ];
    }

    public function toJson($options = 0) : string
    {
        return json_encode($this->toArray(), $options);
    }
}
