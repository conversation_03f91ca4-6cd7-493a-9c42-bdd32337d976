<?php

namespace Modules\Mailing\Interservice\Listeners;

use App\Context\Jobs\AccountAware;
use Domain\DigitalEdition\Events\ContactBlockUpdated as DigitalEditionContactBlockUpdated;
use Domain\Profile\Events\DisplayNameUpdated;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Arr;
use Modules\EmailMarketing\Domain\ContactBlock\Events\ContactBlockUpdated as LocalContentContactBlockUpdated;
use Modules\Mailing\Models\Mailing;

class FromNameSubscriber implements ShouldQueue, AccountAware
{
    public function onDisplayNameUpdated(DisplayNameUpdated $event)
    {
        if (! $displayName = $event->getDisplayName()) {
            return;
        }

        Mailing::canBeQueued()
            ->update(['from_name' => $displayName]);
    }

    public function onDigitalEditionContactBlockUpdated(DigitalEditionContactBlockUpdated $event)
    {
        $item = collect($event->getItems())->first(function ($item) {
            return Arr::get($item, 'section') === 'display_name';
        }, []);

        $displayName = Arr::get($item, 'custom')
                       ?? Arr::get($item, 'item_data.display_name')
                          ?? Arr::get($item, 'item_data.name');

        if (! $displayName) {
            return;
        }

        Mailing::forDigitalEdition()
            ->canBeQueued()
            ->update(['from_name' => $displayName]);
    }

    public function onLocalEventsContactBlockUpdated(LocalContentContactBlockUpdated $event)
    {
        $item = collect($event->getItems())->first(function ($item) {
            return Arr::get($item, 'section') === 'display_name';
        }, []);

        $displayName = Arr::get($item, 'custom')
                       ?? Arr::get($item, 'item_data.display_name')
                          ?? Arr::get($item, 'item_data.name');

        if (! $displayName) {
            return;
        }

        Mailing::forLocalEvents()
            ->canBeQueued()
            ->update(['from_name' => $displayName]);
    }

    public function subscribe(Dispatcher $events)
    {
        $events->listen(DisplayNameUpdated::class, get_class($this) . '@onDisplayNameUpdated');
        $events->listen(DigitalEditionContactBlockUpdated::class,
            get_class($this) . '@onDigitalEditionContactBlockUpdated');
        $events->listen(LocalContentContactBlockUpdated::class, get_class($this) . '@onLocalEventsContactBlockUpdated');
    }
}
