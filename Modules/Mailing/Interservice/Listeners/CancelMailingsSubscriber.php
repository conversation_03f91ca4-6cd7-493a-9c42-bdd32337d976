<?php

namespace Modules\Mailing\Interservice\Listeners;

use App\Events\AccountRemovedFromDigitalEdition;
use App\Events\AccountRemovedFromLocalEvents;
use App\Events\AccountRemovedFromBrandedPosts;
use Infrastructure\EventListeners\EventSubscriber;
use Modules\Mailing\Models\Mailing;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Contracts\Queue\ShouldQueue;

class CancelMailingsSubscriber extends EventSubscriber implements ShouldQueue
{
    protected static $events = [
        AccountRemovedFromLocalEvents::class,
        AccountRemovedFromDigitalEdition::class,
        AccountRemovedFromBrandedPosts::class,
    ];

    public function onAccountRemovedFromDigitalEdition(AccountRemovedFromDigitalEdition $event)
    {
        /** @var \Illuminate\Support\Collection $mailings */
        $mailings = Mailing::forAccountIds([$event->getAccountId()])
            ->forDigitalEdition()
            ->where('send_at', '>', now())
            ->get();

        $mailings->each(function (Mailing $mailing) {
            if (! $mailing->isCancelled()) {
                $mailing->cancel();
            }
            $mailing->delete();
        });
    }

    public function onAccountRemovedFromLocalEvents(AccountRemovedFromLocalEvents $event)
    {
        /** @var \Illuminate\Support\Collection $mailings */
        $mailings = Mailing::forAccountIds([$event->getAccountId()])
            ->forLocalEvents()
            ->where('send_at', '>', now())
            ->get();

        $mailings->each(function (Mailing $mailing) {
            if (! $mailing->isCancelled()) {
                $mailing->cancel();
            }
            $mailing->delete();
        });
    }

    public function onAccountRemovedFromBrandedPosts(AccountRemovedFromBrandedPosts $event)
    {
        /** @var \Illuminate\Support\Collection $mailings */
        $mailings = Mailing::forAccountIds([$event->getAccountId()])
            ->forBrandedPosts()
            ->where('send_at', '>', now())
            ->get();

        $mailings->each(function (Mailing $mailing) {
            if (! $mailing->isCancelled()) {
                $mailing->cancel();
            }
            $mailing->delete();
        });
    }

    public function subscribe(Dispatcher $events)
    {
        $events->listen(
            AccountRemovedFromDigitalEdition::class,
            get_class($this) . '@onAccountRemovedFromDigitalEdition'
        );

        $events->listen(
            AccountRemovedFromLocalEvents::class,
            get_class($this) . '@onAccountRemovedFromLocalEvents'
        );

        $events->listen(
            AccountRemovedFromBrandedPosts::class,
            get_class($this) . '@onAccountRemovedFromBrandedPosts'
        );
    }
}
