<?php

namespace Modules\Mailing\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Throwable;

class FailedJobMailable extends Mailable
{
    use Queueable, SerializesModels;

    /** @var string */
    private $jobClass;

    /** @var string */
    private $jobId;

    /** @var string */
    private $connectionName;

    /** @var \Throwable */
    private $exception;

    public function __construct(
        string $jobClass,
        string $jobId,
        string $connectionName,
        Throwable $exception
    ) {
        $this->with(['__instanceOf' => self::class]);

        $this->jobClass = $jobClass;
        $this->jobId = $jobId;
        $this->connectionName = $connectionName;
        $this->exception = $exception;
    }

    public function build() : FailedJobMailable
    {
        return $this->view('emails.failed-job')
            ->from('<EMAIL>', 'Mailing Service')
            ->subject("A Mailing Service job has failed: " . $this->jobClass)
            ->with([
                "jobClassName"     => $this->jobClass,
                "jobId"            => $this->jobId,
                "connectionName"   => $this->connectionName,
                "exceptionClass"   => get_class($this->exception),
                "exceptionMessage" => $this->exception->getMessage(),
                "stackTrace"       => $this->exception->getTraceAsString(),
            ]);
    }
}
