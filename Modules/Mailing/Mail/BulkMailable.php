<?php

namespace Modules\Mailing\Mail;

use Illuminate\Contracts\Queue\Factory as Queue;
use Modules\Mailing\BulkMailing\Contracts\BodyFactory;
use Modules\Mailing\BulkMailing\Jobs\SendQueuedMailingJob;
use Modules\Mailing\Models\Mailing;
use Illuminate\Bus\Queueable;
use Illuminate\Container\Container;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\HtmlString;
use zz\Html\HTMLMinify;

class BulkMailable extends Mailable
{
    use Queueable, SerializesModels;

    /** @var \Modules\Mailing\Models\Mailing */
    protected $mailing;

    /** @var string|null */
    protected $html;

    /** @var string|null */
    protected $text;

    public function __construct(Mailing $mailing)
    {
        $this->mailing = $mailing;
    }

    public function getMailing() : Mailing
    {
        return $this->mailing;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this
            ->from(
                (empty($this->mailing->getFromEmail())
                    ? $this->mailing->getReplyEmail()
                    : $this->mailing->getFromEmail()
                ),
                $this->mailing->getFromName()
            )
            ->replyTo($this->mailing->getReplyEmail(), $this->mailing->getFromName())
            ->delay($this->mailing->actual_send_at ?? $this->mailing->send_at)
            ->subject($this->getBodyFactory()->renderSubject($this->mailing));
    }

    protected function buildView()
    {
        return array_filter([
            'html' => new HtmlString($this->buildHtml()),
            'raw'  => $this->buildText(),
        ]);
    }

    public function buildHtml() : string
    {
        if (! $this->html) {
            $this->html = $this->mailing->getHtmlBody();
        }

        if (! $this->html) {
            $this->html = $this->getBodyFactory()->renderHtml($this->mailing);
            $this->mailing->setHtmlBody($this->html);
            $this->mailing->save();
        }

        return HTMLMinify::minify($this->html, [HTMLMinify::OPTIMIZATION_ADVANCED]);
    }

    public function buildText() : string
    {
        if (! $this->text) {
            $this->text = $this->mailing->getTextBody();
        }

        if (! $this->text) {
            $this->text = $this->getBodyFactory()->renderText($this->mailing);
            $this->mailing->setTextBody($this->text);
            $this->mailing->save();
        }

        return $this->text;
    }

    public function getCcRecipientEmail() : string
    {
        return $this->mailing->getReplyEmail();
    }

    protected function getBodyFactory() : BodyFactory
    {
        return Container::getInstance()->make(BodyFactory::class);
    }

    /**
     * Overriding the `later` function on the parent class so that we dispatch a job which has the context serialized.
     * Without this, the sending looks like it succeeded, but the delayed job in the queue never succeeds.
     *
     * @param \DateInterval|\DateTimeInterface|int $delay
     * @param \Illuminate\Contracts\Queue\Factory  $queue
     *
     * @return mixed
     */
    public function later($delay, Queue $queue)
    {
        $connection = property_exists($this, 'connection') ? $this->connection : null;

        $queueName = property_exists($this, 'queue') ? $this->queue : null;

        return $queue->connection($connection)->laterOn(
            $queueName ?: null, $delay, new SendQueuedMailingJob($this)
        );
    }
}
