<?php

namespace Modules\Mailing\Jobs;

use App\Context\Jobs\AccountAware;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\FailingJob;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Modules\Mailing\BulkMailing\BulkMailer;
use Modules\Mailing\Models\Mailing;
use Throwable;

class ScheduleMailingJob implements ShouldQueue, AccountAware
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /** @var \Modules\Mailing\Models\Mailing */
    private $mailing;

    /** @var int This job can sometimes take a while, so let's bump the limit to 10 minutes (600 seconds) */
    public $timeout = 600;

    public function __construct(Mailing $mailing)
    {
        $this->mailing = $mailing;
    }

    public function handle(BulkMailer $mailer)
    {
        if ($this->mailing->stateIs() == 'sent') {
            Log::info("Mailing {$this->mailing->getId()} already sent");

            return;
        }

        $mailer->send($this->mailing);

        // Trigger event collection 30 minutes after scheduled send time.
        if ($minutesToWait = config('bulk-mailing.event_collection.initial_fetch_delay')) {
            FetchEventsForMailing::dispatch($this->mailing)
                ->delay($this->mailing->send_at->copy()->addMinutes($minutesToWait));
        }
    }

    public function fail(Throwable $exception = null)
    {
        if ($exception) {
            Log::critical("Unable to send mailing, exception: {$exception->getMessage()}");
        }

        if ($this->mailing->transitionAllowed('failed')) {
            $this->mailing->transition('failed');
        }

        if ($this->job) {
            FailingJob::handle($this->job->getConnectionName(), $this->job, $exception);
        }
    }
}
