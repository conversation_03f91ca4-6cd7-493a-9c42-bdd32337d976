<?php

namespace Modules\Mailing\Jobs;

use App\Context\Jobs\AccountAware;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Modules\Mailing\BulkMailing\EventsRepository;
use Modules\Mailing\Models\ExternalMessage;
use Modules\Mailing\Models\MailgunEventDetail;
use Modules\Mailing\Models\Mailing;

class FetchEventsForMailing implements ShouldQueue, AccountAware
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /** @var \Modules\Mailing\Models\Mailing */
    private $mailing;

    /** @var \Carbon\Carbon */
    private $since;

    /** @var int how many attempts this job can have before failing */
    public $tries = 3;

    /** @var int How long the job can run before timing out */
    public $timeout = 300;

    /** @var array */
    private $events;

    public function __construct(Mailing $mailing, array $events = [], Carbon $since = null)
    {
        $this->mailing = $mailing;
        $this->since = $since ?? $mailing->getSendAt();
        $this->events = $events;

        $this->onQueue('mailing-fetch-events');
    }

    public function getMailing() : Mailing
    {
        return $this->mailing;
    }

    public function handle(EventsRepository $repository)
    {
        Log::debug("Retrieving events on demand");

        $count = 0;

        $this->mailing->externalMessages
            ->filter(function (ExternalMessage $externalMessage) {
                return $externalMessage->isMailGun();
            })
            ->each(
                function (ExternalMessage $externalMessage) use ($repository, $count) {
                    $events = $repository->getAllForId($externalMessage->getId(), $this->since, $this->events);

                    foreach ($events as $event) {
                        try {
                            MailgunEventDetail::createFromEvent($event);
                            $count++;
                        } catch (Exception $e) {
                            Log::error("Unable to save event: " . $e->getMessage(), ['exception' => $e]);
                        }
                    }
                }
            );

        Log::debug("Retrieved $count " . str_plural('event', $count));
    }
}
