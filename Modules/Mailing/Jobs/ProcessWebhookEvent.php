<?php

namespace Modules\Mailing\Jobs;

use App\Context\Jobs\NotAccountAware;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Log;
use Modules\Mailing\BulkMailing\Identifiers\MailgunId;
use Modules\Mailing\Entities\WebhookEvent;
use Modules\Mailing\Models\ExternalMessage;
use Modules\Mailing\Models\MailgunEventDetail;
use Modules\Mailing\Models\Mailing;

class ProcessWebhookEvent implements ShouldQueue, NotAccountAware
{
    use Dispatchable, Queueable;

    /** @var \Modules\Mailing\Entities\WebhookEvent */
    public $event;

    public function __construct(WebhookEvent $event)
    {
        $this->event = $event;
    }

    public function handle() : void
    {
        if (! $mailing = $this->getMailing()) {
            Log::debug(sprintf(
                "Event %s (Message: %s) dropped, unable to locate mailing",
                $this->event->event->getId(),
                $this->getMessageIdForEvent()
            ));

            return;
        }

        // Do the rest w/in the account context
        $mailing->accountId()->execute(function () {
            $loggedEvent = $this->recordEvent();

            Log::debug(sprintf(
                "Recorded %s via webhook, received at %s",
                $loggedEvent->id,
                $this->event->receivedAt->toIso8601String()
            ));
        });
    }

    private function recordEvent() : ?MailgunEventDetail
    {
        return MailgunEventDetail::createFromEvent($this->event->event);
    }

    private function getMailing() : ?Mailing
    {
        $messageId = $this->getMessageIdForEvent();

        if (! $messageId) {
            return null;
        }

        return optional(ExternalMessage::findForExternalMailingId(new MailgunId($messageId)))->getMailing();
    }

    private function getMessageIdForEvent() : string
    {
        return array_get($this->event->event->getMessage(), 'headers.message-id', 'n/a');
    }
}
