<?php

namespace Modules\Mailing\Jobs;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Mailgun\Model\Event\Event;
use Modules\Mailing\BulkMailing\EventsRepository;
use Modules\Mailing\Models\MailgunEventDetail;
use Throwable;

class FetchEmailEventsForDomain implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels, Dispatchable;

    /** @var string */
    private $domain;

    /** @var \Carbon\Carbon */
    private $beginningAt;

    /** @var array */
    private $events;

    public function __construct(string $domain, Carbon $beginningAt, array $events = [])
    {
        $this->domain = $domain;
        $this->beginningAt = $beginningAt;
        $this->events = $events;
        $this->onQueue('mailing-fetch-events');
    }

    public function handle(EventsRepository $repository) : void
    {
        $repository
            ->getEventsStartingAt($this->beginningAt, $this->domain)
            ->each(function (iterable $events) {
                foreach ($events as $event) {
                    $this->persistEvent($event);
                }
            });
    }

    private function persistEvent(Event $event)
    {
        try {
            MailgunEventDetail::createFromEvent($event);
        } catch (Throwable $e) {
            Log::error("Could not save mailgun event: {$e->getMessage()}", [$e]);
        }
    }
}
