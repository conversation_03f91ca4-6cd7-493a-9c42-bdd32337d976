<?php

namespace Modules\Mailing\Jobs;

use InvalidArgumentException;
use Mailgun\Mailgun;
use Modules\Mailing\Entities\EmailEvents\Event as EmailEvent;
use Modules\Mailing\Events\HardBouncesUploaded;
use Modules\Mailing\Events\SpamComplaintsUploaded;
use Modules\Mailing\Events\UnsubscribesUploaded;
use Modules\Mailing\Mailgun\Suppressions\Complaint;
use Modules\Mailing\Mailgun\Suppressions\Hardbounce;
use Modules\Mailing\Mailgun\Suppressions\Suppression;
use Modules\Mailing\Mailgun\Suppressions\Unsubscribe;
use Modules\Mailing\Models\EventType;
use Modules\Mailing\Models\MailgunDomain;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class UploadSuppression implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    /** @var \Modules\Mailing\Models\MailgunEventDetail */
    public $event;

    /** @var \Mailgun\Mailgun */
    private $mailgun;

    public function __construct(EmailEvent $event)
    {
        $this->event = $event;
        $this->onQueue('suppressions');
    }

    public function handle(Mailgun $mailgun)
    {
        if (! $suppression = $this->getSuppression()) {
            return;
        }

        $this->mailgun = $mailgun;

        $this->getDomains()->each(function ($domain) use ($suppression) {
            $this->attemptUpload($suppression, $domain);
        });
    }

    public function getSuppression() : ?Suppression
    {
        return rescue(function () {
            return Suppression::fromMailgunEvent($this->event);
        });
    }

    public function getDomains() : Collection
    {
        return MailgunDomain::getAll();
    }

    private function attemptUpload(Suppression $suppression, string $domain) : void
    {
        try {
            $this->getApiForSuppressionType($suppression)->create($domain, $suppression->getEmail());
            $this->emitSuppressionUploadEvent($suppression, $domain);
        } catch (Exception $e) {
            Log::critical('Failed to upload suppression', [
                'domain'    => $domain,
                'email'     => $suppression->getEmail(),
                'type'      => $suppression->getType()->getEventTypeId(),
                'exception' => $e,
            ]);
        }
    }

    private function emitSuppressionUploadEvent(Suppression $suppression, string $domain) : void
    {
        switch (get_class($suppression->getType())) {
            case Complaint::class:
                event(new SpamComplaintsUploaded($domain, collect([$suppression])));
                break;
            case Hardbounce::class:
                event(new HardBouncesUploaded($domain, collect([$suppression])));
                break;
            case Unsubscribe::class:
                event(new UnsubscribesUploaded($domain, collect([$suppression])));
                break;
        }
    }

    /**
     * @param \Modules\Mailing\Mailgun\Suppressions\Suppression $suppression
     *
     * @return \Mailgun\Api\Suppression\Bounce|\Mailgun\Api\Suppression\Unsubscribe|\Mailgun\Api\Suppression\Complaint
     * @throws \InvalidArgumentException When the suppression type is not supported
     */
    private function getApiForSuppressionType(Suppression $suppression)
    {
        $methodName = null;
        switch ($suppression->getType()->getEventTypeId()) {
            case EventType::UNSUBSCRIBE:
                $methodName = 'unsubscribes';
                break;
            case EventType::SPAM_COMPLAINT:
                $methodName = 'complaints';
                break;
            case EventType::HARD_BOUNCE:
                $methodName = 'bounces';
                break;
        }

        if (! $methodName) {
            throw new InvalidArgumentException(
                "Unsupported suppression type: " . $suppression->getType()->getEventTypeId()
            );
        }

        return $this->mailgun->suppressions()->$methodName();
    }
}
