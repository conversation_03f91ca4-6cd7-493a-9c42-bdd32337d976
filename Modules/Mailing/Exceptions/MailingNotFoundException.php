<?php

namespace Modules\Mailing\Exceptions;

use InvalidArgumentException;

class MailingNotFoundException extends InvalidArgumentException implements MailingException
{
    /** @var int */
    protected $id;

    public static function withId(int $mailingId) : MailingNotFoundException
    {
        $e = new self("No mailing with the ID {$mailingId} was found");
        $e->id = $mailingId;

        return $e;
    }
}