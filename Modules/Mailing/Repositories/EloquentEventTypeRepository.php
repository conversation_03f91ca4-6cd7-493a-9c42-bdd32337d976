<?php

namespace Modules\Mailing\Repositories;

use Illuminate\Database\Eloquent\Collection;
use Modules\Mailing\Contracts\EventTypeRepository;
use Modules\Mailing\Models\EventType;

class EloquentEventTypeRepository implements EventTypeRepository
{
    /** @var EventType */
    private $model;

    public function __construct(EventType $model)
    {
        $this->model = $model;
    }

    public function getAll(): Collection
    {
        return $this->model->orderBy('name')->get();
    }
}
