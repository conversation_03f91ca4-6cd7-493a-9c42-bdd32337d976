<?php

namespace Modules\Mailing\Repositories;

use App\Models\Plan;
use Carbon\Carbon;
use Domain\DigitalEdition\DTO\IssueDTO;
use Domain\DigitalEdition\Models\Recurrence;
use Domain\Scheduling\Models\ProductSchedule;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Modules\Mailing\Contracts\MailingRepository as MailingRepositoryContract;
use Modules\Mailing\DataTransferObjects\MailingDTO;
use Modules\Mailing\DataTransferObjects\RecipientSummaryDTO;
use Modules\Mailing\Entities\AnalyticsSummary;
use Modules\Mailing\Entities\Mailing as MailingEntity;
use Modules\Mailing\Entities\MailingAnalytics;
use Modules\Mailing\Entities\MailingRollup;
use Modules\Mailing\Models\Mailing;
use Modules\Mailing\Models\Mailing as MailingModel;
use Modules\Mailing\Models\MailingRecipient;

class EloquentMailingRepository implements MailingRepositoryContract
{
    public function getMailingIdsPerBook(int $bookId, ?int $accountId = null): array
    {
        return MailingModel::query()
            ->where('book_id', $bookId)
            ->where('product', Plan::PLAN_ID_DIGITAL_EDITIONS)
            ->when($accountId, function (Builder $query, $accountId) {
                $query->where('account_id', $accountId);
            })
            ->pluck('id')
            ->toArray();
    }

    public function getMailingEntityById(int $mailingId): ?MailingEntity
    {
        /** @var \Modules\Mailing\Models\Mailing $mailing */
        $mailing = $this->getMailingModelById($mailingId);

        if (!$mailing) {
            return null;
        }

        return new MailingEntity(
            $mailing['id'],
            $mailing['account_id'],
            $mailing['state'],
            $mailing['from_name'],
            $mailing['from_email'],
            $mailing['reply_email'],
            $mailing['subject'],
            $mailing['letter'] ?? '',
            $mailing['html_body'] ?? '',
            $mailing['book_id'],
            $mailing['send_at'],
            $mailing['recipient_group_uuids'],
            $mailing['created_at'],
            $mailing['updated_at'],
            $mailing->canBeResent(),
            $mailing['properties'] ?? [],
            $mailing->featuredImage->url ?? null,
            $mailing->getProductScheduleId(),
            $mailing['resendOf'] ?? null
        );
    }

    public function getMailingModelById(int $mailingId): ?MailingModel
    {
        /** @var \Modules\Mailing\Models\Mailing $mailing */
        return MailingModel::find($mailingId);
    }

    public function getMailingRollups(array $mailingIds): Collection
    {
        return DB::connection('mailing')
            ->table('mailings')
            ->whereIn('mailings.id', $mailingIds)
            ->leftJoin('mailing_recipients', 'mailings.id', '=', 'mailing_id')
            ->select('mailings.id')
            ->selectRaw(
                "(SELECT COUNT(id) FROM mailing.mailing_recipients WHERE mailing_id = mailings.id) as recipients"
            )
            ->selectRaw("IFNULL(SUM(mailing_recipients.is_delivered),0) as deliveries")
            ->selectRaw("IFNULL(SUM(mailing_recipients.hard_bounced),0) as hard_bounces")
            ->selectRaw("IFNULL(SUM(mailing_recipients.soft_bounces),0) AS soft_bounces")
            ->selectRaw(
                "(SELECT COUNT(id) FROM mailing.mailing_recipients WHERE mailing_id = mailings.id and (opens > 0 || clicks > 0) ) as unique_opens"
            )
            ->selectRaw(
                "(SELECT COUNT(id) FROM mailing.mailing_recipients WHERE mailing_id = mailings.id and clicks > 0) as unique_clicks"
            )
            ->selectRaw(
                "(SELECT COUNT(id) FROM mailing.mailing_recipients WHERE mailing_id = mailings.id and unsubscribed > 0) as unique_unsubscribes"
            )
            ->selectRaw(
                "(SELECT COUNT(id) FROM mailing.mailing_recipients WHERE mailing_id = mailings.id and spam_complaints > 0) as unique_spam_complaints"
            )
            ->groupBy('mailings.id')
            ->get()
            ->mapWithKeys(function (\stdClass $row) {
                return [
                    $row->id => new MailingRollup(
                        $row->id,
                        $row->recipients,
                        $row->deliveries,
                        $row->hard_bounces,
                        $row->soft_bounces,
                        $row->unique_opens,
                        $row->unique_clicks,
                        $row->unique_unsubscribes,
                        $row->unique_spam_complaints
                    ),
                ];
            });
    }

    public function getGroupedDigitalEditionRecurrenceMailingCountsForCreateDate(Carbon $createdAt): Collection
    {
        return MailingModel::allAccounts()
            ->selectRaw("state, count(*) AS num_mailings")
            ->forDigitalEdition()
            ->whereDate('created_at', $createdAt)
            ->whereRaw("JSON_VALUE(properties, '$.recurrence_settings_id') IS NOT NULL")
            ->groupBy('state')
            ->get();
    }

    public function createMailing(MailingDTO $mailingData): int
    {
        // todo should also have logic preventing customer from creating a mailing with the same groups
        // This should be elsewhere... so not removing to-do for now so that I Don't forget
        // Rule is: creating a mailing for the same time and recipient groups

        //get the mailing data as an array
        $mailingArr = $mailingData->toArray();

        //if there's a featured image url, get the id for it
        if ($mailingArr['featured_image_url']) {
            $mailingImage = \Modules\Mailing\Models\MailingImage::firstOrCreate(
                ['url' => $mailingArr['featured_image_url']]
            );
            $mailingArr['featured_image_id'] = $mailingImage->id;
        }

        //create the mailing
        $mailing = MailingModel::create(Arr::except($mailingArr, ['featured_image_url']));

        //return the id
        return $mailing->id;
    }

    public function deleteMailing(int $mailingId): bool
    {
        return (bool)MailingModel::where('id', $mailingId)->delete();
    }

    public function getRecipientEvents(int $mailingId): MailingAnalytics
    {
        $recipients = MailingRecipient::query()
            ->forMailing($mailingId)
            ->with('email')
            ->get()
            ->map(function (MailingRecipient $recipient) {
                return new RecipientSummaryDTO([
                    'recipient_id'    => $recipient->contact_id,
                    'email_address'   => $recipient->email->address,
                    'delivered'       => $recipient->is_delivered,
                    'opens'           => $recipient->opens,
                    'clicks'          => $recipient->clicks,
                    'hard_bounces'    => (int)$recipient->hard_bounced,
                    'soft_bounces'    => $recipient->soft_bounces,
                    'unsubscribed'    => $recipient->unsubscribed,
                    'spam_complaints' => $recipient->spam_complaints
                ]);
            });

        $totals = $recipients->reduce(function (array $carry, RecipientSummaryDTO $item) {
            foreach ($carry as $key => $value) {
                $carry[$key] += $item->{$key};
            }

            return $carry;
        }, [
            'delivered'       => 0,
            'opens'           => 0,
            'clicks'          => 0,
            'hard_bounces'    => 0,
            'soft_bounces'    => 0,
            'unsubscribed'    => 0,
            'spam_complaints' => 0,
        ]);

        return new MailingAnalytics([
            'totals'           => new AnalyticsSummary($totals),
            'recipient_events' => $recipients,
        ]);
    }

    public function determineSendingTime(Recurrence $recurrence, IssueDTO $issue): Carbon
    {
        //get the issue start date as a carbon object
        $issueStart = $issue->startsAt;

        //create the recurrence date time from the issue start date string, and setting time for 9AM,
        // using the time zone for the account, then set to UTC
        $recurDateTime = Carbon::parse($issueStart->toDateString() . " 09:00:00", $recurrence->account->timezone)
            ->timezone('UTC');

        // Get the number of days to push forward from the 1st. Don't go past the last day of the month.
        $numDaysInMonth = $recurDateTime->daysInMonth;
        $recurDateTime->addDays(min($recurrence->selections->day_of_month, $numDaysInMonth) - 1);

        return $recurDateTime;
    }

    public function getNextMailingByProduct(int $product): ?MailingModel
    {
        /** @var \Modules\Mailing\Models\Mailing $mailing */
        $mailing = MailingModel::where('product', '=', $product)
            ->where('state', '=', 'new')
            ->orderBy('send_at', 'asc')
            ->first();

        if (!$mailing) {
            return null;
        }

        return $mailing;
    }

    public function getNextNewMailingsByProduct(int $product): Collection
    {
        return MailingModel::where('product', '=', $product)
            ->where('state', '=', MailingModel::STATE_NEW)
            ->orderBy('send_at', 'asc')
            ->get();
    }

    /**
     * Given the mailing DTO, checks to see if a mailing exists for the same send date and time, for the same product,
     * sending to at least one of the same recipient groups
     *
     * @param MailingDTO $mailingDTO
     * @return bool
     */
    public function checkForExistingMailingsForGroups(MailingDTO $mailingDTO): bool
    {
        return collect($mailingDTO->recipient_group_uuids)->contains(function ($groupId) use ($mailingDTO) {
            return MailingModel::forProduct($mailingDTO->product)
                ->scheduledFor(Carbon::createFromTimestamp($mailingDTO->send_at_timestamp))
                ->get()
                ->first(function ($mailing) use ($groupId) {
                    return in_array($groupId, $mailing->getRecipientGroupUuids());
                });
        });
    }

    /**
     * Given the passed bookId, checks if mailings exist for DE for that book.
     *
     * @param int $bookId - the id of the book to check for mailings
     * @return bool
     */
    public function checkDeMailingsExistForBookId(int $bookId): bool
    {
        return MailingModel::forDigitalEdition()
            ->where('book_id', $bookId)
            ->exists();
    }

    public function getMailingByProductScheduleForBrandedPosts(ProductSchedule $productSchedule): ?MailingModel
    {
        return MailingModel::forBrandedPosts()
            ->forProductSchedule($productSchedule)
            ->where('state', Mailing::STATE_NEW)
            ->where('send_at', '>', Carbon::now())
            ->whereNull('resend_of')
            ->orderBy('send_at', 'ASC')
            ->first();
    }

    public function getLastMailingsForBrandedPosts(): Collection
    {
        return $this->getBrandedPostMailingQuery()
            ->take(4)
            ->get();
    }

    public function getMailingDataFromBrandedPostsPaginated(int $limit): LengthAwarePaginator
    {
        return $this->getBrandedPostMailingQuery()
            ->paginate($limit);
    }

    public function getLatestMailingDataForBrandedPosts(): ?MailingModel
    {
        return MailingModel::forBrandedPosts()
            ->where('state', 'sent')
            ->whereNull('resend_of')
            ->orderBy('send_at', 'DESC')
            ->first();
    }

    public function getScheduledMailingsForBrandedPosts(): Collection
    {
        return MailingModel::forBrandedPosts()
            ->where('state', 'new')
            ->whereProperty('manual_resend', true)
            ->orderBy('send_at', 'DESC')
            ->with(['mailingRecipients', 'featuredImage'])
            ->get();
    }

    public function getScheduleResendMailingForBrandedPosts(): ?MailingModel
    {
        return MailingModel::forBrandedPosts()
            ->isNew()
            ->whereProperty('manual_resend', true)
            ->whereProperty('strict_resend', true)
            ->orderBy('send_at', 'DESC')
            ->first();
    }

    public function getUpcomingMailingForBrandedPostsByProductSchedule(ProductSchedule $schedule): ?MailingModel
    {
        return MailingModel::forBrandedPosts()
            ->forProductSchedule($schedule)
            ->where('send_at', '>', now())
            ->where('state', 'new')
            ->first();
    }

    public function getUpcomingMailingsForBrandedPostByProductSchedule(ProductSchedule $productSchedule): Collection
    {
        return MailingModel::query()
            ->forBrandedPosts()
            ->forProductSchedule($productSchedule)
            ->where('state', Mailing::STATE_NEW)
            ->where('send_at', '>', Carbon::now())
            ->orderBy('send_at', 'ASC')
            ->get();
    }

    public function getMailingDataFromLocalEventsPaginated(int $limit): LengthAwarePaginator
    {
        return $this->getLocalEventsMailingQuery()
            ->paginate($limit);
    }

    public function getLastMailingsForLocalEvents(): Collection
    {
        return MailingModel::query()
            ->forLocalEvents()
            ->where('state', 'sent')
            ->orderBy('send_at', 'DESC')
            ->orderBy('id', 'DESC')
            ->with(['mailingRecipients', 'featuredImage'])
            ->take(3)
            ->get();
    }

    public function getLatestMailingForLocalEvents(): ?MailingModel
    {
        return MailingModel::forLocalEvents()
            ->where('state', 'sent')
            ->whereNull('resend_of')
            ->orderBy('send_at', 'DESC')
            ->first();
    }

    public function getScheduledMailingsForLocalEvents(): Collection
    {
        return MailingModel::forLocalEvents()
            ->where('state', 'new')
            ->whereProperty('manual_resend', true)
            ->orderBy('send_at', 'DESC')
            ->with(['mailingRecipients', 'featuredImage'])
            ->get();
    }

    public function getScheduledMailingStrictResendForLocalEvents(): ?MailingModel
    {
        return MailingModel::forLocalEvents()
            ->where('state', 'new')
            ->whereProperty('manual_resend', true)
            ->whereProperty('strict_resend', true)
            ->orderBy('send_at', 'DESC')
            ->first();
    }

    private function getBrandedPostMailingQuery(): Builder
    {
        return MailingModel::query()
            ->forBrandedPosts()
            ->where('state', 'sent')
            ->orderBy('send_at', 'DESC')
            ->orderBy('id', 'DESC')
            ->with(['mailingRecipients', 'featuredImage']);
    }

    private function getLocalEventsMailingQuery(): Builder
    {
        return MailingModel::query()
            ->forLocalEvents()
            ->where('state', 'sent')
            ->orderBy('send_at', 'DESC')
            ->with(['mailingRecipients', 'featuredImage']);
    }
}
