<?php

namespace Modules\Mailing\Observers;

use Modules\Mailing\Entities\EmailEvents\Event;
use Modules\Mailing\Jobs\UploadSuppression;
use Modules\Mailing\Models\EventType;
use Modules\Mailing\Models\MailingRecipientEvent;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class EmailEventObserver
{
    public function created(Event $event)
    {
        if ($event->shouldBeSuppressedDueToOverQuota() || $event->shouldBeSuppressedDueToMxError()) {
            dispatch(new UploadSuppression($event));
        }

        if ($this->eventShouldBeTranslated($event)) {
            $this->convertEventToMailingRecipientEvent($event);
        }
    }

    /**
     * @param \Modules\Mailing\Entities\EmailEvents\Event $event
     */
    protected function convertEventToMailingRecipientEvent(Event $event) : void
    {
        try {
            MailingRecipientEvent::createFromEmailEvent($event);
        } catch (Exception $e) {
            Log::info("Could not save event {$event->getId()}: " . $e->getMessage());
        }
    }

    private function eventShouldBeTranslated(Event $event) : bool
    {
        return Cache::remember(
            sprintf('event-exists:%s', $event->getType()->getType()),
            60, // todo update when we move to new caching system which caches in seconds
            function () use ($event) {
                return EventType::forName($event->getType()->getType())->exists();
            }
        );
    }
}
