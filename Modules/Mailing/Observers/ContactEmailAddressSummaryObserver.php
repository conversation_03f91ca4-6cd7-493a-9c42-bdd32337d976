<?php

namespace Modules\Mailing\Observers;

use Infrastructure\Contacts\Models\EmailAddress as ContactEmailAddress;
use Modules\Mailing\Models\EmailAddress;

class ContactEmailAddressSummaryObserver
{
    public function created(ContactEmailAddress $address)
    {
        optional(EmailAddress::find($address->mailing_email_address_id))->searchable();
    }

    public function updated(ContactEmailAddress $address)
    {
        optional(EmailAddress::find($address->mailing_email_address_id))->searchable();
    }
}
