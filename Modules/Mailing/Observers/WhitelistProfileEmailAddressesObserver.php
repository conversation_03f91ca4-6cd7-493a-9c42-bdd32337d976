<?php

namespace Modules\Mailing\Observers;

use App\Context\Jobs\AccountAware;
use App\Models\EmailAddress;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Modules\Mailing\Actions\UnwhitelistEmailAddressAction;
use Modules\Mailing\Actions\WhitelistEmailAddressAction;

class WhitelistProfileEmailAddressesObserver implements ShouldQueue, AccountAware
{
    public function saved(EmailAddress $address) : void
    {
        $original = $address->getOriginal('email');
        $new = $address->getEmail();

        if ($original == $new) {
            return;
        }

        // Un-Whitelist the previous email only if it is an actual email.
        if ($original && ! $this->emailExistsElsewhere($original, $address->id)) {
            (new UnwhitelistEmailAddressAction)->execute($original);
        }

        (new WhitelistEmailAddressAction)->execute($new);
    }

    public function deleted(EmailAddress $address) : void
    {
        if ($this->emailExistsElsewhere($address->getEmail(), $address->id)) {
            return;
        }

        // Email does not exist, remove it from the whitelist.
        (new UnwhitelistEmailAddressAction)->execute($address->getEmail());
    }

    protected function emailExistsElsewhere(string $emailAddress, ?int $id) : bool
    {
        return EmailAddress::allAccounts()
            ->where('email', $emailAddress)
            ->when($id, function (Builder $builder, $id) {
                $builder->where('id', '!=', $id);
            })
            ->exists();
    }
}
