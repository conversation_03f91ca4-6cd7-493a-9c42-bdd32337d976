<?php

namespace Modules\Mailing\Observers;

use App\Context\Jobs\AccountAware;
use Domain\Contacts\Actions\CreateContactDeliverabilityStatusFromMailingRecipient;
use Illuminate\Contracts\Queue\ShouldQueue;
use Modules\Mailing\Models\MailingRecipientEvent;

class MailingRecipientObserver implements ShouldQueue, AccountAware
{
    public $queue = 'analytics';

    public function created(MailingRecipientEvent $event)
    {
        $event->recipient->addEvent($event);

        $event->recipient->email->actOnRecipientEvent($event);
    }
}
