<?php

namespace Modules\Mailing\Models;

use Illuminate\Database\Eloquent\Model;
use Modules\Mailing\Traits\UsesMailingDatabase;

class MailgunFailureReason extends Model
{
    use UsesMailingDatabase;

    public $timestamps = false;

    protected $fillable = [
        'reason',
    ];

    public static function getIdForFailureReason(string $failureReason) : ?int
    {
        return self::firstOrCreate(['reason' => $failureReason])->id;
    }
}
