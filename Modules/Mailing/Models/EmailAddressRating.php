<?php

namespace Modules\Mailing\Models;

use App\Models\Plan;
use App\Traits\BelongsToAccount;
use App\ValueObjects\Database\IntegrityConstraint;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\QueryException;
use Modules\Mailing\Traits\UsesMailingDatabase;
use PDOException;

/**
 * Class EmailAddressRating
 * @package Modules\Mailing\Models
 *
 * @property int         $id               ID of the email address rating
 * @property int         $email_address_id ID of the related email address
 * @property int         $account_id       ID of the related account
 * @property int         $product_id       ID of the related product
 * @property int         $rating           Rating of the recipient
 * @property Carbon|null $whitelisted_at
 * @property Carbon|null $unsubscribed_at
 *
 * @method static Builder|self forEmail(int $emailAddressId)
 * @method static Builder|self forAccount(int $accountId)
 * @method static Builder|self forProduct(int $productId)
 * @method static Builder|self forDigitalEdition()
 * @method static Builder|self forLocalEvents()
 * @method static Builder|self forBrandedPosts()
 * @method static Builder|self isUnsubscribed()
 * @method static Builder|self isWhitelisted()
 */
class EmailAddressRating extends Model
{
    use UsesMailingDatabase;
    use BelongsToAccount;

    protected $dates = [
        'whitelisted_at',
        'unsubscribed_at',
    ];

    protected $attributes = [
        'rating' => 1, // Default starting rating
    ];

    /**
     * @var array
     */
    protected $fillable = [
        'email_address_id',
        'account_id',
        'product_id',
        'rating',
        'whitelisted_at',
        'unsubscribed_at',
    ];

    //Relationships
    public function email() : BelongsTo
    {
        return $this->belongsTo(EmailAddress::class, 'email_address_id', 'id');
    }

    // Scopes
    public function scopeForEmail(Builder $query, int $emailAddressId) : Builder
    {
        return $query->where('email_address_id', $emailAddressId);
    }

    public function scopeForAccount(Builder $query, int $accountId) : Builder
    {
        return $query->where('account_id', $accountId);
    }

    public function scopeForProduct(Builder $query, int $productId)
    {
        return $query->where('product_id', $productId);
    }

    public function scopeForDigitalEdition(Builder $query)
    {
        return $query->forProduct(Plan::PLAN_ID_DIGITAL_EDITIONS);
    }

    public function scopeForLocalEvents(Builder $query)
    {
        return $query->forProduct(Plan::PLAN_ID_LOCAL_EVENT);
    }

    public function scopeForBrandedPosts(Builder $query) : void
    {
        $query->forProduct(Plan::PLAN_ID_SOCIAL_MEDIA_SHARES);
    }

    public function scopeIsUnsubscribed(Builder $query) : void
    {
        $query->whereNotNull('unsubscribed_at');
    }

    public function scopeIsWhitelisted(Builder $query) : void
    {
        $query->whereNotNull('whitelisted_at');
    }

    public function scopeIsNotWhitelisted(Builder $query) : void
    {
        $query->whereNull('whitelisted_at');
    }

    public function whitelist(?Carbon $when = null) : self
    {
        $this->update([
            'rating'          => 100,
            'unsubscribed_at' => null,
            'whitelisted_at'  => $when ?? now(),
        ]);

        return $this;
    }

    public function unwhitelist() : self
    {
        $this->whitelisted_at = null;
        $this->recalculateRating();
        $this->save();

        return $this;
    }

    public function unsubscribe() : self
    {
        $this->unsubscribed_at = new Carbon();
        $this->rating = 0;
        $this->save();

        return $this;
    }

    public function resubscribe() : self
    {
        $this->unsubscribed_at = null;
        $this->recalculateRating();
        $this->save();

        return $this;
    }

    public function recalculateRating() : self
    {
        // Do not downgrade any "whitelisted" email addresses
        if ($this->whitelisted_at) {
            return $this;
        }

        // If we're already unsubscribed, do not update any further.
        if ($this->unsubscribed_at) {
            return $this;
        }

        //get the product mailing recipients for this account
        $productRecipients = MailingRecipient::where('email_address_id', $this->email_address_id)
            ->where('account_id', $this->account_id)
            ->where('created_at', '>=', Carbon::parse('4 months ago'))
            ->with('mailing')
            ->get()
            ->filter(function ($recipient) {
                //if no mailing and product is found, don't include the recipient
                return $recipient->mailing && $recipient->mailing->getProduct() == $this->product_id;
            });

        //get the rating
        $newRating = MailingRecipient::calculateEmailRating($productRecipients);

        //set it
        $this->rating = min(100, $newRating);

        return $this;
    }

    public static function recalculateFromEvent(MailingRecipientEvent $event) : void
    {
        //Get all mailing recipients for this account and email address
        $recipients = MailingRecipient::where('email_address_id', $event->recipient->email->id)
            ->where('account_id', $event->recipient->account_id)
            ->where('created_at', '>=', Carbon::parse('4 months ago'))
            ->with('mailing')
            ->get();

        //Determine if there is only 1 mailing for the account and email
        $mailingIds = $recipients->pluck('mailing_id')->unique();

        //check how many unique mailings there are
        if ($mailingIds->count() <= 1 || ! $event->recipient->mailing->isForDigitalProduct()) {
            //only 1 email, adjust rating to 50 for account and all products
            // or the mailing was not for one of the digital products
            foreach (Plan::DIGITAL_PRODUCTS as $planId) {
                try {
                    $rating = self::firstOrCreate([
                        'email_address_id' => $event->recipient->email->id,
                        'account_id'       => $event->recipient->account_id,
                        'product_id'       => $planId,
                    ]);
                } catch (QueryException $e) {
                    $previous = $e->getPrevious();

                    // Even with updateOrCreate, we can occasionally get duplicate entries if there are
                    // jobs firing closely to one another.
                    // This is intended to catch that scenario and try to update it manually.
                    if ($previous instanceof PDOException
                        && $previous->getErrorCode() == IntegrityConstraint::getErrorCode()
                    ) {
                        $rating = self::where('email_address_id', $event->recipient->email->id)
                            ->where('account_id', $event->recipient->account_id)
                            ->where('product_id', $planId)
                            ->first();
                    } else {
                        throw $e;
                    }
                }

                // Update $rating here
                $rating->rating = ($event->recipient->email->whitelisted_at)
                    ? 100
                    : 50;
                $rating->whitelisted_at = ($event->recipient->email->whitelisted_at)
                    ? Carbon::now()
                    : null;

                $rating->save();
            }
        } else {
            //get the product id for the mailing
            $productId = $event->recipient->mailing->getProduct();

            // Do not downgrade any "whitelisted" email address ratings
            if (self::forEmail($event->recipient->email->id)->forProduct($productId)->isWhitelisted()->exists()) {
                return;
            }

            // If we're already unsubscribed, do not update any further.
            if (self::forEmail($event->recipient->email->id)->forProduct($productId)->isUnsubscribed()->exists()) {
                return;
            }

            //otherwise, recalculate just for the product the event is in
            $productRecipients = $recipients->filter(function ($recipient) use ($event, $productId) {
                //if no mailing and product is found, don't include the recipient
                return $recipient->mailing && $recipient->mailing->getProduct() == $productId;
            });

            //get the rating
            $newRating = MailingRecipient::calculateEmailRating($productRecipients);

            //take the lower value between 100 and the calculated rating
            self::updateOrCreate([
                'email_address_id' => $event->recipient->email->id,
                'account_id'       => $event->recipient->account_id,
                'product_id'       => $productId,
            ], [
                'rating' => min(100, $newRating),
            ]);
        }
    }

    public static function unsubscribeFromEvent(MailingRecipientEvent $event) : void
    {
        //if the mailing wasn't for a digital product, return only
        //currently don't support product specific unsubscribe for adhoc mailings
        if (! $event->recipient->mailing->isForDigitalProduct()) {
            return;
        }

        //if the event is an unsubscribe event, we also update the unsubscribed_at date, and only do it for that product
        $updateVars = [
            'unsubscribed_at' => Carbon::now(),
            'rating'          => 0,
        ];

        //unsubscribe for the account and product
        self::updateOrCreate([
            'email_address_id' => $event->recipient->email->id,
            'account_id'       => $event->recipient->account_id,
            'product_id'       => $event->recipient->mailing->getProduct(),
        ], $updateVars);
    }

    public static function dropAllRatingsFromEvent(MailingRecipientEvent $event) : void
    {
        //drop the rating for all digital products
        foreach (Plan::DIGITAL_PRODUCTS as $planId) {
            self::updateOrCreate([
                'email_address_id' => $event->recipient->email->id,
                'account_id'       => $event->recipient->account_id,
                'product_id'       => $planId,
            ], [
                'rating' => 0,
            ]);
        }
    }

    public static function getForEmailAccountProduct(
        EmailAddress $emailAddress,
        int $accountId,
        int $productId
    ) : EmailAddressRating {
        return static::where([
            'email_address_id' => $emailAddress->id,
            'account_id'       => $accountId,
            'product_id'       => $productId,
        ])->firstOr(function () use ($emailAddress, $accountId, $productId) {
            try {
                return static::create([
                    'email_address_id' => $emailAddress->id,
                    'account_id'       => $accountId,
                    'product_id'       => $productId,
                    'rating'           => $emailAddress->rating,
                ]);
            } catch (QueryException $e) {
                // Even with firstOrCreate, we can occasionally get duplicate entries if there are jobs firing closely
                // to one another.  This is intended to catch that scenario and quietly discard the error.
                $previous = $e->getPrevious();

                if ($previous instanceof PDOException
                    && IntegrityConstraint::getErrorCode() == $previous->getErrorCode()
                ) {
                    return static::where([
                        'email_address_id' => $emailAddress->id,
                        'account_id'       => $accountId,
                        'product_id'       => $productId,
                    ])->first();
                }
                throw $e;
            }
        });
    }

    public function toArray()
    {
        return [
            'id'               => $this->id,
            'email_address_id' => $this->email_address_id,
            'account_id'       => $this->account_id,
            'product_id'       => $this->product_id,
            'rating'           => $this->rating,
            'whitelisted_at'   => $this->whitelisted_at,
            'unsubscribed_at'  => $this->unsubscribed_at,
        ];
    }
}
