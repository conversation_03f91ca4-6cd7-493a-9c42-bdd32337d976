<?php

namespace Modules\Mailing\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use InvalidArgumentException;
use Modules\Mailing\Entities\EmailEvents\Event;
use Modules\Mailing\Traits\UsesMailingDatabase;

/**
 * Class MailingRecipientEvent
 * @package Modules\Mailing\Models
 * @property \Modules\Mailing\Models\MailingRecipient $recipient
 * @property int                                      $mailing_recipient_id
 * @property \Modules\Mailing\Models\EventType        $eventType
 * @property int                                      $event_type_id
 * @property \Carbon\Carbon                           $occurred_at
 */
class MailingRecipientEvent extends Model
{
    use UsesMailingDatabase;

    protected $guarded = [];

    public $timestamps = false;

    protected $dates = [
        'occurred_at'
    ];

    public static function createFromEmailEvent(Event $event)
    {
        $externalMessage = $event->getExternalMessage();

        if (! $externalMessage) {
            throw new InvalidArgumentException("Unknown external message id");
        }

        $mailing = $externalMessage->getMailing();

        $emailAddressId = EmailAddress::getIdForAddress($event->getEmailAddress());

        //make sure there is a specific EmailAddressRatings record for the account and product for the email
        EmailAddressRating::firstOrCreate([
            'email_address_id' => $emailAddressId,
            'account_id' => $mailing->getAccountId(),
            'product_id' => $mailing->getProduct(),
        ]);

        /** @var \Modules\Mailing\Models\MailingRecipient $recipient */
        $recipient = $mailing->mailingRecipients()->where(['email_address_id' => $emailAddressId])->first();

        if (! $recipient) {
            // create the recipient
            $recipient = $mailing->mailingRecipients()->create(['email_address_id' => $emailAddressId]);
        }

        return $recipient->events()
            ->create([
                'event_type_id' => EventType::getIdForEventName($event->getType()->getType()),
                'occurred_at'   => $event->getOccurredAt(),
            ]);
    }

    public function eventType(): BelongsTo
    {
        return $this->belongsTo(EventType::class);
    }

    public function recipient(): BelongsTo
    {
        return $this->belongsTo(MailingRecipient::class, 'mailing_recipient_id');
    }
}
