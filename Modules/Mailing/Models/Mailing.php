<?php

namespace Modules\Mailing\Models;

use App\Context\AccountId;
use App\Models\Plan;
use App\Traits\BelongsToAccount;
use App\Traits\QueriesJsonColumns;
use Carbon\Carbon;
use DateTimeInterface;
use Domain\BrandedPosts\Models\Issue;
use Domain\Scheduling\Models\ProductSchedule;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Modules\EmailMarketing\Models\Market;
use Modules\Mailing\Traits\Statable;
use Modules\Mailing\Traits\UsesMailingDatabase;

/**
 * @method static Builder|Mailing sendingBetween(Carbon $after, Carbon $before)
 * @method static Builder|Mailing canBeQueued()
 * @method static Builder|Mailing notYetSent()
 * @method static Builder|Mailing forAccountId(int $accountId)
 * @method static Builder|Mailing forBrandedPosts()
 * @method static Builder|Mailing forDigitalEdition()
 * @method static Builder|Mailing forLocalEvents()
 * @method static Builder|Mailing forProductSchedule(ProductSchedule $schedule)
 * @method static Builder|Mailing whereProperty(string $key, $value)
 * @method static Builder|Mailing where()
 * @property \Modules\Mailing\Models\Mailing|null $resendOf
 * @property \Carbon\Carbon $send_at
 * @property \Carbon\Carbon|null $actual_send_at
 * @property Collection|MailingRecipient[] $mailingRecipients
 * @property MailingImage $featuredImage
 */
class Mailing extends Model
{
    use Statable, UsesMailingDatabase, BelongsToAccount, QueriesJsonColumns;

    const HISTORY_MODEL = [
        'name' => MailingState::class,
    ];
    /* State Machine */
    const STATE_NEW = 'new';
    const INITIAL_STATE = self::STATE_NEW;
    const SM_CONFIG = 'mailing';
    const STATE_SENT = 'sent';
    const STATE_CANCELLED = 'cancelled';
    const STATE_FAILED = 'failed';
    const STATE_FAILED_NO_RECIPIENTS = 'failed-no-recipients';
    const STATE_QUEUED = 'queued';

    /* DE Email Templates */
    const DE_MAILING_DEFAULT_THEME = 'default';
    const DE_MAILING_PERSONAL_THEME = 'personal';

    /* Replacement values used in the email subjects */
    const SUBJECT_REPLACEMENT_VALUE_NAME = '[Name]';
    const SUBJECT_REPLACEMENT_VALUE_MONTH = '[Month]';
    const SUBJECT_REPLACEMENT_VALUE_YEAR = '[Year]';

    const SUBJECT_ALL_REPLACEMENT_VALUES = [
        self::SUBJECT_REPLACEMENT_VALUE_NAME,
        self::SUBJECT_REPLACEMENT_VALUE_MONTH,
        self::SUBJECT_REPLACEMENT_VALUE_YEAR
    ];

    const PERSONAL_THEME_DEFAULT_SUBJECT = "My Digital Magazine for [Month] [Year] [Gifted Access from [Name]]";

    protected $guarded = [];

    protected $attributes = [
        'book_id'           => null,
        'state'             => self::INITIAL_STATE,
        'from_email'        => '', //todo I hate this
        'uuid'              => null,
        'featured_image_id' => null
    ];

    protected $dates = [
        'send_at',
        'actual_send_at',
    ];

    protected $casts = [
        'id'                  => 'int',
        'account_id'          => 'int',
        'book_id'             => 'int',
        'recipient_group_ids' => 'array',
        'recipient_group_uuids' => 'array',
        'properties'          => 'array'
    ];

    public function externalMessages()
    {
        return $this->hasMany(ExternalMessage::class);
    }

    public function mailingRecipients(): HasMany
    {
        return $this->hasMany(MailingRecipient::class);
    }

    public function featuredImage(): HasOne
    {
        return $this->hasOne(MailingImage::class, 'id', 'featured_image_id');
    }

    public function setUuid(?string $uuid)
    {
        return $this->setAttribute('uuid', $uuid);
    }

    public function setResendOf(?int $mailingId)
    {
        return $this->setAttribute('resend_of', $mailingId);
    }

    public function resendOf()
    {
        return $this->belongsTo(self::class, 'resend_of');
    }

    public function resentMailings()
    {
        return $this->hasMany(self::class, 'resend_of');
    }

    public function setActualSend(DateTimeInterface $dateTime): self
    {
        return $this->setAttribute('actual_send_at', $dateTime);
    }

    protected function setSendAtTimestampAttribute($value)
    {
        $this->setAttribute('send_at', Carbon::createFromTimestampUTC($value));
    }

    public function getId(): int
    {
        return $this->getAttribute('id');
    }

    public function getAccountId(): AccountId
    {
        return new AccountId($this->getAttribute('account_id'));
    }

    public function setFromEmail(string $email): self
    {
        $this->setAttribute('from_email', $email);

        return $this;
    }

    public function getFromEmail(): string
    {
        return $this->getAttribute('from_email');
    }

    public function getReplyEmail(): string
    {
        return $this->getAttribute('reply_email');
    }

    public function getFromName(): string
    {
        return $this->getAttribute('from_name');
    }

    public function getSubject(): string
    {
        return $this->getAttribute('subject');
    }

    public function setSubject(?string $subject)
    {
        $this->setAttribute('subject', $subject);
    }

    public function getSendAt(): Carbon
    {
        return $this->getAttribute('send_at');
    }

    public function setSendAt(Carbon $sendAt)
    {
        return $this->setAttribute('send_at', $sendAt);
    }

    public function getBookId(): ?int
    {
        return $this->getAttribute('book_id');
    }

    public function getLetter(): ?string
    {
        return $this->getAttribute('letter');
    }

    public function setLetter(?string $letter)
    {
        $this->setAttribute('letter', $letter);
    }

    public function getProperties(): ?array
    {
        return $this->getAttribute('properties') ?? [];
    }

    public function setProperties(?array $properties)
    {
        if ($properties) {
            $this->setAttribute('properties', array_merge($this->getProperties(), $properties));
        }
    }

    public function getHeading(): ?string
    {
        return $this->getProperties()['heading'] ?? null;
    }

    public function getOriginalMailing(): ?self
    {
        return $this->resendOf;
    }

    public function getRecipientGroupIds(): array
    {
        return $this->getAttribute('recipient_group_ids');
    }

    public function getRecipientGroupUuids(): array
    {
        return $this->getAttribute('recipient_group_uuids') ?? [];
    }

    public function isResend(): bool
    {
        return !empty($this->getAttribute('resend_of'));
    }

    public function setHtmlBody(string $body)
    {
        $this->setAttribute('html_body', $body);
    }

    public function getHtmlBody(): ?string
    {
        return $this->getAttribute('html_body');
    }

    public function setTextBody(string $body)
    {
        $this->setAttribute('text_body', $body);
    }

    public function getTextBody(): ?string
    {
        return $this->getAttribute('text_body');
    }

    public function getProduct(): int
    {
        return $this->getAttribute('product');
    }

    public function getProductScheduleId(): ?int
    {
        return $this->getAttribute('product_schedule_id');
    }

    public function isDigitalEditions(): bool
    {
        return $this->getProduct() === Plan::PLAN_ID_DIGITAL_EDITIONS;
    }

    public function isLocalEvents(): bool
    {
        return $this->getProduct() === Plan::PLAN_ID_LOCAL_EVENT;
    }

    // Scopes
    public static function scopeForAccountId(Builder $builder, int $accountId): Builder
    {
        return $builder->where('account_id', $accountId);
    }

    public static function scopeForAccountIds(Builder $builder, $accountIds): Builder
    {
        return $builder->whereIn('account_id', $accountIds);
    }

    public function scopeSendingBetween(Builder $query, Carbon $after, Carbon $before)
    {
        return $query->whereBetween('send_at', [$after, $before]);
    }

    public function scopeIsNew(Builder $query)
    {
        return $query->whereIn('state', [self::STATE_NEW]);
    }

    public function scopeCanBeQueued(Builder $query)
    {
        return $query->whereIn('state', [self::INITIAL_STATE]);
    }

    public function scopeNotYetSent(Builder $query)
    {
        return $query->where('state', '=', self::STATE_NEW);
    }

    public function scopeScheduledFor(Builder $builder, Carbon $sendAt)
    {
        return $builder->where('send_at', $sendAt);
    }

    public function scopeForProduct(Builder $query, int $productId)
    {
        return $query->where('product', $productId);
    }

    public function scopeForDigitalEdition(Builder $query)
    {
        return $query->forProduct(Plan::PLAN_ID_DIGITAL_EDITIONS);
    }

    public function scopeForLocalEvents(Builder $query): void
    {
        $query->forProduct(Plan::PLAN_ID_LOCAL_EVENT);
    }

    public function scopeForBrandedPosts(Builder $query): void
    {
        $query->forProduct(Plan::PLAN_ID_SOCIAL_MEDIA_SHARES);
    }

    public function scopeForProductSchedule(Builder $builder, ProductSchedule $schedule): void
    {
        $builder->where('product_schedule_id', $schedule->id);
    }

    public function scopeWhereProperty(Builder $builder, string $key, $value): void
    {
        $builder->whereJsonProperty('properties', $key, $value);
    }

    public function createForResend(Carbon $sendAt)
    {
        return self::create(
            array_merge(
                array_except(
                    $this->getAttributes(),
                    ['id', 'updated_at', 'created_at', 'state', 'recipient_group_uuids', 'actual_send_at']
                ),
                [
                    'resend_of'           => $this->getAttribute('id'),
                    'recipient_group_uuids' => $this->getAttribute('recipient_group_uuids'),
                    'properties'          => $this->getAttribute('properties'),
                    'send_at'             => $sendAt,
                ]
            )
        );
    }

    public function restore(): bool
    {
        return $this->transition('restore');
    }

    public function cancel(): bool
    {
        return $this->transition('cancel');
    }

    public function isCancelled(): bool
    {
        return 'cancelled' == $this->stateIs();
    }

    public function canBeDeleted(): bool
    {
        return $this->stateIs() === self::STATE_NEW;
    }

    public function canBeResent(): bool
    {
        if ($this->getSendAt()->lessThan(app('bulk-mailing.resend-cutoff'))) {
            return false;
        }

        return in_array(
            $this->stateIs(),
            [self::STATE_SENT, self::STATE_CANCELLED, self::STATE_FAILED, self::STATE_FAILED_NO_RECIPIENTS]
        );
    }

    /**
     * Checks if the product id for the current mailing is a digital product
     * @return bool
     */
    public function isForDigitalProduct(): bool
    {
        return in_array($this->getProduct(), Plan::DIGITAL_PRODUCTS);
    }

    public function getRecipientEmailIdsForAllMailings(): Collection
    {
        //get the top level parent of the mailing chain
        $originalParent = $this->getTopLevelParentMailing();

        //get all the related mailing ids
        $allMailingIds = $originalParent->getRelatedMailingIds();

        //get the email ids of the previous recipients
        $previousMailingRecipients = MailingRecipient::getEmailIdsOfPastRecipientsOfMailings($allMailingIds);

        return $previousMailingRecipients;
    }

    public function getRecipientEmailAddressesAndIdsForAllMailings(): Collection
    {
        //get the top level parent of the mailing chain
        $originalParent = $this->getTopLevelParentMailing();

        //get all the related mailing ids
        $allMailingIds = $originalParent->getRelatedMailingIds();

        //get the email ids of the previous recipients
        return MailingRecipient::getEmailAddressesAndIdsOfPastRecipientsOfMailings($allMailingIds);
    }

    /**
     * Given the passed mailing object, recursively call up to the parent until the original mailing is found
     *
     * @param Mailing $mailing
     * @return Mailing
     */
    public function getTopLevelParentMailing(): Mailing
    {
        //if this is a resent mail, and has a valid parent, return the parent's original parent
        if ($this->isResend() && $this->resendOf) {
            return $this->resendOf->getTopLevelParentMailing();
        };

        //otherwise, return itself
        return $this;
    }

    /**
     * Given the passed mailing object, recursively return all the mailing ids for itself, and it's children
     *
     * @param Mailing $mailing
     * @return Collection
     */
    public function getRelatedMailingIds(): Collection
    {
        $ids = collect($this->getId());

        //foreach child, get the related mailing ids
        $this->resentMailings->each(function (Mailing $child) use (&$ids) {
            $ids = $ids->merge($child->getRelatedMailingIds());
        });

        //return a unique collection
        return $ids->unique();
    }

    /**
     * Given the current mailing object, return the string that for the content type
     */
    public function getContentTypeText(): ?string
    {
        //default return string
        $contentTypeText = null;

        //Switch on the product
        switch ($this->getProduct()) {
            //Branded Posts
            case Plan::PLAN_ID_BRANDED_POSTS:
                $issueUuidForMailing = Arr::get($this->getProperties(), 'issue_uuid');
                $issue = Issue::find($issueUuidForMailing);

                //if there's an issue, get the theme name for the issue
                if ($issue) {
                    $contentTypeText = $issue->theme->name;
                }
                break;

            //Local Events
            case Plan::PLAN_ID_LOCAL_EVENT:
                //get the market from the mailing
                $marketUuidForMailing = Arr::get($this->getProperties(), 'market_uuid');
                $market = Market::find($marketUuidForMailing);

                //if there's a market, get the name
                if ($market) {
                    $contentTypeText = $market->name;
                }
                break;
        }

        //return the text
        return $contentTypeText;
    }

    /**
     * Some mailings are not valid for the add mailing process.
     * Ex: a BP mailing that was converted from an LE blog post mailing and is missing the issue_uuid
     *
     * @return bool
     */
    public function isValidForAddMailing(): bool
    {
        return Plan::PLAN_ID_SOCIAL_MEDIA_SHARES == $this->getProduct()
            ? Arr::exists($this->getProperties(), 'issue_uuid')
            : true;
    }

    /**
     * Some DE mailings have replacement values in the subject that need to be replaced with data from the account,
     * or other mailing properties
     */
    public function subjectHasReplacementValues(): bool
    {
        return Str::contains($this->getSubject(), self::SUBJECT_ALL_REPLACEMENT_VALUES);
    }
}
