<?php

namespace Modules\Mailing\Models;

use Carbon\Carbon;
use Doctrine\DBAL\Driver\PDOException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\QueryException;
use Illuminate\Support\Collection;
use Infrastructure\Contacts\Models\EmailAddress as ContactEmailAddress;
use Laravel\Scout\Searchable;
use Modules\Mailing\Traits\UsesMailingDatabase;

/**
 * Class EmailAddress
 * @package Modules\Mailing\Models
 * @method static Builder forAddress(string $emailAddress)
 * @property int    $id               ID of the email address
 * @property string $address          Email Address
 * @property int    $rating           Rating of the recipient
 * @property \Carbon\Carbon|null $whitelisted_at
 * @property \Carbon\Carbon|null $unsubscribed_at Whether or not the email is globally unsubscribed
 */
class EmailAddress extends Model
{
    use UsesMailingDatabase, Searchable;

    const DEFAULT_EMAIL_RATING = 1;

    public $timestamps = false;

    protected $guarded = [];

    protected $casts = [
    ];

    protected $attributes = [
        'rating' => self::DEFAULT_EMAIL_RATING, // Default starting rating
    ];

    protected $dates = [
        'whitelisted_at',
        'unsubscribed_at',
    ];

    public function mailingRecipients() : HasMany
    {
        return $this->hasMany(MailingRecipient::class);
    }

    public function mailings() : HasManyThrough
    {
        return $this->hasManyThrough(
            Mailing::class,
            MailingRecipient::class,
            'email_address_id',
            'id',
            'id',
            'mailing_id'
        );
    }

    public function emailRatings() : HasMany
    {
        return $this->hasMany(EmailAddressRating::class, 'email_address_id');
    }

    public function contactEmailAddresses() : HasMany
    {
        return $this->hasMany(ContactEmailAddress::class, 'mailing_email_address_id');
    }

    public function unsubscribe() : self
    {
        $this->unsubscribed_at = new Carbon();

        return $this;
    }

    public function resubscribe() : self
    {
        $this->unsubscribed_at = null;
        $this->recalculateRating();

        return $this;
    }

    public function scopeForAddress(Builder $builder, string $emailAddress) : void
    {
        $builder->where('address', $emailAddress);
    }

    public function scopeForAddresses(Builder $builder, Collection $emailAddresses) : void
    {
        $builder->whereIn('address', $emailAddresses);
    }

    public function scopeIsUnsubscribed(Builder $builder) : void
    {
        $builder->whereNotNull('unsubscribed_at');
    }

    public function scopeIsNotWhitelisted(Builder $builder) : void
    {
        $builder->whereNull('whitelisted_at');
    }

    public static function getIdForAddress(string $emailAddress) : int
    {
        return static::getForAddress($emailAddress)->id;
    }

    public static function getForAddress(string $emailAddress) : EmailAddress
    {
        return static::where(['address' => $emailAddress])->firstOr(function () use ($emailAddress) {
            try {
               return static::create(['address' => $emailAddress]);
            } catch (QueryException $e) {
                // Even with firstOrCreate, we can occasionally get duplicate entries if there are jobs firing closely to one
                // another.  This is intended to catch that scenario and quietly discard the error.
                $previous = $e->getPrevious();

                if ($previous instanceof PDOException && 1062 == $previous->getErrorCode()) {
                    return static::where(['address' => $emailAddress])->first();
                }
                throw $e;
            }
        });
    }

    protected function setRatingAttribute(int $rating) : void
    {
        $this->attributes['rating'] = max(0, $rating);
    }

    protected function setUnsubscribedAtAttribute(?Carbon $value = null) : void
    {
        $this->attributes['unsubscribed_at'] = $value;

        if (null != $this->unsubscribed_at) {
            $this->rating = 0;
        }
    }

    public function whitelist(?Carbon $when = null) : self
    {
        $this->update([
            'rating'          => 100,
            'unsubscribed_at' => null,
            'whitelisted_at'  => $when ?? now()
        ]);

        return $this;
    }

    public function unwhitelist() : self
    {
        $this->whitelisted_at = null;

        $this->recalculateRating();

        $this->save();

        return $this;
    }

    public function recalculateRating() : self
    {
        // Do not downgrade any "whitelisted" email addresses
        if ($this->whitelisted_at) {
            return $this;
        }

        // If we're already unsubscribed, do not update any further.
        if ($this->unsubscribed_at) {
            return $this;
        }

        //get the all the mailing recipients with this email address, that were created in the last 4 months
        $recipients = MailingRecipient::where('email_address_id', $this->id)
            ->where('created_at', '>=', Carbon::parse('4 months ago'))
            ->get();

        //get the rating
        $newRating = MailingRecipient::calculateEmailRating($recipients);

        //take the lower value between 100 and the calculated rating
        $this->rating = min(100, $newRating);

        return $this;
    }

    protected function shouldBeUnsubscribed(
        int $complaints,
        int $unsubscribes,
        int $hardBounces
    ) : bool {
        return $complaints > 0
               || $unsubscribes > 0
               || $hardBounces > 0;
    }

    public function isUnsubscribed() : bool
    {
        return (bool)$this->unsubscribed_at;
    }

    /**
     * Updates the rating based on the type of event returned
     * @param MailingRecipientEvent $event
     */
    public function actOnRecipientEvent(MailingRecipientEvent $event) : void
    {
        switch ($event->event_type_id) {

            //For delivered or click, we recalculate the rating
            case EventType::DELIVERED:
            case EventType::CLICK:
                //Recalculate for email address if the email was for a digital product
                if ($event->recipient->mailing->isForDigitalProduct()) {
                    $this->recalculateRating();
                    $this->save();
                }

                //recalculate for product and account
                EmailAddressRating::recalculateFromEvent($event);
                break;

            //For unsubscribe, we unsubscribe and set rating to 0
            case EventType::UNSUBSCRIBE:
                //unsubscribe and save the changes
                $this->unsubscribe();
                $this->rating = 0;
                $this->save();

                //Unsubscribe for product and account
                EmailAddressRating::unsubscribeFromEvent($event);
                break;

            //For spam complaints and hard bounces, we set rating to 0
            case EventType::SPAM_COMPLAINT:
            case EventType::HARD_BOUNCE:
                $this->rating = 0;
                $this->save();

                //Unsubscribe for product and account
                EmailAddressRating::dropAllRatingsFromEvent($event);
                break;

            //Do nothing for soft bounce or Open
            case EventType::SOFT_BOUNCE:
            case EventType::OPEN:
                break;
        }
    }

    public function toArray()
    {
        return [
            'id' => $this->id,
            'address' => $this->address,
            'rating' => $this->rating,
            'whitelisted_at' => $this->whitelisted_at,
            'unsubscribed_at' => $this->unsubscribed_at
        ];
    }

    public function toSearchableArray()
    {
        return [
            'id'                      => $this->id,
            'address'                 => $this->address,
            'contacts' => $this->contactEmailAddresses()
                ->get()
                ->map(function (ContactEmailAddress $contact) {
                    return [
                        'contact_id' => $contact->contact_id,
                        'account_id' => $contact->account_id,
                        'validity'   => $contact->is_valid,
                    ];
                }),
            'account_ids'             => $this->contactEmailAddresses()->pluck('account_id')->unique()->toArray(),
            'global_rating'           => $this->rating,
            'whitelisted_at'          => $this->whitelisted_at,
            'unsubscribed_at'         => $this->unsubscribed_at,
            'last_send'               => optional($this->mailings()
                ->orderBy('send_at', 'desc')
                ->where('state', 'sent')
                ->select('mailings.id', 'mailings.account_id', 'send_at')
                ->first())
                ->only('id', 'account_id', 'send_at')
        ];
    }

    public function searchableAs()
    {
        return 'mailing_email_address_index';
    }

    public function getEmail(): string
    {
        return $this->address;
    }
}
