<?php

namespace Modules\Mailing\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Modules\Mailing\Traits\UsesMailingDatabase;

/**
 * Class EventType
 * @package Modules\Mailing\Models
 * @property string $name
 * @method static Builder|static forName(string $name)
 */
class EventType extends Model
{
    use UsesMailingDatabase;

    public $timestamps = false;

    const DELIVERED = 1;
    const OPEN = 2;
    const CLICK = 3;
    const HARD_BOUNCE = 4;
    const SOFT_BOUNCE = 5;
    const UNSUBSCRIBE = 6;
    const SPAM_COMPLAINT = 7;
    const REPLY = 8;

    public static function getIdForEventName(string $eventName) : ?int
    {
        return self::forName($eventName)->pluck('id')->first();
    }

    public function scopeForName(Builder $query, string $name) : void
    {
        $query->where('name', $name);
    }
}
