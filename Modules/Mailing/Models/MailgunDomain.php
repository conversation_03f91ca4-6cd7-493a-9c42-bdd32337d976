<?php

namespace Modules\Mailing\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Laravel\Scout\Searchable;
use Modules\Mailing\Traits\UsesMailingDatabase;

/**
 * Class Domain
 *
 * @package App
 * @method static Builder|MailgunDomain forProduct(int $productId)
 */
class MailgunDomain extends Model
{
    use UsesMailingDatabase, SoftDeletes, Searchable;

    protected $guarded = [];

    protected $hidden = [
        'updated_at',
        'created_at',
    ];

    public function ratings() : HasMany
    {
        return $this->hasMany(MailgunDomainRating::class, 'mailgun_domain_id');
    }

    public function accountMailgunDomains() : HasMany
    {
        return $this->hasMany(AccountMailgunDomain::class, 'mailgun_domain_id');
    }

    public function getDomain() : string
    {
        return $this->getAttribute('domain');
    }

    public static function getAll() : Collection
    {
        // There could be duplicate domains, listed once per product.
        // Use unique to reduce it down to the unique domains so we don't have
        // multiple jobs collecting events for the same domain.
        return static::pluck('domain')
            ->unique()
            ->values();
    }

    public function searchableAs()
    {
        return 'mailing_mailgun_domains_index';
    }
}
