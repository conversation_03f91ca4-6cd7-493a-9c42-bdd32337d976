<?php

namespace Modules\Mailing\Models;

use Illuminate\Database\Eloquent\Model;
use Modules\Mailing\BulkMailing\Identifiers\CampaignerId;
use Modules\Mailing\BulkMailing\Identifiers\LaravelId;
use Modules\Mailing\BulkMailing\Contracts\ExternalId;
use Modules\Mailing\Exceptions\InvalidExternalMessageType;
use Modules\Mailing\BulkMailing\Identifiers\MailgunId;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Mailing\Traits\UsesMailingDatabase;
use Ramsey\Uuid\Uuid;

/**
 * Class ExternalMessage
 * @package App
 */
class ExternalMessage extends Model
{
    use UsesMailingDatabase;

    const TYPE_CAMPAIGNER = 1;
    const TYPE_MAILGUN = 2;
    const TYPE_LARAVEL = 0;

    protected $guarded = [];

    public $incrementing = false;

    protected $casts = [
        'mailing_id' => 'int',
    ];

    public static function createForMailing(Mailing $mailing, ExternalId $messageId)
    {
        switch (get_class($messageId)) {
            case CampaignerId::class:
                $type = self::TYPE_CAMPAIGNER;
                break;
            case MailgunId::class:
                $type = self::TYPE_MAILGUN;
                break;
            case LaravelId::class:
                $type = self::TYPE_LARAVEL;
                break;
            default:
                $type = 0;
        }

        return self::create([
            'id'         => $messageId->getId(),
            'mailing_id' => $mailing->getId(),
            'type'       => $type,
        ]);
    }

    public static function findForExternalMailingId(ExternalId $externalId) : ?self
    {
        return self::find($externalId->getId());
    }

    public function mailing() : BelongsTo
    {
        return $this->belongsTo(Mailing::class);
    }

    /**
     * @return \Modules\Mailing\BulkMailing\Contracts\ExternalId
     * @throws \Modules\Mailing\Exceptions\InvalidExternalMessageType
     */
    public function getId() : ExternalId
    {
        switch ($this->type) {
            case self::TYPE_CAMPAIGNER:
                return new CampaignerId($this->id);
                break;
            case self::TYPE_MAILGUN:
                return new MailgunId($this->id);
                break;
            case self::TYPE_LARAVEL:
                return new LaravelId(Uuid::fromString($this->id));
                break;
        }

        throw InvalidExternalMessageType::forId($this->type);
    }

    public function isMailGun() : bool
    {
        return self::TYPE_MAILGUN == $this->getAttribute('type');
    }

    public function getMailingId() : int
    {
        return $this->getAttribute('mailing_id');
    }

    public function getMailing() : Mailing
    {
        return $this->mailing;
    }
}
