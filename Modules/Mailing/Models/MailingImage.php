<?php

namespace Modules\Mailing\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Mailing\Traits\UsesMailingDatabase;

class MailingImage extends Model
{
    use UsesMailingDatabase;

    /** @var array */
    protected $fillable = [
        'url',
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function mailings() : HasMany
    {
        return $this->hasMany(Mailing::class, 'featured_image_id');
    }
}
