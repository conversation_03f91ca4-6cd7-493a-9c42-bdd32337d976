<?php

namespace Modules\Mailing\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\QueryException;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Mailgun\Model\Event\Event;
use Modules\Mailing\BulkMailing\Identifiers\MailgunId;
use Modules\Mailing\Entities\EmailEvents\Accepted;
use Modules\Mailing\Entities\EmailEvents\Click;
use Modules\Mailing\Entities\EmailEvents\Delivered;
use Modules\Mailing\Entities\EmailEvents\Event as EmailEvent;
use Modules\Mailing\Entities\EmailEvents\EventId;
use Modules\Mailing\Entities\EmailEvents\EventType;
use Modules\Mailing\Entities\EmailEvents\HardBounce;
use Modules\Mailing\Entities\EmailEvents\MailgunEventId;
use Modules\Mailing\Entities\EmailEvents\Open;
use Modules\Mailing\Entities\EmailEvents\Rejected;
use Modules\Mailing\Entities\EmailEvents\SoftBounce;
use Modules\Mailing\Entities\EmailEvents\SpamComplaint;
use Modules\Mailing\Entities\EmailEvents\Unknown;
use Modules\Mailing\Entities\EmailEvents\Unsubscribe;
use Modules\Mailing\Entities\SMTPReplyCodes;
use Modules\Mailing\Traits\UsesMailingDatabase;
use PDOException;

/**
 * Class MailgunEventDetail
 * @package Modules\Mailing\Models
 * @property \Modules\Mailing\Models\ExternalMessage $externalMessage
 * @property array                                   $payload
 */
class MailgunEventDetail extends Model implements EmailEvent
{
    use UsesMailingDatabase;
    const EVENT_TYPE_ACCEPTED = 'accepted';
    const EVENT_TYPE_REJECTED = 'rejected';
    const EVENT_TYPE_DELIVERED = 'delivered';
    const EVENT_TYPE_FAILED = 'failed';
    const EVENT_TYPE_OPENED = 'opened';
    const EVENT_TYPE_CLICKED = 'clicked';
    const EVENT_TYPE_UNSUBSCRIBED = 'unsubscribed';
    const EVENT_TYPE_COMPLAINED = 'complained';
    const EVENT_TYPE_STORED = 'stored';
    const HARDBOUNCE_SEVERITY = 'permanent';

    protected $guarded = [];

    public $timestamps = false;

    protected $casts = [
        'payload'     => 'json',
        'occurred_at' => 'datetime',
    ];

    /** @var \Modules\Mailing\Models\MailingRecipient|null */
    protected $recipient;

    public static function createFromEvent(Event $event) : ?self
    {
        try {
            $messageId = array_get($event->getMessage(), 'headers.message-id');

            if (! $messageId) {
                return null;
            }

            $mailing = optional(ExternalMessage::findForExternalMailingId(new MailgunId($messageId)))->getMailing();

            if (! $mailing) {
                return null;
            }

            $recipientEmail = self::getEmailFromEventRecipient($event->getRecipient());

            $emailAddress = EmailAddress::getForAddress($recipientEmail);
            $mailingRecipient = MailingRecipient::getForMailingAndEmailAddress($mailing, $emailAddress);
            $mailgunFailureReasonId = ($reason = $event->getReason())
                ? MailgunFailureReason::getIdForFailureReason($reason)
                : null;

            return self::create([
                'event_id'             => substr($event->getId(), 0, 22),
                'message_id'           => $messageId,
                'event_type_id'        => self::getMailgunEventTypeId($event),
                'failure_reason_id'    => $mailgunFailureReasonId,
                'severity'             => self::getSeverityIdByName($event->getSeverity()),
                'mailing_id'           => $mailing->getId(),
                'mailing_recipient_id' => $mailingRecipient->getAttribute('id'),
                'payload'              => $event,
                'occurred_at'          => Carbon::createFromTimestampUTC(floor($event->getTimestamp())),
            ]);
        } catch (QueryException $e) {
            // Even with firstOrCreate, we can occasionally get duplicate entries if there are jobs firing closely to one
            // another.  This is intended to catch that scenario and quietly discard the error.
            // Additionally, always checking if that event exists already adds overhead, so now with this accurate check,
            // we'll reduce calls to only be duplicated whenever the event does exists, instead of checking if it exists
            // every time.
            $previous = $e->getPrevious();

            if ($previous instanceof PDOException && 1062 == $previous->getErrorCode()) {
                return static::where('event_id', $event->getId())->first();
            }

            throw $e;
        }
    }

    public static function getEmailFromEventRecipient(string $eventRecipient): string
    {
        //if the passed recipient is a valid email, return it
        if (filter_var($eventRecipient, FILTER_VALIDATE_EMAIL)) {
            return $eventRecipient;
        }

        //otherwise, it may be of the format "Firstname Lastname <<EMAIL>>"
        //so we need to parse it
        $parsedEmail = preg_replace("/>$/", '', preg_replace("/.*</", '', trim($eventRecipient)));

        //if the parsed email is valid, return it
        if (filter_var($parsedEmail, FILTER_VALIDATE_EMAIL)) {
            return $parsedEmail;
        }

        //by default, we just return the original event recipient
        return $eventRecipient;
    }

    public function getId() : EventId
    {
        return new MailgunEventId($this->getAttribute('id'));
    }

    public function getOriginalEvent() : Event
    {
        $rawValue = $this->getAttribute('payload');

        // Originally, we stored these as serializations of the mailgun Event class
        // We're converting them to json to make it easier to read
        // If this event was already converted, return the OriginalEvent as is.
        if (is_array($rawValue)) {
            $rawValue = collect($rawValue)->mapWithKeys(function ($value, $key) {
                return [Str::snake($key, '-') => $value];
            });

            return Event::create($rawValue->toArray());
        }

        return unserialize($rawValue);
    }

    public function getMessageId() : string
    {
        return $this->getAttribute('message_id');
    }

    public function externalMessage()
    {
        return $this->belongsTo(ExternalMessage::class, 'message_id');
    }

    public function getMailing() : ?Mailing
    {
        if ($externalMessage = $this->getExternalMessage()) {
            return $externalMessage->mailing;
        }

        return null;
    }

    public function getMailingId() : ?int
    {
        if ($externalMessage = $this->getExternalMessage()) {
            return $externalMessage->getMailingId();
        }

        return null;
    }

    public function getRecipientEmailAddress() : EmailAddress
    {
        /** @var MailingRecipient $recipient */
        $recipient = MailingRecipient::with('email')->find($this->getAttribute('mailing_recipient_id'));

        return $recipient->email;
    }

    public function getExternalMessage() : ?ExternalMessage
    {
        return $this->externalMessage;
    }

    public function getEmailAddress() : string
    {
        return $this->getRecipientEmailAddress()->address;
    }

    public function getEmailAddressId() : int
    {
        return $this->getRecipientEmailAddress()->id;
    }

    public function getRecipient() : MailingRecipient
    {
        if (! $this->recipient && $this->getMailing()) {
            $this->recipient =
                MailingRecipient::getForMailingAndEmailAddress($this->getMailing(), $this->getRecipientEmailAddress());
        }

        return $this->recipient;
    }

    public static function getMailgunEventTypeId(Event $event) : string
    {
        return MailgunEventType::getIdForEventName($event->getEvent());
    }

    public function getFailureReason() : string
    {
        return $this->getAttribute('failure_reason');
    }

    public function getRecipientId() : int
    {
        return $this->getRecipient()->getContactId();
    }

    public function getOccurredAt() : Carbon
    {
        return $this->getAttribute('occurred_at');
    }

    public function getType() : EventType
    {
        switch (strtolower($this->getTypeName())) {
            case self::EVENT_TYPE_ACCEPTED:
                return new Accepted;
            case self::EVENT_TYPE_REJECTED:
                return new Rejected;
            case self::EVENT_TYPE_FAILED:
                return ($this->getSeverityIdByName($this->payload['severity'])
                    ? new HardBounce
                    : new SoftBounce());
            case self::EVENT_TYPE_DELIVERED:
                return new Delivered;
            case self::EVENT_TYPE_OPENED:
                return new Open;
            case self::EVENT_TYPE_CLICKED:
                return new Click;
            case self::EVENT_TYPE_COMPLAINED:
                return new SpamComplaint;
            case self::EVENT_TYPE_UNSUBSCRIBED:
                return new Unsubscribe;
            default:
                return new Unknown;
        }
    }

    public static function getSeverityIdByName(?string $severity) : bool
    {
        return ($severity == self::HARDBOUNCE_SEVERITY);
    }

    public function getEventTypeId() : int
    {
        return $this->getAttribute('event_type_id');
    }

    public function getTypeName() : string
    {
        return MailgunEventType::getNameForEventId($this->getAttribute('event_type_id'));
    }

    protected function setPayloadAttribute($event) : void
    {
        if ($event instanceof Event) {
            $event = extract_props($event);

            $event = collect($event)->mapWithKeys(function ($value, $key) {
                return [Str::snake($key, '-') => $value];
            })->toArray();
        }

        if (! is_array($event)) {
            throw new \InvalidArgumentException("Must provide array or Event");
        }

        $this->attributes['payload'] = $this->asJson($event);
    }

    public function getDeliveryStatusCode() : ?int
    {
        return Arr::get($this->payload, 'delivery-status.code');
    }

    public function shouldBeSuppressedDueToOverQuota() : bool
    {
        return $this->isHardBounce()
               && in_array($this->getDeliveryStatusCode(), [
                SMTPReplyCodes::MAILBOX_UNAVAILABLE,
                SMTPReplyCodes::TOO_MANY_EMAILS_OR_TOO_MANY_RECIPIENTS,
                SMTPReplyCodes::STORAGE_ALLOCATION_EXCEEDED,
            ]);
    }

    public function shouldBeSuppressedDueToMxError() : bool
    {
        return is_a($this->getType(), HardBounce::class)
               && in_array($this->getDeliveryStatusCode(), [
                SMTPReplyCodes::TEMPORARY_MX_ERROR,
                SMTPReplyCodes::PERMANENT_MX_ERROR,
            ]);
    }

    public function isHardBounce() : bool
    {
        return is_a($this->getType(), HardBounce::class);
    }
}
