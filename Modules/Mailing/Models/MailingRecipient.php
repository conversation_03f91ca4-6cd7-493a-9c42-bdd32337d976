<?php

namespace Modules\Mailing\Models;

use App\Traits\BelongsToAccount;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;
use Laravel\Scout\Searchable;
use Modules\Mailing\BulkMailing\Recipient;
use Modules\Mailing\DataTransferObjects\MailingCardSummaryDTO;
use Modules\Mailing\Traits\UsesMailingDatabase;

/**
 * Class MailingRecipient
 * @package App
 * @property EmailAddress $email
 * @property int          $contact_id
 * @property bool         $is_delivered
 * @property bool         $hard_bounced
 * @property int          $soft_bounces
 * @property int          $spam_complaints
 * @property bool         $unsubscribed
 * @method static Builder|self forMailing(int $mailingId)
 * @method static Builder|self query()
 */
class MailingRecipient extends Model
{
    use UsesMailingDatabase;
    use BelongsToAccount;
    use Searchable;

    protected $guarded = [];

    protected $attributes = [
        'is_delivered'    => false,
        'opens'           => 0,
        'clicks'          => 0,
        'soft_bounces'    => 0,
        'spam_complaints' => 0,
        'unsubscribed'    => false,
        'hard_bounced'    => false,
    ];

    protected $casts = [
        'opens'        => 'int',
        'is_delivered' => 'boolean',
        'unsubscribed' => 'boolean',
        'hard_bounced' => 'boolean',
    ];

    public static function createFromMailingAndRecipient(Mailing $mailing, Recipient $recipient)
    {
        return static::create([
            'account_id'       => $mailing->getAccountId(),
            'mailing_id'       => $mailing->getId(),
            'contact_uuid'    => $recipient->contactUuid,
            'email_address_id' => $recipient->email->id,
        ]);
    }

    public static function getForMailingAndEmailAddress(Mailing $mailing, EmailAddress $emailAddress): ?self
    {
        return self::firstOrCreate([
            'mailing_id'       => $mailing->getId(),
            'email_address_id' => $emailAddress->id,
        ], [
            'account_id' => $mailing->getAccountId(),
            'contact_id' => 0,
            'contact_uuid' => "",
        ]);
    }

    public function mailing(): BelongsTo
    {
        return $this->belongsTo(Mailing::class);
    }

    public function email(): BelongsTo
    {
        return $this->belongsTo(EmailAddress::class, 'email_address_id', 'id');
    }

    public function getEmail(): string
    {
        return $this->email->getEmail();
    }

    public function getContactId(): int
    {
        return (int)$this->contact_id;
    }

    public function getEmailAddress(): EmailAddress
    {
        return $this->email;
    }

    public function events(): HasMany
    {
        return $this->hasMany(MailingRecipientEvent::class);
    }

    public static function getEmailIdsOfPastRecipientsOfBook(int $bookId): Collection
    {
        return static::query()
            ->whereHas('mailing', function ($query) use ($bookId) {
                $query->where('book_id', $bookId);
            })
            ->groupBy(['email_address_id'])
            ->pluck('email_address_id');
    }

    public static function getEmailIdsOfPastRecipientsOfMailings(Collection $mailingIds) : Collection
    {
        return static::query()
            ->whereHas('mailing', function ($query) use ($mailingIds) {
                $query->whereIn('id', $mailingIds);
            })
            ->groupBy(['email_address_id'])
            ->pluck('email_address_id');
    }

    public static function getEmailAddressesAndIdsOfPastRecipientsOfMailings(Collection $mailingIds) : Collection
    {
        return static::query()
            ->with('email_addresses.address')
            ->join('email_addresses', 'email_addresses.id', 'email_address_id')
            ->whereIn('mailing_id', $mailingIds)
            ->groupBy(['email_addresses.address'])
            ->pluck('address');
    }

    public function addEvent(MailingRecipientEvent $event) : self
    {
        $data = null;

        switch ($event->event_type_id) {
            case EventType::DELIVERED:
                $data = ['is_delivered' => true];
                break;
            case EventType::OPEN:
                $data = ['opens' => min($this->opens + 1, 255)];
                break;
            case EventType::CLICK:
                $data = ['clicks' => min($this->clicks + 1, 255)];
                break;
            case EventType::SOFT_BOUNCE:
                $data = ['soft_bounces' => $this->soft_bounces + 1];
                break;
            case EventType::SPAM_COMPLAINT:
                $data = ['spam_complaints' => $this->spam_complaints + 1];
                break;
            case EventType::UNSUBSCRIBE:
                $data = ['unsubscribed' => true];
                break;
            case EventType::HARD_BOUNCE:
                $data = ['hard_bounced' => true];
                break;
        }

        $this->update($data);

        return $this;
    }

    public function scopeForMailing(Builder $query, int $mailingId): void
    {
        $query->where('mailing_id', $mailingId);
    }

    public static function calculateEmailRating(Collection $recipients): int
    {
        //if no recipients passed, return 0
        if ($recipients->isEmpty()) {
            return EmailAddress::DEFAULT_EMAIL_RATING;
        }

        //recalculate the rating base on percentages
        // 50% - num recipients with click / total number of recipients * 0.5
        // 50% - if at least 1 delivered, 0.5, otherwise 0

        //Click Rating
        $numRecipientsWithClicks = $recipients->filter(function (MailingRecipient $recipient) {
            return $recipient->clicks > 0;
        })->count();

        $clickRating = ($numRecipientsWithClicks / $recipients->count()) * 0.5;

        //Delivered Rating
        $numRecipientsWithDelivered = $recipients->filter(function (MailingRecipient $recipient) {
            return $recipient->is_delivered > 0;
        })->count();

        $deliveredRating = min($numRecipientsWithDelivered, 1) * 0.5;

        //new rating is the 2 calculated ratings * 100, then rounded
        $newRating = round( ($clickRating + $deliveredRating) * 100);

        //return the calculated rating
        return $newRating;
    }

    public static function getMailingCardSummaryForMailing(Mailing $mailing) : MailingCardSummaryDTO
    {
        //Query the summary from the database
        $summary = self::selectRaw(
            'COUNT(*) as recipients_count, ' .
            'IFNULL(SUM(IF(is_delivered > 0 || opens > 0 || clicks > 0, 1, 0)), 0) AS delivered, ' .
            'IFNULL(SUM(IF(opens > 0 || clicks > 0, 1, 0)), 0) AS opens, ' .
            'IFNULL(SUM(IF(clicks > 0, 1, 0)), 0) AS clicks'
        )
            ->forMailing($mailing->id)
            ->first();

        //return a new instance of self
        return new MailingCardSummaryDTO([
            'recipients_count'  => (int) $summary->recipients_count,
            'delivered'         => (int) $summary->delivered,
            'opens'             => (int) $summary->opens,
            'clicks'            => (int) $summary->clicks,
            'open_rate'         => $summary->opens >= 0 && $summary->recipients_count > 0
                ? (float)($summary->opens / $summary->recipients_count) * 100
                : 0.0,
            'click_rate'        => $summary->clicks >= 0 && $summary->recipients_count > 0
                ? (float)($summary->clicks / $summary->recipients_count) * 100
                : 0.0
        ]);
    }
}
