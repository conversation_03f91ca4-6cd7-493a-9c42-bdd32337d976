<?php

namespace Modules\Mailing\Models;

use App\Traits\BelongsToAccount;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Mailing\Traits\UsesMailingDatabase;

/**
 * Class AccountMailgunDomain
 * @package Modules\Mailing\Models
 * @property \Modules\Mailing\Models\MailgunDomain $domain
 */
class AccountMailgunDomain extends Model
{
    use UsesMailingDatabase, BelongsToAccount;

    protected $guarded = [];

    public function domain() : BelongsTo
    {
        return $this->belongsTo(MailgunDomain::class, 'mailgun_domain_id');
    }

    public function getDomain() : string
    {
        return $this->domain->getDomain();
    }
}
