<?php

namespace Modules\Mailing\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Modules\Mailing\Traits\UsesMailingDatabase;

class MailgunEventType extends Model
{
    use UsesMailingDatabase;
    const ACCEPTED = 1;
    const REJECTED = 2;
    const DELIVERED = 3;
    const FAILED = 4;
    const OPENED = 5;
    const CLICKED = 6;
    const UNSUBSCRIBED = 7;
    const COMPLAINED = 8;
    const STORED = 9;
    const TYPE_NAMES = [
        self::ACCEPTED     => 'accepted',
        self::REJECTED     => 'rejected',
        self::DELIVERED    => 'delivered',
        self::FAILED       => 'failed',
        self::OPENED       => 'opened',
        self::CLICKED      => 'clicked',
        self::UNSUBSCRIBED => 'unsubscribed',
        self::COMPLAINED   => 'complained',
        self::STORED       => 'stored',
    ];

    public $timestamps = false;

    public static function getIdForEventName(string $eventName) : ?int
    {
        return array_search(strtolower($eventName), self::TYPE_NAMES);
    }

    public static function getNameForEventId(int $eventId) : ?string
    {
        return self::where('id', $eventId)->pluck('name')->first();
    }

    public function scopeForName(Builder $query, string $name) : void
    {
        $query->where('name', $name);
    }
}
