<?php

namespace Modules\Mailing\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Mailing\Traits\UsesMailingDatabase;

/**
 * Class MailgunDomainRating
 * @package Modules\Mailing\Models
 * @property MailgunDomain $domain
 */
class MailgunDomainRating extends Model
{
    use UsesMailingDatabase;

    protected $guarded = [];

    protected $casts = [
        'minimum_rating' => 'int',
        'maximum_rating' => 'int',
    ];

    public function domain() : BelongsTo
    {
        return $this->belongsTo(MailgunDomain::class, 'mailgun_domain_id', 'id');
    }

    public function getDomain() : string
    {
        return $this->domain->getDomain();
    }

    public function getMinimumRating() : int
    {
        return $this->getAttribute('minimum_rating');
    }

    public function getMaximumRating() : int
    {
        return $this->getAttribute('maximum_rating');
    }

    public function getProduct() : int
    {
        return $this->getAttribute('product');
    }

    public function scopeForProduct(Builder $query, int $product) : Builder
    {
        return $query->where('product', $product);
    }
}
