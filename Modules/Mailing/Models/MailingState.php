<?php

namespace Modules\Mailing\Models;

// todo: should we keep mailing_state
use Illuminate\Database\Eloquent\Model;
use Modules\Mailing\Traits\UsesMailingDatabase;

class MailingState extends Model
{
    use UsesMailingDatabase;

    protected $fillable = [
        'mailing_id',
        'transition',
        'from',
        'to',
    ];

    public function mailing()
    {
        return $this->belongsTo(Mailing::class);
    }
}
