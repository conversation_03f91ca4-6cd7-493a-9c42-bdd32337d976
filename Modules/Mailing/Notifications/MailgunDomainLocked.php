<?php

namespace Modules\Mailing\Notifications;

use App\TeamsNotification\Section;
use App\TeamsNotification\TeamsChannel;
use App\TeamsNotification\TeamsMessage;
use Carbon\Carbon;
use Illuminate\Notifications\Notification;

class MailgunDomainLocked extends Notification
{
    /** @var string */
    private $domain;

    /** @var \Carbon\Carbon */
    public $occurredAt;

    public function __construct(string $domain, Carbon $occurredAt)
    {
        $this->domain = $domain;
        $this->occurredAt = $occurredAt;
    }

    public function via()
    {
        return [TeamsChannel::class];
    }

    public function toMicrosoftTeamsMessage()
    {
        return TeamsMessage::create('Mailgun domain is locked')
            ->newSection(
                (new Section)
                    ->title(sprintf('Domain %s locked', $this->domain))
                    ->subtitle($this->occurredAt->setTimezone('est')->format('D, d M y H:i:s'))
                    ->addFact('Domain', $this->domain)
                    ->addFact(
                        'See more',
                        sprintf(
                            'See [Mailgun UI](https://app.mailgun.com/app/domains/%s) for more details',
                            $this->domain
                        )
                    )
            );
    }
}
