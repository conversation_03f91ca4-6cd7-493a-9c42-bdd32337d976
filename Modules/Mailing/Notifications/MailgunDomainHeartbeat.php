<?php

namespace Modules\Mailing\Notifications;

use App\TeamsNotification\TeamsChannel;
use App\TeamsNotification\TeamsMessage;
use Carbon\Carbon;
use Illuminate\Notifications\Notification;

class MailgunDomainHeartbeat extends Notification
{
    /** @var string */
    private $domain;

    /** @var \Carbon\Carbon */
    public $occurredAt;

    public function __construct(string $domain, Carbon $occurredAt)
    {
        $this->domain = $domain;
        $this->occurredAt = $occurredAt;
    }

    public function via()
    {
        return [TeamsChannel::class];
    }

    public function toMicrosoftTeamsMessage()
    {
        return TeamsMessage::create('Mailgun domain is OK')
            ->newSection([
                'activityTitle'    => sprintf('Domain %s is OK', $this->domain),
                'activitySubtitle' => $this->occurredAt->setTimezone('est')->format('D, d M y H:i:s'),
                'activityImage'    => 'https://cdn3.iconfinder.com/data/icons/flat-actions-icons-9/792/Tick_Mark_Dark-512.png',
                'activityText'     => sprintf(
                    'Domain has been detected as ACTIVE. See [Mailgun UI](https://app.mailgun.com/app/domains/%s) for more details',
                    $this->domain
                ),
            ]);
    }
}
