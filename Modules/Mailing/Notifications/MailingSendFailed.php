<?php

namespace Modules\Mailing\Notifications;

use App\TeamsNotification\Section;
use App\TeamsNotification\TeamsChannel;
use App\TeamsNotification\TeamsMessage;
use Carbon\Carbon;
use Exception;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Modules\Mailing\Models\Mailing;

class MailingSendFailed extends Notification
{
    /** @var \Modules\Mailing\Models\Mailing */
    public $mailing;

    /** @var \Carbon\Carbon */
    public $occurredAt;

    /** @var \Exception|null */
    public $exception;

    /** @var int */
    private $count;

    public function __construct(Mailing $mailing, Carbon $occurredAt, ?Exception $exception, int $count)
    {
        $this->mailing = $mailing;
        $this->occurredAt = $occurredAt;
        $this->exception = $exception;
        $this->count = $count;
    }

    public function via(): array
    {
        return [TeamsChannel::class];
    }

    public function toMicrosoftTeamsMessage(): TeamsMessage
    {
        return TeamsMessage::create('Failed to send mailing ' . $this->count . str_plural('time', $this->count))
            ->text(
                "An exception occurred when sending a customer's email. " . $this->count . " mailings have failed in the last 10 minutes."
            )
            ->newSection(
                (new Section())
                    ->title('Mailing Info')
                    ->addFact('Account ID', $this->mailing->getAccountId())
                    ->addFact('Mailing ID', $this->mailing->getId())
                    ->addFact('Occurred At', $this->occurredAt->setTimezone('est')->format('D, d M y H:i:s'))
            )->newSection(
                (new Section())
                    ->title('Exception Info')
                    ->subtitle($this->exception->getMessage())
                    ->addFact('Exception', get_class($this->exception))
                    ->addFact('File', $this->exception->getFile())
                    ->addFact('Line', $this->exception->getLine())
            )->newSection(
                (new Section())
                    ->title('Environment')
                    ->addFact('Server', gethostname())
                    ->addFact('Url', config('app.url'))
                    ->addFact('Environment', config('app.env'))
            );
    }

    public function toMail(): MailMessage
    {
        return (new MailMessage)
            ->greeting('Failed to send mailing')
            ->view('emails.send-mail-failed', [
                'accountId'        => $this->mailing->getAccountId(),
                'mailingId'        => $this->mailing->getId(),
                'exceptionMessage' => optional($this->exception)->getMessage(),
                'stackTrace'       => optional($this->exception)->getTraceAsString(),
                'count'            => $this->count
            ]);
    }
}
