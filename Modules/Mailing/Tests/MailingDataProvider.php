<?php

namespace Modules\Mailing\Tests;

use Modules\Mailing\Models\Mailing;
use Modules\Mailing\Models\MailingRecipient;
use Modules\Mailing\Models\MailingRecipientEvent;
use DateTimeInterface;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Collection;

class MailingDataProvider
{
    use WithFaker;

    /** @var int|null */
    public $accountId;

    /** @var \Modules\Mailing\Models\Mailing */
    public $mailing;

    /** @var \Illuminate\Support\Collection */
    public $recipients;

    /** @var int */
    public $eventCount;

    /** @var int */
    private $contactId = 1;

    public function __construct(int $accountId)
    {
        $this->recipients = collect();
        $this->accountId = $accountId;
        $this->setUpFaker();
        $this->faker->unique();
    }

    public static function forAccountId(int $accountId) : self
    {
        return new self($accountId);
    }

    public function withSentMailing(?DateTimeInterface $sentWhen = null) : self
    {
        $this->mailing = factory(Mailing::class)->create([
            'account_id' => $this->accountId,
            'send_at'    => $sentWhen ?? $this->faker->dateTimeBetween('-1 year'),
            'state'      => 'sent',
        ]);

        return $this;
    }

    public function withRecipientsWhoDeliveredOnly(int $count = 5) : self
    {
        if (! $this->recipients->has('delivered')) {
            $this->recipients['delivered'] = collect();
        }

        $this->recipients['delivered'] = $this->recipients['delivered']
            ->merge(
                $this->createRecipients($count)
                    ->each(function ($recipient) {
                        $this->generateEventsForRecipient($recipient, ['delivered']);
                    })
            );

        return $this;
    }

    public function withRecipientsWhoOpened(int $count = 5) : self
    {
        if (! $this->recipients->has('opened')) {
            $this->recipients['opened'] = collect();
        }

        $this->recipients['opened'] = $this->recipients['opened']
            ->merge(
                $this->createRecipients($count)
                    ->each(function ($recipient) {
                        $this->generateEventsForRecipient($recipient, ['delivered', 'open']);
                    })
            );

        return $this;
    }

    public function withRecipientsWhoOnlyClicked(int $count = 5) : self
    {
        if (! $this->recipients->has('clicked')) {
            $this->recipients['clicked'] = collect();
        }

        $this->recipients['clicked'] = $this->recipients['clicked']
            ->merge(
                $this->createRecipients($count)
                    ->each(function ($recipient) {
                        $this->generateEventsForRecipient($recipient, ['delivered', 'click']);
                    })
            );

        return $this;
    }

    public function withRecipientsWhoOpenedAndClicked(int $count = 5) : self
    {
        if (! $this->recipients->has('opened-clicked')) {
            $this->recipients['opened-clicked'] = collect();
        }

        $this->recipients['opened-clicked'] = $this->recipients['opened-clicked']
            ->merge(
                $this->createRecipients($count)
                    ->each(function ($recipient) {
                        $this->generateEventsForRecipient($recipient, ['delivered', 'open', 'click']);
                    })
            );

        return $this;
    }

    public function withRecipientsWhoHardbounced(int $count = 5) : self
    {
        if (! $this->recipients->has('hardbounced')) {
            $this->recipients['hardbounced'] = collect();
        }

        $this->recipients['hardbounced'] = $this->recipients['hardbounced']
            ->merge(
                $this->createRecipients($count)
                    ->each(function ($recipient) {
                        $this->generateEventsForRecipient($recipient, ['softbounce', 'hardbounce']);
                    })
            );

        return $this;
    }

    public function withRecipientsWhoComplained(int $count = 5) : self
    {
        if (! $this->recipients->has('complained')) {
            $this->recipients['complained'] = collect();
        }

        $this->recipients['complained'] = $this->recipients['complained']
            ->merge(
                $this->createRecipients($count)
                    ->each(function ($recipient) {
                        $this->generateEventsForRecipient($recipient, ['delivered', 'spamcomplaint']);
                    })
            );

        return $this;
    }

    public function withRecipientsWhoUnsubscribed(int $count = 5) : self
    {
        if (! $this->recipients->has('unsubscribed')) {
            $this->recipients['unsubscribed'] = collect();
        }

        $this->recipients['unsubscribed'] = $this->recipients['unsubscribed']
            ->merge(
                $this->createRecipients($count)
                    ->each(function ($recipient) {
                        $this->generateEventsForRecipient($recipient, ['delivered', 'unsubscribe']);
                    })
            );

        return $this;
    }

    protected function generateEventsForRecipient(
        MailingRecipient $recipient, ?array $types = [], ?DateTimeInterface $when = null
    ) : void {
        foreach ($types as $type) {

            factory(MailingRecipientEvent::class)
                ->state($type)
                ->create([
                    'mailing_recipient_id' => $recipient->id,
                    'occurred_at'          => $when ?? $this->faker->dateTimeBetween('-1 year'),
                ]);
        }
    }

    protected function createRecipients(int $count) : Collection
    {
        $out = collect();

        foreach (range($this->contactId, ($this->contactId + ($count - 1))) as $contactId) {
            $out->push(
                factory(MailingRecipient::class)
                    ->create([
                        'mailing_id' => $this->mailing->id,
                        'account_id' => $this->mailing->account_id,
                        'contact_id' => $contactId,
                    ])
            );
            $this->contactId++;
        }

        return $out;
    }
}
