<?php

namespace Modules\Mailing\Tests;

use Illuminate\Contracts\Console\Kernel;
use Illuminate\Support\Facades\DB;

trait RefreshMailingDatabase
{
    /**
     * Define hooks to migrate the database before and after each test.
     *
     * @return void
     */
    public function refreshMailingDatabase()
    {
        if (!RefreshMailingDatabaseState::$migrated) {
            $this->refreshMailingTestDatabase();
        }

        $this->beginDatabaseTransaction();
    }

    /**
     * Begin a database transaction on the testing database.
     *
     * @return void
     */
    public function beginDatabaseTransaction()
    {
        $database = $this->app->make('db');

        foreach ($this->connectionsToTransact() as $name) {
            $connection = $database->connection($name);
            $dispatcher = $connection->getEventDispatcher();

            $connection->unsetEventDispatcher();
            $connection->beginTransaction();
            $connection->setEventDispatcher($dispatcher);
        }

        $this->beforeApplicationDestroyed(function () use ($database) {
            foreach ($this->connectionsToTransact() as $name) {
                $connection = $database->connection($name);
                $dispatcher = $connection->getEventDispatcher();

                $connection->unsetEventDispatcher();
                $connection->rollback();
                $connection->setEventDispatcher($dispatcher);
                $connection->disconnect();
            }
        });
    }

    /**
     * The database connections that should have transactions.
     *
     * @return array
     */
    protected function connectionsToTransact()
    {
        return ['mailing'];
    }

    protected function refreshMailingTestDatabase(): void
    {
        $defaultConnection = DB::getDefaultConnection();

        $this->artisan(
            'migrate:fresh',
            [
                '--database' => 'mailing',
                '--path' => __DIR__ . '/../Database/Migrations',
                '--realpath' => true,
            ]
        );

        $this->app[Kernel::class]->setArtisan(null);

        DB::setDefaultConnection($defaultConnection);

        RefreshMailingDatabaseState::$migrated = true;
    }
}
