<?php

namespace Modules\Mailing\Tests;

use Modules\Mailing\BulkMailing\Contracts\BodyFactory;
use Modules\Mailing\BulkMailing\Exceptions\InvalidProductException;
use Modules\Mailing\Models\Mailing;
use Illuminate\Foundation\Testing\WithFaker;

class FakeBodyFactory implements BodyFactory
{
    use WithFaker;

    /** @var bool */
    private $throwInvalidProduct;

    public function __construct(bool $throwInvalidProduct = false)
    {
        $this->setUpFaker();
        $this->throwInvalidProduct = $throwInvalidProduct;
    }

    public function renderSubject(Mailing $mailing) : string
    {
        return $mailing->getSubject();
    }

    public function renderHtml(Mailing $mailing) : string
    {
        if ($this->throwInvalidProduct) {
            throw InvalidProductException::forMailing($mailing);
        }

        return $this->faker->randomHtml();
    }

    public function renderText(Mailing $mailing) : string
    {
        if ($this->throwInvalidProduct) {
            throw InvalidProductException::forMailing($mailing);
        }

        return $this->faker->paragraphs(3, true);
    }
}
