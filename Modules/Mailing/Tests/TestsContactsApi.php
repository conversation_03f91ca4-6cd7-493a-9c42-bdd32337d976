<?php

namespace Modules\Mailing\Tests;

use Faker\Factory;
use Illuminate\Support\Collection;
use Modules\Mailing\Contracts\ContactsRepository;
use Modules\Mailing\Entities\Contact;

trait TestsContactsApi
{
    /** @var \Mockery\LegacyMockInterface|\Mockery\MockInterface|\Modules\Mailing\Contracts\ContactsRepository */
    public $contactsRepositoryMock;

    public function setupMockContactsCollection(int $numberOfContacts = 10): Collection
    {
        $contacts = $this->buildContactCollection($numberOfContacts);

        $mock = $this->getContactsRepositoryMock();

        $mock->shouldReceive('getForAccountAndGroups')
            ->andReturn($contacts);

        return $contacts;
    }

    public function setContactsToReturnEmpty()
    {
        $mock = $this->getContactsRepositoryMock();

        $mock->shouldReceive('getForAccountAndGroups')
            ->andReturn(collect());
    }

    private function buildContactCollection($numberOfContacts) : Collection
    {
        $out = collect();

        $faker = Factory::create();

        while ($numberOfContacts > 0) {
            $out->push(new Contact(
                $faker->uuid,
                $faker->email,
                $faker->firstName,
                $faker->lastName
            ));
            $numberOfContacts--;
        }

        return $out;
    }

    /**
     * @return \Mockery\LegacyMockInterface|\Mockery\MockInterface|\Modules\Mailing\Contracts\ContactsRepository
     */
    protected function getContactsRepositoryMock()
    {
        if (! $this->contactsRepositoryMock) {
            $this->contactsRepositoryMock = $this->mock(ContactsRepository::class);
        }

        return $this->contactsRepositoryMock;
    }
}
