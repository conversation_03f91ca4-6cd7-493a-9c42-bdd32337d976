<?php

namespace Modules\Mailing\Tests\Integration;

use App\Context\AccountId;
use App\Models\Account;
use App\Models\Plan;
use Carbon\Carbon;
use Domain\Mailings\Services\ReplacementValuesService;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Mail;
use Mailgun\Api\Message;
use Mailgun\Mailgun;
use Mailgun\Message\BatchMessage;
use Mockery;
use Modules\Mailing\BulkMailing\Recipient;
use Modules\Mailing\BulkMailing\Transport\BatchMailgunTransport;
use Modules\Mailing\BulkMailing\Transport\LaravelTransport;
use Modules\Mailing\Mail\BulkMailable;
use Modules\Mailing\Models\AccountMailgunDomain;
use Modules\Mailing\Models\EmailAddress;
use Modules\Mailing\Models\ExternalMessage;
use Modules\Mailing\Models\Mailing;
use Modules\Mailing\Tests\RefreshMailingDatabase;
use Modules\Mailing\Tests\TestCase;

class MailingReplacementValuesTest extends TestCase
{
    use RefreshMailingDatabase;
    use WithFaker;

    /** @var string */
    private $baseLpUrl = "http://qa-tester123.reminderlp.com/market-analysis";

    /** @var \Mockery\Mock|BatchMessage */
    private $batchMessageMock;

    /** @var \Mailgun\Api\Message|\Mockery\LegacyMockInterface|\Mockery\MockInterface */
    private $messageApiMock;

    /** @var \Illuminate\Database\Eloquent\Model|mixed */
    private $mailing;

    protected function setUp(): void
    {
        parent::setUp();

        // Fake account for job serializing
        $account = factory(Account::class)->make(['id' => AccountId::current()->id()]);
        $this->setupAccountRepositoryMockForAccount($account);

        Config::set('bulk-mailing.enabled', true);
    }

    /** @test */
    public function it_updates_the_replacement_values_using_the_default_transport()
    {
        // Setup fake mailable
        $mailable = $this->setupBulkMailable();
        $recipient = $this->createRecipient();

        // Fake mail
        Mail::fake();

        // test with LaravelTransport because we can't test directly with Mailgun
        /** @var LaravelTransport $batchTransport */
        $batchTransport = $this->app->make(LaravelTransport::class);

        // Send the email!
        $batchTransport->send($mailable, collect([$recipient]));

        $verifyString = $this->getReplacedValues(
            $this->getTestString(),
            $recipient->firstName,
            $recipient->lastName,
            base64_encode($recipient->contactUuid),
            base64_encode($this->mailing->id)
        );

        Mail::assertQueued(BulkMailable::class, function ($mail) use ($verifyString) {
            return $mail->buildHtml() == $verifyString
                && $mail->textView == $verifyString;
        });
    }

    /** @test */
    public function it_defaults_first_name_to_friend_using_the_default_transport()
    {
        // Setup fake mailable
        $mailable = $this->setupBulkMailable();
        $recipient = $this->createRecipient();

        //reset the recipient first name
        $recipient->firstName = null;

        // Fake mail
        Mail::fake();

        // test with LaravelTransport because we can't test directly with Mailgun
        /** @var LaravelTransport $batchTransport */
        $batchTransport = $this->app->make(LaravelTransport::class);

        // Send the email!
        $batchTransport->send($mailable, collect([$recipient]));

        $verifyString = $this->getReplacedValues(
            $this->getTestString(),
            "Friend",
            $recipient->lastName,
            base64_encode($recipient->contactUuid),
            base64_encode($this->mailing->id)
        );

        Mail::assertQueued(BulkMailable::class, function ($mail) use ($verifyString) {
            return $mail->buildHtml() == $verifyString;
        });
    }

    /** @test */
    public function it_updates_the_replacement_values_using_the_mailgun_transport()
    {
        // Setup fake mailable
        $mailable = $this->setupBulkMailable();
        $recipient = $this->createRecipient();

        //get the expected string (mailgun uses a custom variables for the recipient)
        $verifyString = $this->getReplacedValues(
            $this->getTestString(),
            '%recipient.first%',
            '%recipient.last%',
            '%recipient.id%',
            base64_encode($this->mailing->id)
        );

        // Setup Mailgun Mock
        $this->setUpMailgunMock();

        //set the mock to expect that string - THIS IS THE TEST
        // other test classes verify that the mailgun transport will dispatch the correct event
        $this->batchMessageMock->expects('setSubject')->with($verifyString)->andReturnSelf();
        $this->batchMessageMock->expects('setHtmlBody')->with($verifyString)->andReturnSelf();
        $this->batchMessageMock->expects('setTextBody')->with($verifyString)->andReturnSelf();

        //verify the transport is also sending the correct replacement values
        $this->batchMessageMock->expects('addToRecipient')->with(
            $recipient->email->address,
            [
                'first' => $recipient->firstName,
                'last' => $recipient->lastName,
                'id' => base64_encode($recipient->contactUuid),
            ]
        )->andReturnSelf();

        //fake events
        Event::fake();

        /** @var BatchMailgunTransport $batchTransport */
        $batchTransport = $this->app->make(BatchMailgunTransport::class);

        // Send the email!
        $batchTransport->send($mailable, collect([$recipient]));
    }

    /** @test */
    public function it_defaults_first_name_to_friend_using_the_mailgun_transport()
    {
        // Setup fake mailable
        $mailable = $this->setupBulkMailable();
        $recipient = $this->createRecipient();

        //reset the recipient first name
        $recipient->firstName = null;

        // Setup Mailgun Mock
        $this->setUpMailgunMock();

        //verify the transport is also sending the correct replacement values
        $this->batchMessageMock->expects('addToRecipient')->with(
            $recipient->email->address,
            [
                'first' => 'Friend',
                'last' => $recipient->lastName,
                'id' => base64_encode($recipient->contactUuid),
            ]
        )->andReturnSelf();

        //fake events
        Event::fake();

        /** @var BatchMailgunTransport $batchTransport */
        $batchTransport = $this->app->make(BatchMailgunTransport::class);

        // Send the email!
        $batchTransport->send($mailable, collect([$recipient]));
    }

    private function createRecipient(): Recipient
    {
        $recipient = new Recipient;
        $recipient->contactUuid = $this->faker->uuid;
        $recipient->email = factory(EmailAddress::class)->create();
        $recipient->firstName = $this->faker->firstName;
        $recipient->lastName = $this->faker->lastName;
        $recipient->rating = 50;

        return $recipient;
    }

    private function getReplacedValues(
        string $content,
        string $firstName,
        string $lastName,
        string $recipientId,
        string $mailingId
    ): string {
        return str_replace(
            [
                ReplacementValuesService::REPLACEMENT_VALUE_FIRST_NAME,
                ReplacementValuesService::REPLACEMENT_VALUE_LAST_NAME,
                ReplacementValuesService::REPLACEMENT_VALUE_RECIPIENT_ID,
                ReplacementValuesService::REPLACEMENT_VALUE_MAILING_ID
            ],
            [
                $firstName,
                $lastName,
                $recipientId,
                $mailingId
            ],
            $content
        );
    }

    private function getTestString(): string
    {
        return "This test string includes first name: "
            . ReplacementValuesService::REPLACEMENT_VALUE_FIRST_NAME
            . ", last name: "
            . ReplacementValuesService::REPLACEMENT_VALUE_LAST_NAME
            . ", and a url: "
            . $this->baseLpUrl . "?p=123&pi=2&rid="
            . ReplacementValuesService::REPLACEMENT_VALUE_RECIPIENT_ID
            . "&mid="
            . ReplacementValuesService::REPLACEMENT_VALUE_MAILING_ID;
    }

    private function setupBulkMailable(): BulkMailable
    {
        $testString = $this->getTestString();
        $this->mailing = factory(Mailing::class)->create([
            'product' => Plan::PLAN_ID_DIGITAL_EDITIONS,
            'featured_image_id' => null,
            'subject' => $testString,
            'html_body' => $testString,
            'text_body' => $testString,
            'send_at' => Carbon::now(),
            'properties' => []
        ]);

        return new BulkMailable($this->mailing);
    }

    /**
     * Set up mocks for Mailgun
     * @return void
     */
    private function setUpMailgunMock()
    {
        $this->batchMessageMock = Mockery::mock(BatchMessage::class)->makePartial();

        $this->messageApiMock = Mockery::mock(Message::class);

        $this->mock(Mailgun::class, function ($mock) {
            return $mock->shouldReceive('messages')
                ->andReturn($this->messageApiMock);
        });

        /** @var AccountMailgunDomain $accountDomain */
        $accountDomain = factory(AccountMailgunDomain::class)->create();

        $this->messageApiMock->shouldReceive('getBatchMessage')
            ->withArgs([$accountDomain->getDomain()])
            ->andReturn($this->batchMessageMock);

        // do this only once.
        $this->batchMessageMock->shouldReceive('finalize');
        $this->batchMessageMock->shouldReceive('getMessageIds')
            ->andReturn(
                [factory(ExternalMessage::class)->state('mailgun')->make()->id]
            );
    }
}
