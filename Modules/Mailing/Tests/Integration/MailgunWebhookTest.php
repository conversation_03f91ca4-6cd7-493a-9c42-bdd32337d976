<?php

namespace Modules\Mailing\Tests\Integration;

use Illuminate\Http\Response;
use Illuminate\Support\Facades\Bus;
use Mailgun\Api\Webhook;
use Mailgun\Mailgun;
use Modules\Mailing\Jobs\ProcessWebhookEvent;
use Modules\Mailing\Tests\TestCase;

class MailgunWebhookTest extends TestCase
{
    /** @var \Mockery\Mock */
    private $mailgunMock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mailgunMock = $this->mock(Mailgun::class);
    }

    /** @test */
    public function it_rejects_request_with_bad_content()
    {
        $this->postJson(route('api.mailgun-webhook'), [
            [
                'test' => 'this is a test',
            ],
        ])->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    /** @test */
    public function it_rejects_request_with_bad_method()
    {
        $this->getJson(route('api.mailgun-webhook'), [
            [
                'test' => 'this is a test',
            ],
        ])->assertStatus(Response::HTTP_METHOD_NOT_ALLOWED);
    }

    /** @test */
    public function it_rejects_request_with_bad_signature()
    {
        // Skip over the webhook verification.
        // We just want this to return false.
        $webhookMock = $this->mock(Webhook::class);
        $this->mailgunMock
            ->shouldReceive('webhooks')
            ->andReturn($webhookMock);

        $webhookMock
            ->shouldReceive('verifyWebhookSignature')
            ->andReturn(false);

        $this->postJson(
            route('api.mailgun-webhook'),
            json_decode(file_get_contents(__DIR__ . '/../stubs/webhook-post.json'), true)
        )
            ->assertStatus(Response::HTTP_NOT_ACCEPTABLE)
            ->assertSeeText('Unverified signature');
    }

    /** @test */
    public function it_dispatches_job_for_valid_post()
    {
        // Skip over the webhook verification.
        $webhookMock = $this->mock(Webhook::class);
        $this->mailgunMock
            ->shouldReceive('webhooks')
            ->andReturn($webhookMock);

        $webhookMock
            ->shouldReceive('verifyWebhookSignature')
            ->andReturn(true);

        Bus::fake();

        $this->postJson(
            route('api.mailgun-webhook'),
            json_decode(file_get_contents(__DIR__ . '/../stubs/webhook-post.json'), true)
        )->assertOk();

        Bus::assertDispatched(ProcessWebhookEvent::class);
    }
}
