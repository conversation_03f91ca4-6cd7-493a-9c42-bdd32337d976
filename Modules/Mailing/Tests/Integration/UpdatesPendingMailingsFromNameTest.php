<?php

namespace Modules\Mailing\Tests\Integration;

use App\Context\AccountId;
use App\Contracts\AccountRepository;
use App\Models\Account;
use Domain\DigitalEdition\Events\ContactBlockUpdated as DigitalEditionContactBlockUpdated;
use Domain\Profile\Events\DisplayNameUpdated;
use Illuminate\Foundation\Testing\WithFaker;
use Mockery;
use Modules\EmailMarketing\Domain\ContactBlock\Events\ContactBlockUpdated as LocalContentContactBlockUpdated;
use Modules\Mailing\Interservice\Listeners\FromNameSubscriber;
use Modules\Mailing\Models\Mailing;
use Modules\Mailing\Tests\RefreshMailingDatabase;
use Modules\Mailing\Tests\TestCase;
use ReminderMedia\Messaging\Message;

class UpdatesPendingMailingsFromNameTest extends TestCase
{
    use RefreshMailingDatabase, WithFaker;

    /** @var int */
    private $accountId;

    protected function setUp(): void
    {
        parent::setUp();

        $this->accountId = AccountId::current()->id();

        $account = factory(Account::class)->make([
            'id' => $this->accountId,
        ]);

        $mock = Mockery::mock(AccountRepository::class);
        $mock->shouldReceive('find')
            ->with($this->accountId)
            ->andReturn($account);

        $this->app->instance(
            AccountRepository::class,
            $mock
        );
    }

    /** @test */
    public function it_updates_on_display_name_updated_event()
    {
        factory(Mailing::class, 2)->state('digital-edition')->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        factory(Mailing::class, 2)->state('local-events')->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        event($this->makeDisplayNameUpdated('George Costanza'));

        Mailing::all()->each(function (Mailing $mailing) {
            $this->assertEquals('George Costanza', $mailing->getFromName());
        });
    }

    /** @test */
    public function it_does_not_update_on_display_name_updated_event_with_no_display_name()
    {
        factory(Mailing::class, 2)->state('digital-edition')->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        factory(Mailing::class, 2)->state('local-events')->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        event($this->makeDisplayNameUpdated());

        Mailing::all()->each(function (Mailing $mailing) {
            $this->assertEquals('Cosmo Kramer', $mailing->getFromName());
        });
    }

    /** @test */
    public function it_updates_on_digital_edition_contact_block_updated_event_with_display_name()
    {
        factory(Mailing::class, 2)->state('digital-edition')->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        event($this->makeDigitalEditionContactBlockUpdated([
            [
                'section'   => 'display_name',
                'item_type' => 'DisplayName',
                'custom'    => null,
                'item_data' => [
                    'display_name' => 'George Costanza',
                ],
            ],
        ]));

        Mailing::all()->each(function (Mailing $mailing) {
            $this->assertEquals('George Costanza', $mailing->getFromName());
        });
    }

    /** @test */
    public function it_updates_on_digital_edition_contact_block_updated_event_with_team_name()
    {
        factory(Mailing::class, 2)->state('digital-edition')->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        event($this->makeDigitalEditionContactBlockUpdated([
            [
                'section'   => 'display_name',
                'item_type' => 'Team',
                'custom'    => null,
                'item_data' => [
                    'name' => 'Seinfeld dudes',
                ],
            ],
        ]));

        Mailing::all()->each(function (Mailing $mailing) {
            $this->assertEquals('Seinfeld dudes', $mailing->getFromName());
        });
    }

    /** @test */
    public function it_updates_on_digital_edition_contact_block_updated_event_with_team_member_name()
    {
        factory(Mailing::class, 2)->state('digital-edition')->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        event($this->makeDigitalEditionContactBlockUpdated([
            [
                'section'   => 'display_name',
                'item_type' => 'TeamMember',
                'custom'    => null,
                'item_data' => [
                    'name' => 'Elaine Benes',
                ],
            ],
        ]));

        Mailing::all()->each(function (Mailing $mailing) {
            $this->assertEquals('Elaine Benes', $mailing->getFromName());
        });
    }

    /** @test */
    public function it_updates_on_digital_edition_contact_block_updated_event_with_custom_value()
    {
        factory(Mailing::class, 2)->state('digital-edition')->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        event($this->makeDigitalEditionContactBlockUpdated([
            [
                'section'   => 'display_name',
                'item_type' => 'DisplayName',
                'custom'    => 'Newman',
                'item_data' => null,
            ],
        ]));

        Mailing::all()->each(function (Mailing $mailing) {
            $this->assertEquals('Newman', $mailing->getFromName());
        });
    }

    /** @test */
    public function it_does_not_update_on_digital_edition_contact_block_updated_event_with_no_display_name()
    {
        factory(Mailing::class, 2)->state('digital-edition')->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        event($this->makeDigitalEditionContactBlockUpdated());

        Mailing::all()->each(function (Mailing $mailing) {
            $this->assertEquals('Cosmo Kramer', $mailing->getFromName());
        });
    }

    /** @test */
    public function it_does_not_update_local_events_mailings_on_digital_edition_contact_block_updated_event()
    {
        factory(Mailing::class, 2)->state('digital-edition')->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        factory(Mailing::class, 2)->state('local-events')->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        $event = $this->makeDigitalEditionContactBlockUpdated([
            [
                'section'   => 'display_name',
                'item_type' => 'DisplayName',
                'custom'    => 'Newman',
                'item_data' => null,
            ],
        ]);

        /** @var \Modules\Mailing\Interservice\Listeners\FromNameSubscriber $subscriber */
        $subscriber = app(FromNameSubscriber::class);

        $subscriber->onDigitalEditionContactBlockUpdated($event);

        Mailing::forDigitalEdition()->get()->each(function (Mailing $mailing) {
            $this->assertEquals('Newman', $mailing->getFromName());
        });

        Mailing::forLocalEvents()->get()->each(function (Mailing $mailing) {
            $this->assertEquals('Cosmo Kramer', $mailing->getFromName());
        });
    }

    /** @test */
    public function it_does_not_update_not_pending_mailings_on_digital_edition_contact_block_updated_event()
    {
        factory(Mailing::class, 2)->states(['digital-edition', 'sent'])->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        factory(Mailing::class, 2)->states(['digital-edition', 'failed'])->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        factory(Mailing::class, 2)->states(['digital-edition', 'stuck'])->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        $event = $this->makeDigitalEditionContactBlockUpdated([
            [
                'section'   => 'display_name',
                'item_type' => 'DisplayName',
                'custom'    => 'Newman',
                'item_data' => null,
            ],
        ]);

        /** @var \Modules\Mailing\Interservice\Listeners\FromNameSubscriber $subscriber */
        $subscriber = app(FromNameSubscriber::class);

        $subscriber->onDigitalEditionContactBlockUpdated($event);

        Mailing::all()->each(function (Mailing $mailing) {
            $this->assertEquals('Cosmo Kramer', $mailing->getFromName());
        });
    }

    /** @test */
    public function it_updates_on_local_events_contact_block_updated_event_with_display_name()
    {
        factory(Mailing::class, 2)->state('local-events')->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        $event = $this->makeLocalEventsContactBlockUpdated([
            [
                'section'   => 'display_name',
                'item_type' => 'DisplayName',
                'custom'    => null,
                'item_data' => [
                    'display_name' => 'George Costanza',
                ],
            ],
        ]);

        /** @var \Modules\Mailing\Interservice\Listeners\FromNameSubscriber $subscriber */
        $subscriber = app(FromNameSubscriber::class);

        $subscriber->onLocalEventsContactBlockUpdated($event);

        Mailing::all()->each(function (Mailing $mailing) {
            $this->assertEquals('George Costanza', $mailing->getFromName());
        });
    }

    /** @test */
    public function it_updates_on_local_events_contact_block_updated_event_with_team_name()
    {
        factory(Mailing::class, 2)->state('local-events')->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        $event = $this->makeLocalEventsContactBlockUpdated([
            [
                'section'   => 'display_name',
                'item_type' => 'Team',
                'custom'    => null,
                'item_data' => [
                    'name' => 'Seinfeld dudes',
                ],
            ],
        ]);

        /** @var \Modules\Mailing\Interservice\Listeners\FromNameSubscriber $subscriber */
        $subscriber = app(FromNameSubscriber::class);

        $subscriber->onLocalEventsContactBlockUpdated($event);

        Mailing::all()->each(function (Mailing $mailing) {
            $this->assertEquals('Seinfeld dudes', $mailing->getFromName());
        });
    }

    /** @test */
    public function it_updates_on_local_events_contact_block_updated_event_with_team_member_name()
    {
        factory(Mailing::class, 2)->state('local-events')->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        $event = $this->makeLocalEventsContactBlockUpdated([
            [
                'section'   => 'display_name',
                'item_type' => 'TeamMember',
                'custom'    => null,
                'item_data' => [
                    'name' => 'Elaine Benes',
                ],
            ],
        ]);

        /** @var \Modules\Mailing\Interservice\Listeners\FromNameSubscriber $subscriber */
        $subscriber = app(FromNameSubscriber::class);

        $subscriber->onLocalEventsContactBlockUpdated($event);

        Mailing::all()->each(function (Mailing $mailing) {
            $this->assertEquals('Elaine Benes', $mailing->getFromName());
        });
    }

    /** @test */
    public function it_updates_on_local_events_contact_block_updated_event_with_custom_value()
    {
        factory(Mailing::class, 2)->state('local-events')->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        $event = $this->makeLocalEventsContactBlockUpdated([
            [
                'section'   => 'display_name',
                'item_type' => 'DisplayName',
                'custom'    => 'Newman',
                'item_data' => null,
            ],
        ]);

        /** @var \Modules\Mailing\Interservice\Listeners\FromNameSubscriber $subscriber */
        $subscriber = app(FromNameSubscriber::class);

        $subscriber->onLocalEventsContactBlockUpdated($event);

        Mailing::all()->each(function (Mailing $mailing) {
            $this->assertEquals('Newman', $mailing->getFromName());
        });
    }

    /** @test */
    public function it_does_not_update_on_local_events_contact_block_updated_event_with_no_display_name()
    {
        factory(Mailing::class, 2)->state('local-events')->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        $event = $this->makeLocalEventsContactBlockUpdated();

        /** @var \Modules\Mailing\Interservice\Listeners\FromNameSubscriber $subscriber */
        $subscriber = app(FromNameSubscriber::class);

        $subscriber->onLocalEventsContactBlockUpdated($event);

        Mailing::all()->each(function (Mailing $mailing) {
            $this->assertEquals('Cosmo Kramer', $mailing->getFromName());
        });
    }

    /** @test */
    public function it_does_not_update_digital_edition_mailings_on_local_events_contact_block_updated_event()
    {
        factory(Mailing::class, 2)->state('digital-edition')->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        factory(Mailing::class, 2)->state('local-events')->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        $event = $this->makeLocalEventsContactBlockUpdated([
            [
                'section'   => 'display_name',
                'item_type' => 'DisplayName',
                'custom'    => 'Newman',
                'item_data' => null,
            ],
        ]);

        /** @var \Modules\Mailing\Interservice\Listeners\FromNameSubscriber $subscriber */
        $subscriber = app(FromNameSubscriber::class);

        $subscriber->onLocalEventsContactBlockUpdated($event);

        Mailing::forDigitalEdition()->get()->each(function (Mailing $mailing) {
            $this->assertEquals('Cosmo Kramer', $mailing->getFromName());
        });

        Mailing::forLocalEvents()->get()->each(function (Mailing $mailing) {
            $this->assertEquals('Newman', $mailing->getFromName());
        });
    }

    /** @test */
    public function it_does_not_update_not_pending_mailings_on_local_events_contact_block_updated_event()
    {
        factory(Mailing::class, 2)->states(['local-events', 'sent'])->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        factory(Mailing::class, 2)->states(['local-events', 'failed'])->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        factory(Mailing::class, 2)->states(['local-events', 'stuck'])->create([
            'account_id' => $this->accountId,
            'from_name'  => 'Cosmo Kramer',
        ]);

        $event = ($this->makeLocalEventsContactBlockUpdated([
            [
                'section'   => 'display_name',
                'item_type' => 'DisplayName',
                'custom'    => 'Newman',
                'item_data' => null,
            ],
        ]));

        /** @var \Modules\Mailing\Interservice\Listeners\FromNameSubscriber $subscriber */
        $subscriber = app(FromNameSubscriber::class);

        $subscriber->onLocalEventsContactBlockUpdated($event);

        Mailing::all()->each(function (Mailing $mailing) {
            $this->assertEquals('Cosmo Kramer', $mailing->getFromName());
        });
    }

    private function makeDisplayNameUpdated(?string $displayName = null) : DisplayNameUpdated
    {
        return DisplayNameUpdated::fromArray([
            'uuid'        => $this->faker->uuid,
            'occurred_at' => now()->format('Y-m-d H:i:s'),
            'global_name' => 'profile.display-name-updated',
            'data'        => ['display_name' => $displayName],
            'metadata'    => [
                'source'  => 'rmc',
                'context' => ['account_id' => $this->accountId],
            ],
        ]);
    }

    private function makeDigitalEditionContactBlockUpdated(array $items = []): Message
    {
        return DigitalEditionContactBlockUpdated::fromArray([
            'uuid'        => $this->faker->uuid,
            'occurred_at' => now()->format('Y-m-d H:i:s'),
            'global_name' => 'profile.digital-edition-contact-block-updated',
            'data'        => ['items' => $items],
            'metadata'    => [
                'source'  => 'rmc',
                'context' => ['account_id' => $this->accountId],
            ],
        ]);
    }

    private function makeLocalEventsContactBlockUpdated(array $items = []): Message
    {
        return LocalContentContactBlockUpdated::fromArray([
            'uuid'        => $this->faker->uuid,
            'occurred_at' => now()->format('Y-m-d H:i:s'),
            'global_name' => 'profile.local-events-contact-block-updated',
            'data'        => ['items' => $items],
            'metadata'    => [
                'source'  => 'rmc',
                'context' => ['account_id' => $this->accountId],
            ],
        ]);
    }
}
