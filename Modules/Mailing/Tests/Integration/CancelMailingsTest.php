<?php

namespace Modules\Mailing\Tests\Integration;

use App\Context\AccountId;
use App\Contracts\AccountRepository;
use App\Events\AccountRemovedFromBrandedPosts;
use App\Events\AccountRemovedFromDigitalEdition;
use App\Events\AccountRemovedFromLocalEvents;
use App\Models\Account;
use App\Models\Plan;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Collection;
use Mockery;
use Modules\Mailing\Interservice\Listeners\CancelMailingsSubscriber;
use Modules\Mailing\Models\Mailing;
use Modules\Mailing\Tests\RefreshMailingDatabase;
use Modules\Mailing\Tests\TestCase;

class CancelMailingsTest extends TestCase
{
    use RefreshMailingDatabase;
    use WithFaker;

    /** @var int */
    private $accountId;

    protected function setUp(): void
    {
        parent::setUp();

        $account = Mockery::mock(Account::class);

        $this->accountId = AccountId::current()->id();

        $this->mock(AccountRepository::class, function ($mock) use ($account) {
            $mock->shouldReceive('find')
                ->with($this->accountId)
                ->andReturn($account);
        });

        $account->shouldReceive('hasPlan')
            ->with(Plan::PLAN_ID_SOCIAL_MEDIA_SHARES)->andReturn(false);


        // remove observers because we dont want to be testing that..
        Mailing::unsetEventDispatcher();
    }

    /** @test */
    public function it_cancels_mailings_on_account_removed_from_digital_edition()
    {
        /** @var \Illuminate\Support\Collection $mailings */
        $mailings = factory(Mailing::class, 2)->state('digital-edition')->create([
            'account_id' => $this->accountId,
        ]);

        event($this->makeAccountRemovedFromDigitalEdition());

        $this->assertEmpty(Mailing::find($mailings->pluck('id')->toArray()));
    }

    /** @test */
    public function it_cancels_mailings_on_account_removed_from_local_events()
    {
        /** @var \Illuminate\Support\Collection $mailings */
        $mailings = factory(Mailing::class, 2)->state('local-events')->create([
            'account_id' => $this->accountId,
        ]);

        event($this->makeAccountRemovedFromLocalEvents());

        $this->assertEmpty(Mailing::find($mailings->pluck('id')->toArray()));
    }

    /** @test */
    public function it_cancels_mailings_on_account_removed_from_branded_posts()
    {
        /** @var \Illuminate\Support\Collection $mailings */
        $mailings = factory(Mailing::class, 2)->state('branded-posts')->create([
            'account_id' => $this->accountId,
        ]);

        $event = $this->makeAccountRemovedFromBrandedPosts();

        $listener = $this->app->make(CancelMailingsSubscriber::class);

        $listener->onAccountRemovedFromBrandedPosts($event);

        $this->assertEmpty(Mailing::find($mailings->pluck('id')->toArray()));
    }

    /** @test */
    public function it_does_not_cancel_other_mailings_on_account_removed_from_digital_edition()
    {
        //get product mailings
        $mailings = $this->createMailingsForAllDigitalProducts();

        //dispatch the event
        event($this->makeAccountRemovedFromDigitalEdition());

        //get the ids for mailings for the removed product, and
        //assert they no longer exist in the table
        $removedIds = $mailings->where('product', Plan::PLAN_ID_DIGITAL_EDITIONS)->pluck('id')->toArray();
        $this->assertEmpty(Mailing::find($removedIds));

        //get the ids for mailings that are not for the removed product and
        //assert mailing still exist for the other products
        $remainingIds = $mailings->where('product', '!=', Plan::PLAN_ID_DIGITAL_EDITIONS)->pluck('id');
        $this->assertEquals($remainingIds->count(), Mailing::whereIn('id', $remainingIds->toArray())->count());
    }

    /** @test */
    public function it_does_not_cancel_other_mailings_on_account_removed_from_local_events()
    {
        //get product mailings
        $mailings = $this->createMailingsForAllDigitalProducts();

        //dispatch the event
        event($this->makeAccountRemovedFromLocalEvents());

        //get the ids for mailings for the removed product, and
        //assert they no longer exist in the table
        $removedIds = $mailings->where('product', Plan::PLAN_ID_LOCAL_EVENT)->pluck('id')->toArray();
        $this->assertEmpty(Mailing::find($removedIds));

        //get the ids for mailings that are not for the removed product and
        //assert mailing still exist for the other products
        $remainingIds = $mailings->where('product', '!=', Plan::PLAN_ID_LOCAL_EVENT)->pluck('id');
        $this->assertEquals($remainingIds->count(), Mailing::whereIn('id', $remainingIds->toArray())->count());
    }

    /** @test */
    public function it_does_not_cancel_other_mailings_on_account_removed_from_branded_posts()
    {
        //get product mailings
        $mailings = $this->createMailingsForAllDigitalProducts();

        //dispatch the event
        $event = $this->makeAccountRemovedFromBrandedPosts();

        $listener = $this->app->make(CancelMailingsSubscriber::class);

        $listener->onAccountRemovedFromBrandedPosts($event);

        //get the ids for mailings for the removed product, and
        //assert they no longer exist in the table
        $removedIds = $mailings->where('product', Plan::PLAN_ID_SOCIAL_MEDIA_SHARES)->pluck('id')->toArray();
        $this->assertEmpty(Mailing::find($removedIds));

        //get the ids for mailings that are not for the removed product and
        //assert mailing still exist for the other products
        $remainingIds = $mailings->where('product', '!=', Plan::PLAN_ID_SOCIAL_MEDIA_SHARES)->pluck('id');
        $this->assertEquals($remainingIds->count(), Mailing::whereIn('id', $remainingIds->toArray())->count());
    }

    private function createMailingsForAllDigitalProducts() : Collection
    {
        //create empty collection
        $mailings = collect();

        //loop on the digital product
        collect(Plan::DIGITAL_PRODUCTS)->each(function ($productId) use (&$mailings) {
            $productMailings = factory(Mailing::class, 2)->create([
                'account_id' => $this->accountId,
                'product'    =>  $productId
            ]);

            //combine with the other mailings
            $mailings = $mailings->merge($productMailings);


        });

        //return the final collection
        return $mailings;
    }

    private function makeAccountRemovedFromDigitalEdition() : AccountRemovedFromDigitalEdition
    {
        return AccountRemovedFromDigitalEdition::fromArray([
            'uuid'        => $this->faker->uuid,
            'occurred_at' => now()->format('Y-m-d H:i:s'),
            'global_name' => 'account.removed-from-digital-edition',
            'data'        => ['plan_id' => Plan::PLAN_ID_DIGITAL_EDITIONS],
            'metadata'    => [
                'source'  => 'crm',
                'context' => ['account_id' => $this->accountId],
            ],
        ]);
    }

    private function makeAccountRemovedFromLocalEvents() : AccountRemovedFromLocalEvents
    {
        return AccountRemovedFromLocalEvents::fromArray([
            'uuid'        => $this->faker->uuid,
            'occurred_at' => now()->format('Y-m-d H:i:s'),
            'global_name' => 'account.removed-from-local-content',
            'data'        => ['plan_id' => Plan::PLAN_ID_LOCAL_EVENT],
            'metadata'    => [
                'source'  => 'crm',
                'context' => ['account_id' => $this->accountId],
            ],
        ]);
    }

    private function makeAccountRemovedFromBrandedPosts() : AccountRemovedFromBrandedPosts
    {
        return AccountRemovedFromBrandedPosts::fromArray([
            'uuid'        => $this->faker->uuid,
            'occurred_at' => now()->format('Y-m-d H:i:s'),
            'global_name' => 'account.removed-from-branded-posts',
            'data'        => ['plan_id' => Plan::PLAN_ID_SOCIAL_MEDIA_SHARES],
            'metadata'    => [
                'source'  => 'crm',
                'context' => ['account_id' => $this->accountId],
            ],
        ]);
    }
}
