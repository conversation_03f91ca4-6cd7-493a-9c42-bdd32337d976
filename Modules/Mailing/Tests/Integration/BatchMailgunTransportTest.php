<?php

namespace Modules\Mailing\Tests\Integration;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Event;
use Mailgun\Api\Message;
use Mailgun\Mailgun;
use Mailgun\Message\BatchMessage;
use Mockery;
use Modules\Mailing\BulkMailing\Events\MessageSent;
use <PERSON><PERSON><PERSON>\Mailing\BulkMailing\Recipient;
use Mo<PERSON>les\Mailing\BulkMailing\Transport\BatchMailgunTransport;
use Modules\Mailing\Mail\BulkMailable;
use Modules\Mailing\Models\AccountMailgunDomain;
use Modules\Mailing\Models\EmailAddress;
use Modules\Mailing\Models\ExternalMessage;
use Modules\Mailing\Models\MailgunDomainRating;
use Mo<PERSON>les\Mailing\Models\Mailing;
use Modules\Mailing\Tests\RefreshMailingDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Modules\Mailing\Tests\TestCase;

class BatchMailgunTransportTest extends TestCase
{
    use RefreshMailingDatabase, WithFaker;

    /** @var \Mockery\Mock|BatchMessage */
    private $batchMessageMock;

    /** @var \Mailgun\Api\Message|\Mockery\LegacyMockInterface|\Mockery\MockInterface */
    private $messageApiMock;

    /** @test */
    public function it_sends_an_email_through_mailgun_domains_relevant_to_recipient_rating_and_only_cc_once()
    {
        /**
         * We want to test the batchmailgun transport class to take a:
         *   1) Mailable
         *   2) Collection of Recipient[s]
         *
         * And ensure that it sends the recipients out the right domain based on their rating by:
         * - mock the mailgun api sdk
         * - assert that addToRecipient is called?
         * - assert that finalize is called?
         * - assert that MessageSent is dispatched
         */

        // Setup Mailgun Mock
        $this->mock(Mailgun::class, function ($mock) {
            return $this->createMailgunMock($mock);
        });

        /** @var MailgunDomainRating[] $domains */
        $domains = [];
        // Setup domain and domain ratings
        foreach (['medium', 'high'] as $state) {
            $domains[ $state ] = factory(MailgunDomainRating::class)->state($state)->create();

            // HEre we're ensuring that, when building a message, we get the right domains built.
            $this->messageApiMock->shouldReceive('getBatchMessage')
                ->withArgs([$domains[ $state ]->getDomain()])
                ->andReturn($this->batchMessageMock);

            $this->batchMessageMock->shouldReceive('finalize');
            $this->batchMessageMock->shouldReceive('getMessageIds')
                ->andReturn(
                    [factory(ExternalMessage::class)->state('mailgun')->make()->id]
                );
        }

        // Setup fake mailable
        $mailable = $this->setupBulkMailable();

        //get the expected cc email address
        $expectedCC = $mailable->getCcRecipientEmail();
        $this->batchMessageMock
            ->shouldReceive('addCcRecipient')
            ->with($expectedCC)
            ->once();

        // setup Recipient Collection
        $recipients = collect();
        foreach ([0, 50, 100] as $rating) {
            // 3 of 0, which won't be sent to,
            // 3 of 50, which will be sent to the 'medium' domain
            // 3 of 100, which will be sent to the 'high' domain
            $recipients = $recipients->merge($this->setupRecipientCollection(1, $rating));
        }

        Event::fake();

        /** @var BatchMailgunTransport $batchTransport */
        $batchTransport = $this->app->make(BatchMailgunTransport::class);

        // Send the email!
        $batchTransport->send($mailable, $recipients);

        // We should have two MEssageSent events, because of the recipients sent to, there are two sets which would go
        // each to a different domain, and one set which will not go at all becuase they have a rating of 0,
        // and we have no domain for a rating of 0
        Event::assertDispatched(MessageSent::class, 2);
    }

    /** @test */
    public function it_sends_an_email_through_one_domain_for_account()
    {
        // Setup Mailgun Mock
        $this->mock(Mailgun::class, function ($mock) {
            return $this->createMailgunMock($mock);
        });

        /** @var AccountMailgunDomain $accountDomain */
        $accountDomain = factory(AccountMailgunDomain::class)->create();

        // Assert we only get one batchMessage from Mailgun's SDK, and are using the one for this account!
        $this->messageApiMock->shouldReceive('getBatchMessage')
            ->withArgs([$accountDomain->getDomain()])
            ->andReturn($this->batchMessageMock);

        // do this only once.
        $this->batchMessageMock->shouldReceive('finalize');
        $this->batchMessageMock->shouldReceive('getMessageIds')
            ->andReturn(
                [factory(ExternalMessage::class)->state('mailgun')->make()->id]
            );

        // Setup fake mailable
        $mailable = $this->setupBulkMailable();

        // setup Recipient Collection
        $recipients = collect();
        foreach ([0, 50, 100] as $rating) {
            // 3 of 0, which won't be sent to,
            // 3 of 50, which will be sent to the 'medium' domain
            // 3 of 100, which will be sent to the 'high' domain
            $recipients = $recipients->merge($this->setupRecipientCollection(1, $rating));
        }

        Event::fake();

        /** @var BatchMailgunTransport $batchTransport */
        $batchTransport = $this->app->make(BatchMailgunTransport::class);

        // Send the email!
        $batchTransport->send($mailable, $recipients);

        // We should have one MessageSent events, because "this account" has a specific domain associated to it.
        // Therefore, all recipients, regardless of rating (aside from 0) should go out one domain.
        // By passing , 1 to the assertDispatched, it will ensure only one was dispatched!
        Event::assertDispatched(MessageSent::class, 1);
    }

    /** @test */
    public function it_trims_email_addresses_before_adding_the_recipient_to_the_batch()
    {
        // Setup Mailgun Mock
        $this->mock(Mailgun::class, function ($mock) {
            return $this->createMailgunMock($mock);
        });

        /** @var AccountMailgunDomain $accountDomain */
        $accountDomain = factory(AccountMailgunDomain::class)->create();

        // Assert we only get one batchMessage from Mailgun's SDK, and are using the one for this account!
        $this->messageApiMock->shouldReceive('getBatchMessage')
            ->withArgs([$accountDomain->getDomain()])
            ->andReturn($this->batchMessageMock);

        // do this only once.
        $this->batchMessageMock->shouldReceive('finalize');
        $this->batchMessageMock->shouldReceive('getMessageIds')
            ->andReturn(
                [factory(ExternalMessage::class)->state('mailgun')->make()->id]
            );

        // Setup fake mailable
        $mailable = $this->setupBulkMailable();

        // setup Recipient Collection with 1 recipient
        $recipients = $this->setupRecipientCollection(1, 100);

        //pull the data we need for testing
        $recipient = $recipients->first();
        $expectedEmail = $recipients->first()->email->address;

        //update the email address for that recipient to have a trailing " "
        $recipient->email->update(['address' => $expectedEmail . " "]);

        //Test for the addToRecipient being called with the correct parameters
        $this->batchMessageMock->expects('addToRecipient')->with(
            $expectedEmail,
            [
                'first' => $recipient->firstName,
                'last' => $recipient->lastName,
                'id' => base64_encode($recipient->contactUuid),
            ]
        )->andReturnSelf();

        Event::fake();

        /** @var BatchMailgunTransport $batchTransport */
        $batchTransport = $this->app->make(BatchMailgunTransport::class);

        // Send the email!
        $batchTransport->send($mailable, $recipients);

        // We should have one MessageSent events, because "this account" has a specific domain associated to it.
        // Therefore, all recipients, regardless of rating (aside from 0) should go out one domain.
        // By passing , 1 to the assertDispatched, it will ensure only one was dispatched!
        Event::assertDispatched(MessageSent::class, 1);
    }

    /** @test */
    public function it_tests_message_sent_has_cc_with_correct_data()
    {
        // Setup Mailgun Mock
        $this->mock(Mailgun::class, function ($mock) {
            return $this->createMailgunMock($mock);
        });

        /** @var AccountMailgunDomain $accountDomain */
        $accountDomain = factory(AccountMailgunDomain::class)->create();

        // Assert we only get one batchMessage from Mailgun's SDK, and are using the one for this account!
        $this->messageApiMock->shouldReceive('getBatchMessage')
            ->withArgs([$accountDomain->getDomain()])
            ->andReturn($this->batchMessageMock);

        // do this only once.
        $this->batchMessageMock->shouldReceive('finalize');
        $this->batchMessageMock->shouldReceive('getMessageIds')
            ->andReturn(
                [factory(ExternalMessage::class)->state('mailgun')->make()->id]
            );

        $ccData = [
            'name' => 'Test CC',
            'address' => '<EMAIL>'
        ];

        // Setup fake mailable
        $mailing = factory(Mailing::class)->create([
            'from_name' => $ccData['name'],
            'reply_email' => $ccData['address']
        ]);
        $mailable = new BulkMailable($mailing);

        // setup Recipient Collection
        $recipients = $this->setupRecipientCollection(1);

        Event::fake();

        $this->batchMessageMock
            ->shouldReceive('addCcRecipient')
            ->with($ccData['address'])
            ->once();

        /** @var BatchMailgunTransport $batchTransport */
        $batchTransport = $this->app->make(BatchMailgunTransport::class);

        // Send the email!
        $batchTransport->send($mailable, $recipients);

        Event::assertDispatched(MessageSent::class);
    }

    /** @test */
    public function it_does_not_add_a_cc_recipient_if_there_is_a_mailing_recipients_with_the_same_email()
    {
        // Setup Mailgun Mock
        $this->mock(Mailgun::class, function ($mock) {
            return $this->createMailgunMock($mock);
        });

        /** @var AccountMailgunDomain $accountDomain */
        $accountDomain = factory(AccountMailgunDomain::class)->create();

        // Assert we only get one batchMessage from Mailgun's SDK, and are using the one for this account!
        $this->messageApiMock->shouldReceive('getBatchMessage')
            ->withArgs([$accountDomain->getDomain()])
            ->andReturn($this->batchMessageMock);

        // do this only once.
        $this->batchMessageMock->shouldReceive('finalize');
        $this->batchMessageMock->shouldReceive('getMessageIds')
            ->andReturn(
                [factory(ExternalMessage::class)->state('mailgun')->make()->id]
            );

        // setup Recipient Collection
        $recipients = $this->setupRecipientCollection(1);

        // Setup fake mailable
        $mailing = factory(Mailing::class)->create([
            'from_name' => 'Test CC',
            'reply_email' => $recipients->first()->email->address
        ]);
        $mailable = new BulkMailable($mailing);

        Event::fake();

        $this->batchMessageMock ->shouldNotReceive('addCcRecipient');

        /** @var BatchMailgunTransport $batchTransport */
        $batchTransport = $this->app->make(BatchMailgunTransport::class);

        // Send the email!
        $batchTransport->send($mailable, $recipients);

        Event::assertDispatched(MessageSent::class);
    }

    /** @test */
    public function it_does_not_add_a_cc_recipient_if_there_is_a_mailing_recipients_with_the_same_email_regardless_of_letter_case()
    {
        // Setup Mailgun Mock
        $this->mock(Mailgun::class, function ($mock) {
            return $this->createMailgunMock($mock);
        });

        /** @var AccountMailgunDomain $accountDomain */
        $accountDomain = factory(AccountMailgunDomain::class)->create();

        // Assert we only get one batchMessage from Mailgun's SDK, and are using the one for this account!
        $this->messageApiMock->shouldReceive('getBatchMessage')
            ->withArgs([$accountDomain->getDomain()])
            ->andReturn($this->batchMessageMock);

        // do this only once.
        $this->batchMessageMock->shouldReceive('finalize');
        $this->batchMessageMock->shouldReceive('getMessageIds')
            ->andReturn(
                [factory(ExternalMessage::class)->state('mailgun')->make()->id]
            );

        // setup Recipient Collection
        $recipients = $this->setupRecipientCollection(1);

        // Setup fake mailable
        $mailing = factory(Mailing::class)->create([
            'from_name' => 'Test CC',
            'reply_email' => strtoupper($recipients->first()->email->address)
        ]);
        $mailable = new BulkMailable($mailing);

        Event::fake();

        $this->batchMessageMock ->shouldNotReceive('addCcRecipient');

        /** @var BatchMailgunTransport $batchTransport */
        $batchTransport = $this->app->make(BatchMailgunTransport::class);

        // Send the email!
        $batchTransport->send($mailable, $recipients);

        Event::assertDispatched(MessageSent::class);
    }

    /** @test */
    public function it_tests_manual_recipient_chunking()
    {
        // Setup Mailgun Mock
        $this->mock(Mailgun::class, function ($mock) {
            return $this->createMailgunMock($mock);
        });

        /** @var AccountMailgunDomain $accountDomain */
        $accountDomain = factory(AccountMailgunDomain::class)->create();

        // Assert we only get one batchMessage from Mailgun's SDK, and are using the one for this account!
        $this->messageApiMock->shouldReceive('getBatchMessage')
            ->withArgs([$accountDomain->getDomain()])
            ->andReturn($this->batchMessageMock);

        // do this only once.
        $this->batchMessageMock->shouldReceive('getMessageIds')
            ->andReturn(
                [factory(ExternalMessage::class)->state('mailgun')->make()->id]
            );

        $ccData = [
            'name' => 'Test CC',
            'address' => '<EMAIL>'
        ];

        // Setup fake mailable
        $mailing = factory(Mailing::class)->create([
            'from_name' => $ccData['name'],
            'reply_email' => $ccData['address']
        ]);
        $mailable = new BulkMailable($mailing);

        // setup Recipient Collection
        $numRecipients = 1005; //recipient count greater than the limit
        $chunkSize = BatchMessage::RECIPIENT_COUNT_LIMIT - 1; //chunk size is limit - 1 for the cc
        $recipients = $this->setupRecipientCollection($numRecipients);

        //add the expectations
        $this->batchMessageMock->shouldReceive('addToRecipient')->times($chunkSize);
        $this->batchMessageMock->shouldReceive('finalize')->once();
        $this->batchMessageMock->shouldReceive('addToRecipient')->times($numRecipients - $chunkSize);
        $this->batchMessageMock->shouldReceive('finalize')->once();

        Event::fake();

        $this->batchMessageMock
            ->shouldReceive('addCcRecipient')
            ->once();

        /** @var BatchMailgunTransport $batchTransport */
        $batchTransport = $this->app->make(BatchMailgunTransport::class);

        // Send the email!
        $batchTransport->send($mailable, $recipients);

        Event::assertDispatched(MessageSent::class, 1);
    }

    /**
     * @param \Mailgun\Mailgun|\Mockery\Mock $mock
     */
    protected function createMailgunMock($mock)
    {
        $batchMessageMock = Mockery::mock(BatchMessage::class)->makePartial();

        $this->batchMessageMock = $batchMessageMock;

        $this->messageApiMock = Mockery::mock(Message::class);

        return $mock->shouldReceive('messages')
            ->andReturn($this->messageApiMock);
    }

    private function setupRecipientCollection(int $qty = 2, int $rating = 50) : Collection
    {
        return Collection::times($qty, function () use ($rating) {
            $recipient = new Recipient;
            $recipient->contactUuid = $this->faker->uuid;
            $recipient->email = factory(EmailAddress::class)->create();
            $recipient->firstName = $this->faker->firstName;
            $recipient->lastName = $this->faker->lastName;
            $recipient->rating = $rating;

            return $recipient;
        });
    }

    private function setupBulkMailable() : BulkMailable
    {
        $mailing = factory(Mailing::class)->create();

        return new BulkMailable($mailing);
    }
}
