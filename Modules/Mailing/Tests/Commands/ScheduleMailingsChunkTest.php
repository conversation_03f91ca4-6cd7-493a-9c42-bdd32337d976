<?php

namespace Commands;

use App\Context\AccountId;
use App\Models\Account;
use Carbon\Carbon;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Config;
use Modules\Mailing\Console\ScheduleMailings;
use Mo<PERSON>les\Mailing\Jobs\ScheduleMailingJob;
use Modules\Mailing\Models\Mailing;
use Modules\Mailing\Tests\RefreshMailingDatabase;
use Modules\Mailing\Tests\TestCase;
use Illuminate\Support\Facades\Bus;
use Symfony\Component\Console\Output\NullOutput;
use Tests\DoesNotCreateAccount;

class ScheduleMailingsChunkTest extends TestCase implements DoesNotCreateAccount
{
    use RefreshMailingDatabase;

    /** @test */
    public function it_dispatches_mailing_jobs_for_all_mailings()
    {
        //enable bulk mailing
        Config::set('bulk-mailing.enabled', true);

        //set the number of mailings to 1 higher than the chunk sized used by the ScheduleMailings job
        $numMailings = ScheduleMailings::CHUNK_SIZE + 1;

        //set up a number of accounts and mailings
        for ($i = 0; $i < $numMailings; $i++) {
            //create an account
            $account = factory(Account::class)->make();
            $this->setupAccountRepositoryMockForAccount($account);

            //create an LE mailing for that account
            factory(Mailing::class)->state('local-events')->create([
                'account_id' => $account->id,
                'send_at' => Carbon::now(),
            ]);
        }

        //fake the bus, we only want to verify mailing transitions to queued, and dispatch of the job
        Bus::fake();

        // Forget current account
        AccountId::forgetCurrent();

        //Call the command to send the mailings
        Artisan::call('mailings:send', [], new NullOutput());

        //Assert the job has been dispatched
        Bus::assertDispatched(ScheduleMailingJob::class, $numMailings);

        //Assert the number of mailing set to queue
        $numQueued = Mailing::where('state', Mailing::STATE_QUEUED)->count();
        $this->assertEquals($numQueued, $numMailings, 'Number of enqueue mailings does not match expected result');
    }
}
