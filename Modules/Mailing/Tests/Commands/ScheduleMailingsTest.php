<?php

namespace Modules\Mailing\Tests\Commands;

use App\Context\AccountId;
use App\Models\Account;
use Carbon\Carbon;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Config;
use Modules\Mailing\Models\MailgunDomainRating;
use Modules\Mailing\Models\Mailing;
use Modules\Mailing\Models\MailingRecipient;
use Modules\Mailing\Tests\RefreshMailingDatabase;
use Modules\Mailing\Tests\TestCase;
use Modules\Mailing\Tests\TestsContactsApi;

class ScheduleMailingsTest extends TestCase
{
    use TestsContactsApi; // todo remove or rename
    use RefreshMailingDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Fake account for job serializing
        $account = factory(Account::class)->make(['id' => AccountId::current()->id()]);
        $this->setupAccountRepositoryMockForAccount($account);

        Config::set('bulk-mailing.enabled', true);
    }

    /** @test */
    public function it_can_send_mailings_for_digital_edition()
    {
        factory(MailgunDomainRating::class)->states(['default', 'digital-edition'])->create();

        // Given a mailing
        /** @var \Modules\Mailing\Models\Mailing $mailing */
        $mailing = factory(Mailing::class)->state('digital-edition')->create([
            'send_at'    => Carbon::now(),
            'from_name'  => 'Mr Sender',
        ]);

        // Setup Contacts
        $this->setupMockContactsCollection();

        // Forget current account
        AccountId::forgetCurrent();

        Artisan::call('mailings:send');

        $mailing->refresh();

        $this->assertEquals('sent', $mailing->stateIs());
        $this->assertEquals(10, MailingRecipient::count());
        $this->assertEquals('Mr Sender', $mailing->getFromName());
    }

    /** @test */
    public function it_can_send_mailings_for_local_events()
    {
        factory(MailgunDomainRating::class)->states(['default', 'local-events'])->create();

        /** @var \Modules\Mailing\Models\Mailing $mailing */
        $mailing = factory(Mailing::class)->state('local-events')->create([
            'send_at'    => Carbon::now(),
        ]);

        // Setup Contacts
        $this->setupMockContactsCollection();

        // Forget current account
        AccountId::forgetCurrent();

        Artisan::call('mailings:send');

        $mailing = $mailing->fresh();

        $this->assertEquals('sent', $mailing->stateIs());
        $this->assertEquals(10, MailingRecipient::count());
    }

    /** @test */
    public function it_does_not_dispatch_jobs_when_no_mailings_are_found()
    {
        // Given a mailing
        /** @var \Modules\Mailing\Models\Mailing $mailing */
        $mailing = factory(Mailing::class)->create([
            'send_at' => Carbon::now()->addDays(2),
        ]);

        Artisan::call('mailings:send');

        $mailing = $mailing->fresh();

        $this->assertEquals('new', $mailing->stateIs());
    }
}
