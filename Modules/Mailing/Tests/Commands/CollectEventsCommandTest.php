<?php

namespace Modules\Mailing\Tests\Commands;

use Modules\Mailing\Jobs\FetchEmailEventsForDomain;
use Modules\Mailing\Jobs\FetchEventsForMailing;
use Modules\Mailing\Models\MailgunDomainRating;
use Modules\Mailing\Models\Mailing;
use Carbon\Carbon;
use Illuminate\Support\Facades\Artisan;
use Modules\Mailing\Tests\RefreshMailingDatabase;
use Modules\Mailing\Tests\TestCase;

class CollectEventsCommandTest extends TestCase
{
    use RefreshMailingDatabase;

    /** @test */
    public function it_dispatches_jobs_for_domains()
    {
        factory(MailgunDomainRating::class)->state('digital-edition')->create();

        $this->expectsJobs(FetchEmailEventsForDomain::class);

        Artisan::call('mailings:collect-events');
    }

    /** @test */
    public function it_dispatches_jobs_for_specific_mailing()
    {
        factory(Mailing::class)->create();
        $mailing = factory(Mailing::class)->create();

        $this->expectsJobs(FetchEventsForMailing::class);

        Artisan::call('mailings:collect-events', ['--mailing_id' => $mailing->id]);
    }

    /** @test */
    public function it_dispatches_jobs_for_mailings_sent_within_timeframe()
    {
        $now = Carbon::now();
        factory(Mailing::class)->states(['sent'])->create(['send_at' => $now->copy()->subHours(4)]);
        $firstMailing = factory(Mailing::class)->states(['sent'])->create(['send_at' => $now->copy()->subHours(2)]);
        $secondMailing =
            factory(Mailing::class)->states(['sent'])->create(['send_at' => $now->copy()->subHours(2)->subMinutes(30)]);

        $this->withoutJobs();

        Artisan::call('mailings:collect-events', ['--sent_within' => 3]);

        $this->beforeApplicationDestroyed(function () use ($firstMailing, $secondMailing) {
            $job = array_shift($this->dispatchedJobs);

            $this->assertEquals($firstMailing->id, $job->getMailing()->getId());

            $job = array_shift($this->dispatchedJobs);

            $this->assertEquals($secondMailing->id, $job->getMailing()->getId());
        });
    }
}
