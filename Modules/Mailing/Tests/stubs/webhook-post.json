{"signature": {"timestamp": "1608660520", "token": "9029572955cbf0a30b074be8572b5352af82cdd6cb40197cd4", "signature": "ec689ba912aba8063776d32701c8690df20eb72fcaf0caa19a631ac57583dd53"}, "event-data": {"tags": ["my_tag_1", "my_tag_2"], "timestamp": 1521472262.908181, "storage": {"url": "https://se.api.mailgun.net/v3/domains/testing-int.myamericanlifestyle.com/messages/message_key", "key": "message_key"}, "envelope": {"transport": "smtp", "sender": "<EMAIL>", "sending-ip": "**************", "targets": "<EMAIL>"}, "recipient-domain": "example.com", "event": "delivered", "campaigns": [], "user-variables": {"my_var_1": "Mailgun Variable #1", "my-var-2": "awesome"}, "flags": {"is-routed": false, "is-authenticated": true, "is-system-test": false, "is-test-mode": false}, "log-level": "info", "message": {"headers": {"to": "<PERSON> ", "message-id": "<EMAIL>", "from": "<PERSON> ", "subject": "Test delivered webhook"}, "attachments": [], "size": 111}, "recipient": "<EMAIL>", "id": "CPgfbmQMTCKtHW6uIWtuVe", "delivery-status": {"tls": true, "mx-host": "smtp-in.example.com", "attempt-no": 1, "description": "", "session-seconds": 0.4331989288330078, "utf8": true, "code": 250, "message": "OK", "certificate-verified": true}}}