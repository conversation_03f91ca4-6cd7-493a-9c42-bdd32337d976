{"items": [{"geolocation": {"country": "US", "region": "Unknown", "city": "Unknown"}, "tags": [], "ip": "*************", "log-level": "info", "id": "jVxx2hzPSTeU9b-kBMe7nQ", "campaigns": [], "user-variables": {}, "recipient-domain": "comcast.net", "timestamp": 1531233763.911973, "client-info": {"client-type": "browser", "device-type": "desktop", "client-name": "Chrome", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.99 Safari/537.36", "client-os": "OS X"}, "message": {"headers": {"message-id": "<EMAIL>"}}, "recipient": "<EMAIL>", "event": "opened"}, {"geolocation": {"country": "US", "region": "Unknown", "city": "Unknown"}, "tags": [], "ip": "*************", "log-level": "info", "id": "Bf99edeeR8udc9FiJZnc_w", "campaigns": [], "user-variables": {}, "recipient-domain": "comcast.net", "timestamp": 1531169855.947289, "client-info": {"client-type": "mobile browser", "device-type": "mobile", "client-name": "Android Webkit", "user-agent": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel 2 XL Build/OPM4.171019.021.E1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "client-os": "Android"}, "message": {"headers": {"message-id": "<EMAIL>"}}, "recipient": "<EMAIL>", "event": "opened"}, {"tags": [], "delivery-status": {"tls": true, "mx-host": "mx2.comcast.net", "attempt-no": 1, "description": "", "session-seconds": 1.0385410785675049, "code": 250, "message": "OK", "certificate-verified": true}, "storage": {"url": "https://se.api.mailgun.net/v3/domains/discover.myamericanlifestyle.com/messages/eyJwIjpmYWxzZSwiayI6IjU5NzI2MWQ3LTk2NmYtNGE0OS1iMDQ0LWE3MzVhMGZmYWMxMSIsInMiOiI0ZDkzN2ZmZTNiIiwiYyI6InRhbmtiIn0=", "key": "eyJwIjpmYWxzZSwiayI6IjU5NzI2MWQ3LTk2NmYtNGE0OS1iMDQ0LWE3MzVhMGZmYWMxMSIsInMiOiI0ZDkzN2ZmZTNiIiwiYyI6InRhbmtiIn0="}, "log-level": "info", "id": "BD4u2diCSmuLe-6B2T66AQ", "campaigns": [], "user-variables": {}, "flags": {"is-routed": false, "is-authenticated": true, "is-system-test": false, "is-test-mode": false}, "recipient-domain": "comcast.net", "timestamp": 1531169216.855444, "envelope": {"transport": "smtp", "sender": "<EMAIL>", "sending-ip": "***************", "targets": "<EMAIL>"}, "message": {"headers": {"to": "<PERSON> <<EMAIL>>", "message-id": "<EMAIL>", "from": "Support Team <<EMAIL>>", "subject": "Welcome to American Lifestyle!"}, "attachments": [], "size": 615}, "recipient": "<EMAIL>", "event": "delivered"}, {"tags": [], "envelope": {"sender": "<EMAIL>", "transport": "smtp", "targets": "<EMAIL>"}, "storage": {"url": "https://se.api.mailgun.net/v3/domains/discover.myamericanlifestyle.com/messages/eyJwIjpmYWxzZSwiayI6IjU5NzI2MWQ3LTk2NmYtNGE0OS1iMDQ0LWE3MzVhMGZmYWMxMSIsInMiOiI0ZDkzN2ZmZTNiIiwiYyI6InRhbmtiIn0=", "key": "eyJwIjpmYWxzZSwiayI6IjU5NzI2MWQ3LTk2NmYtNGE0OS1iMDQ0LWE3MzVhMGZmYWMxMSIsInMiOiI0ZDkzN2ZmZTNiIiwiYyI6InRhbmtiIn0="}, "log-level": "info", "id": "w3-uUM9dQkGiZ9STi_Amtw", "campaigns": [], "method": "http", "user-variables": {}, "flags": {"is-routed": false, "is-authenticated": true, "is-system-test": false, "is-test-mode": false}, "recipient-domain": "comcast.net", "timestamp": 1531169215.081476, "message": {"headers": {"to": "%recipient%", "message-id": "<EMAIL>", "from": "Support Team <<EMAIL>>", "subject": "Welcome to American Lifestyle!"}, "attachments": [], "size": 615}, "recipient": "<EMAIL>", "event": "accepted"}], "paging": {"previous": "https://api.mailgun.net/v3/discover.myamericanlifestyle.com/events/WzIseyJhIjpmYWxzZSwiYiI6IjIwMTgtMDctMTBUMTY6MjQ6MTMuMjA1KzAwOjAwIn0sW1siZGVmYXVsdCIseyJiIjoiMjAxOC0wNy0xMFQxNDo0Mjo0My45MTErMDA6MDAiLCJlIjoiMjAxOC0wNy0xMFQxNjoyNDoxMy4yMDYrMDA6MDAifSxbInAiXSwibWVzc2FnZSNqVnh4Mmh6UFNUZVU5Yi1rQk1lN25RIl1dLFsiZiJdLG51bGwsW1siYWNjb3VudC5pZCIsIjViNDM5MDgxY2QxNjgyMDAwMTM4YjJjMiJdLFsiZG9tYWluLm5hbWUiLCJtZy5ub2tpb3NkZXNpZ25zLmNvbSJdLFsibWVzc2FnZS5oZWFkZXJzLm1lc3NhZ2UtaWQiLCIyMDE4MDcwOTIwNDY1NS4xLjI1QTNDOTdCOEM2NDhBRUFAbWcubm9raW9zZGVzaWducy5jb20iXV0sMTAwXQ==", "first": "https://api.mailgun.net/v3/discover.myamericanlifestyle.com/events/WzIseyJhIjpmYWxzZSwiYiI6IjIwMTgtMDctMTBUMTY6MjQ6MTMuMjA1KzAwOjAwIn0sW1siZGVmYXVsdCIseyJhIjpmYWxzZSwiYiI6IjIwMTgtMDctMTBUMTY6MjQ6MTMuMjA1KzAwOjAwIn0sW10sbnVsbF1dLFsiZiJdLG51bGwsW1siYWNjb3VudC5pZCIsIjViNDM5MDgxY2QxNjgyMDAwMTM4YjJjMiJdLFsiZG9tYWluLm5hbWUiLCJtZy5ub2tpb3NkZXNpZ25zLmNvbSJdLFsibWVzc2FnZS5oZWFkZXJzLm1lc3NhZ2UtaWQiLCIyMDE4MDcwOTIwNDY1NS4xLjI1QTNDOTdCOEM2NDhBRUFAbWcubm9raW9zZGVzaWducy5jb20iXV0sMTAwXQ==", "last": "https://api.mailgun.net/v3/discover.myamericanlifestyle.com/events/WzIseyJhIjpmYWxzZSwiYiI6IjIwMTgtMDctMTBUMTY6MjQ6MTMuMjA1KzAwOjAwIn0sW1siZGVmYXVsdCIseyJiIjoiMTk3MC0wMS0wMVQwMDowMDowMC4wMDArMDA6MDAiLCJlIjoiMjAxOC0wNy0xMFQxNjoyNDoxMy4yMDUrMDA6MDAifSxbInAiXSxudWxsXV0sWyJmIl0sbnVsbCxbWyJhY2NvdW50LmlkIiwiNWI0MzkwODFjZDE2ODIwMDAxMzhiMmMyIl0sWyJkb21haW4ubmFtZSIsIm1nLm5va2lvc2Rlc2lnbnMuY29tIl0sWyJtZXNzYWdlLmhlYWRlcnMubWVzc2FnZS1pZCIsIjIwMTgwNzA5MjA0NjU1LjEuMjVBM0M5N0I4QzY0OEFFQUBtZy5ub2tpb3NkZXNpZ25zLmNvbSJdXSwxMDBd", "next": "https://api.mailgun.net/v3/discover.myamericanlifestyle.com/events/WzIseyJhIjpmYWxzZSwiYiI6IjIwMTgtMDctMTBUMTY6MjQ6MTMuMjA1KzAwOjAwIn0sW1siZGVmYXVsdCIseyJhIjpmYWxzZSwiYiI6IjIwMTgtMDctMDlUMjA6NDY6NTUuMDgxKzAwOjAwIn0sW10sIm1lc3NhZ2UjdzMtdVVNOWRRa0dpWjlTVGlfQW10dyJdXSxbImYiXSxudWxsLFtbImFjY291bnQuaWQiLCI1YjQzOTA4MWNkMTY4MjAwMDEzOGIyYzIiXSxbImRvbWFpbi5uYW1lIiwibWcubm9raW9zZGVzaWducy5jb20iXSxbIm1lc3NhZ2UuaGVhZGVycy5tZXNzYWdlLWlkIiwiMjAxODA3MDkyMDQ2NTUuMS4yNUEzQzk3QjhDNjQ4QUVBQG1nLm5va2lvc2Rlc2lnbnMuY29tIl1dLDEwMF0="}}