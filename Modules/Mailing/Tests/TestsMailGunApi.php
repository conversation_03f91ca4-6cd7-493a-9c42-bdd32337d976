<?php

namespace Modules\Mailing\Tests;

use Guzzle<PERSON>ttp\Client;
use Guzzle<PERSON>ttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use Http\Adapter\Guzzle6\Client as GuzzleAdapter;

trait TestsMailGunApi
{
    /** @var \GuzzleHttp\Handler\MockHandler */
    protected $mockMailGunHandler;

    public function bootTestsMailGunApi()
    {
        $this->mockMailGunHandler = new MockHandler();

        $this->app->bind('mailgun.client', function () {
            return new GuzzleAdapter(
                new Client(['handler' => HandlerStack::create($this->mockMailGunHandler)])
            );
        });
    }

    public function addMailGunResponse(int $statusCode, ?string $body = null, string $fileName = null) : void
    {
        $this->mockMailGunHandler->append(
            new Response($statusCode, ['Content-Type' => 'application/json'], $body ?? file_get_contents($fileName))
        );
    }

    public function addMailGunResponseFromFile(int $statusCode, string $fileName)
    {
        $this->addMailGunResponse($statusCode, null, $fileName);
    }
}
