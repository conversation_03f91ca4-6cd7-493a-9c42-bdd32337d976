<?php

namespace Modules\Mailing\Tests\Unit;

use Modules\Mailing\Models\Mailing;
use Modules\Mailing\Models\MailingState;
use Carbon\Carbon;
use Modules\Mailing\Tests\RefreshMailingDatabase;
use Modules\Mailing\Tests\TestCase;

class MailingModelTest extends TestCase
{
    use RefreshMailingDatabase;

    /** @test */
    public function cannot_resend_when_send_at_is_before_cutoff()
    {
        /** @var Mailing $mailing */
        $mailing =
            factory(Mailing::class)->create(['send_at' => Carbon::parse(config('bulk-mailing.resend_cutoff'))->subDay()]);

        $this->assertFalse($mailing->canBeResent());
    }

    /** @test */
    public function it_can_transition_to_sending()
    {
        /** @var Mailing $mailing */
        $mailing = factory(Mailing::class)->create();

        $mailing->transition('prepare');

        $this->assertEquals('preparing', $mailing->stateIs());

        $this->assertEquals(1, MailingState::count());
    }

    /** @test */
    public function it_can_transition_to_sent()
    {
        $mailing = factory(Mailing::class)->create(['state' => 'sending']);

        $this->assertTrue($mailing->transitionAllowed('sent'));
    }
}
