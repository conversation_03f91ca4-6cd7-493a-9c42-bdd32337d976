<?php

namespace Modules\Mailing\Tests\Unit;

use Illuminate\Foundation\Testing\WithFaker;
use Modules\Mailing\Models\MailgunEventDetail;
use Modules\Mailing\Tests\TestCase;
use Tests\DoesNotCreateAccount;

class MailgunEventDetailTest extends TestCase implements DoesNotCreateAccount
{
    use WithFaker;

    /** @test */
    public function it_returns_a_valid_recipient_email_address()
    {
        $validEmail = $this->faker->email;

        $result = MailgunEventDetail::getEmailFromEventRecipient($validEmail);

        $this->assertEquals(
            $validEmail,
            $result,
            'Emails do not match'
        );
    }

    /** @test */
    public function it_returns_a_parsed_recipient_email_address()
    {
        //get a valid email
        $validEmail = $this->faker->email;

        //Build the test string
        $testStr = $this->faker->name . " <" . $validEmail . ">";

        $result = MailgunEventDetail::getEmailFromEventRecipient($testStr);

        $this->assertEquals(
            $validEmail,
            $result,
            'Emails do not match'
        );
    }

    /** @test */
    public function it_returns_the_passed_recipient_email_address()
    {
        $invalidString = $this->faker->name;

        $result = MailgunEventDetail::getEmailFromEventRecipient($invalidString);

        $this->assertEquals(
            $invalidString,
            $result,
            'Returned string does not match'
        );
    }
}
