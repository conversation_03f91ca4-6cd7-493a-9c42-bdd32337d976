<?php

namespace Modules\Mailing\Tests\Feature;

use Illuminate\Support\Facades\Bus;
use Modules\Mailing\Jobs\UploadSuppression;
use Modules\Mailing\Models\EventType;
use Modules\Mailing\Models\MailgunDomain;
use Modules\Mailing\Models\MailgunEventDetail;
use Modules\Mailing\Tests\RefreshMailingDatabase;
use Modules\Mailing\Tests\TestCase;

class UploadSuppressionTest extends TestCase
{
    use RefreshMailingDatabase;

    /** @test */
    public function if_fires_upload_suppression_job_after_over_quota_event()
    {
        Bus::fake();
        // Given an event created for an over-quota bounce
        $event = factory(MailgunEventDetail::class)->state('over-quota')->create();
        // Since the above has an observer that is watching for its creation to trigger the below,
        // we don't need to actually mock a mailgun api response for retrieving events.
        // We also should not need to verify api response for uploading, that is a separate test.
        // All we want to do here is ensure the creation of an event that represents an over-quota bounce
        // actually results in an upload of a suppression to MG.
        Bus::assertDispatched(UploadSuppression::class, function (UploadSuppression $job) use ($event) {
            return $job->event->getEmailAddress() == $event->getRecipientEmailAddress()->address;
        });
    }

    /** @test */
    public function if_fires_upload_suppression_job_after_mx_record_error()
    {
        Bus::fake();
        // Given an event created for a mx-error bounce
        $event = factory(MailgunEventDetail::class)->state('mx-error')->create();

        // Ensure UploadSuppression fires for an event with a mx-error bounce
        Bus::assertDispatched(UploadSuppression::class, function (UploadSuppression $job) use ($event) {
            return $job->event->getEmailAddress() == $event->getRecipientEmailAddress()->address;
        });
    }

    /** @test */
    public function a_suppression_event_can_access_all_domains()
    {
        Bus::fake();

        factory(MailgunDomain::class)->create(['domain' => 'first.myamericanlifestyle.com']);
        factory(MailgunDomain::class)->create(['domain' => 'second.myamericanlifestyle.com']);

        // Given an event created for an over-quota bounce
        $event = factory(MailgunEventDetail::class)->state('over-quota')->create();

        // Since the above has an observer that is watching for its creation to trigger the below,
        // we don't need to actually mock a mailgun api response for retrieving events.
        // We also should not need to verify api response for uploading, that is a separate test.
        // All we want to do here is ensure the creation of an event that represents an over-quota bounce
        // actually results in an upload of a suppression to MG.
        Bus::assertDispatched(UploadSuppression::class, function (UploadSuppression $job) use ($event) {
            return in_array('first.myamericanlifestyle.com', $job->getDomains()->toArray())
                   && in_array('second.myamericanlifestyle.com', $job->getDomains()->toArray())
                   && $job->getSuppression()->getEmail() === $event->getRecipientEmailAddress()->address;
        });
    }

    /** @test */
    public function it_does_not_upload_suppression_when_failed_for_some_reason_other_than_over_quota()
    {
        Bus::fake();

        // Given an event created for a soft bounce that.
        $event = factory(MailgunEventDetail::class)->state('softbounce')->create();

        // Verify it does not dispatch an upload job.
        Bus::assertNotDispatched(UploadSuppression::class);

        // But that it did create a localized/normalized event
        $this->assertDatabaseHas(
            'mailing_recipient_events',
            [
                'mailing_recipient_id' => $event->mailing_recipient_id,
                'event_type_id'        => EventType::SOFT_BOUNCE,
            ],
            'mailing'
        );
    }
}
