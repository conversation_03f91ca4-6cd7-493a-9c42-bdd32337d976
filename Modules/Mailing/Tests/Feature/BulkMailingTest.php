<?php

namespace Modules\Mailing\Tests\Feature;

use App\Context\AccountId;
use App\Models\Account;
use App\Models\Plan;
use App\TeamsNotification\TeamsNotifiable;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Modules\Mailing\BulkMailing\BulkMailer;
use Modules\Mailing\BulkMailing\Exceptions\UnableToSendMailing;
use Modules\Mailing\BulkMailing\Recipient;
use Modules\Mailing\BulkMailing\Transport\Transport;
use Modules\Mailing\Entities\Contact;
use Modules\Mailing\Mail\BulkMailable;
use Modules\Mailing\Models\EmailAddress;
use Modules\Mailing\Models\EmailAddressRating;
use Modules\Mailing\Models\ExternalMessage;
use Modules\Mailing\Models\MailgunDomainRating;
use Modules\Mailing\Models\Mailing;
use Modules\Mailing\Models\MailingRecipient;
use Modules\Mailing\Notifications\MailingSendFailed;
use Modules\Mailing\Tests\RefreshMailingDatabase;
use Modules\Mailing\Tests\TestCase;
use Modules\Mailing\Tests\TestsContactsApi;
use Modules\Mailing\Tests\TestsMailGunApi;

class BulkMailingTest extends TestCase
{
    use TestsContactsApi;
    use RefreshMailingDatabase;
    use TestsMailGunApi;

    /** @var \Illuminate\Support\Collection */
    private $contacts;

    protected function setUp(): void
    {
        parent::setUp();

        $this->getContactsRepositoryMock();
    }

    /** @test */
    public function it_can_send_a_mailing()
    {
        // Given a Mailing
        /** @var \Modules\Mailing\Models\Mailing $mailing */
        $mailing = factory(Mailing::class)->create([
            'send_at'   => Carbon::now()->addMinute()->toDateTimeString(),
            'from_name' => 'Mr Sender',
        ]);

        // Intended to go to certain recipients
        $this->setupMockContactsCollection();

        $this->getBulkMailer()->send($mailing);

        $this->assertEquals('sent', $mailing->stateIs());

        $this->assertEquals(10, MailingRecipient::count());

        $this->assertDatabaseHas('external_messages', ['mailing_id' => $mailing->id], 'mailing');

        $this->assertEquals('Mr Sender', $mailing->getFromName());
    }

    /** @test */
    public function it_can_send_a_mailing_to_specific_contacts_in_a_recipient_group()
    {
        // Given a Mailing
        /** @var \Modules\Mailing\Models\Mailing $mailing */
        $mailing = factory(Mailing::class)->create([
            'send_at'   => Carbon::now()->addMinute()->toDateTimeString(),
            'from_name' => 'Mr Sender',
        ]);

        // Build the contacts (default 10)
        $contacts = $this->setupMockContactsCollection();

        //Take 3 contact ids from the contact list, and add them to the mailing properties
        $intendedRecipients = $contacts->pluck('uuid')->take(3);
        $mailing->setProperties([
            'contact_ids' => $intendedRecipients->toArray()
        ]);
        $mailing->save();

        $this->getBulkMailer()->send($mailing);

        $this->assertEquals('sent', $mailing->stateIs());

        //check there are only 3 mailing recipients
        $this->assertEquals(3, MailingRecipient::forMailing($mailing->id)->count());

        //verify there are no other mailing recipients other than the intended contacts
        $otherRecipients = MailingRecipient::forMailing($mailing->id)
            ->whereNotIn('contact_uuid', $intendedRecipients)
            ->count();

        $this->assertEquals(0, $otherRecipients);
    }

    /** @test */
    public function it_does_not_send_message_that_has_send_date_too_far_in_future()
    {
        factory(MailgunDomainRating::class)->states(['default', 'digital-edition'])->create();

        // Given a Mailing due to be sent in the future
        /** @var \Modules\Mailing\Models\Mailing $mailing */
        $mailing = factory(Mailing::class)->create([
            'send_at' => Carbon::now()->addMonths(6),
        ]);

        try {
            $this->getBulkMailer()->send($mailing);
        } catch (UnableToSendMailing $e) {
            $this->assertEquals(Mailing::INITIAL_STATE, $mailing->stateIs());

            $this->assertEquals($mailing->getId(), $e->getMailing()->getId());

            $this->assertEquals(UnableToSendMailing::TOO_FAR_IN_FUTURE, $e->getMessage());
        }
    }

    /** @test */
    public function it_fails_when_in_incorrect_state()
    {
        factory(MailgunDomainRating::class)->states(['default', 'digital-edition'])->create();

        // Given a Mailing
        /** @var \Modules\Mailing\Models\Mailing $mailing */
        $mailing = factory(Mailing::class)->create([
            'state'   => 'sent',
            'send_at' => Carbon::now()->addMinute()->toDateTimeString(),
        ]);

        try {
            $this->getBulkMailer()->send($mailing);
        } catch (UnableToSendMailing $e) {
            $this->assertEquals(sprintf(
                UnableToSendMailing::TRANSITION_NOT_ALLOWED,
                'preparing',
                'sent'
            ), $e->getMessage());
        }

        $this->assertEquals('sent', $mailing->stateIs());

        $this->assertEquals(0, MailingRecipient::count());

        $this->assertEmpty(ExternalMessage::count());
    }

    /** @test */
    public function it_marks_mailing_as_failed_when_transport_fails()
    {
        // Given a Mailing
        /** @var \Modules\Mailing\Models\Mailing $mailing */
        $mailing = factory(Mailing::class)->create([
            'send_at' => Carbon::now()->addMinute()->toDateTimeString(),
        ]);

        // with intended recipients
        $this->setupMockContactsCollection();

        // Setup transport
        $this->app->singleton('bulk-mailing.transport', function () {
            return new FailingTransport;
        });

        $this->expectsNotification(app(TeamsNotifiable::class), MailingSendFailed::class);

        try {
            $this->getBulkMailer()->send($mailing);
        } catch (UnableToSendMailing $e) {
            $this->assertEquals(
                sprintf(UnableToSendMailing::TRANSPORT_ERROR, 'Unable to send mailing'),
                $e->getMessage()
            );
        }

        $this->assertEquals('failed', $mailing->stateIs());

        $this->assertEquals(0, MailingRecipient::count());

        $this->assertEmpty(ExternalMessage::count());
    }

    /** @test */
    public function it_does_not_send_message_for_invalid_product()
    {
        /** @var \Modules\Mailing\Models\Mailing $mailing */
        $mailing = factory(Mailing::class)->state('invalid-product')->create([
            'send_at' => Carbon::now()->addMinute()->toDateTimeString(),
        ]);

        // with intended recipients
        $this->setupMockContactsCollection();

        try {
            $this->getBulkMailer()->send($mailing);
        } catch (UnableToSendMailing $e) {
            $this->assertEquals(
                sprintf(UnableToSendMailing::INVALID_PRODUCT, $mailing->id),
                $e->getMessage()
            );
        }
    }

    /** @test */
    public function it_fails_to_send_mailings_when_no_recipients_are_found()
    {
        /** @var \Modules\Mailing\Models\Mailing $mailing */
        $mailing = factory(Mailing::class)
            ->state('local-events')
            ->create(['send_at' => Carbon::now()]);

        $this->setContactsToReturnEmpty();

        $this->getBulkMailer()->send($mailing);

        $mailing->refresh();

        $this->assertEquals('failed-no-recipients', $mailing->stateIs());
    }

    /** @test */
    public function it_ignores_unsubscribed_emails_from_the_email_address_ratings_table_for_account_and_product()
    {
        // Given a Mailing
        /** @var \App\Models\Mailing $mailing */
        $mailing = factory(Mailing::class)->create([
            'send_at' => Carbon::now()->addMinute()->toDateTimeString(),
            'from_name' => 'Mr Sender',
        ]);

        // Setup contacts to come back positively
        $contacts = $this->setupMockContactsCollection(5);

        //Take the first contact returned, and create entries forEmailAddress and UnsubscribedRecipient
        $contactToUnsub = $contacts->first();
        $emailAddress = factory(EmailAddress::class)->create(['address' => $contactToUnsub->email]);
        $unsubEmail = factory(EmailAddressRating::class)->create([
            "email_address_id" => $emailAddress->id,
            "account_id" => $mailing->account_id,
            "product_id" => $mailing->getProduct(),
            "unsubscribed_at" => Carbon::parse("3 days ago")
        ]);

        //send the mailing
        $this->getBulkMailer()->send($mailing);

        $mailing->refresh();

        //rest results
        $this->assertEquals('sent', $mailing->stateIs());

        $this->assertEquals(4, MailingRecipient::count());

        $this->assertDatabaseMissing('mailing_recipients', [
            'mailing_id' => $mailing->id,
            'email_address_id' => $emailAddress->id
        ],
            'mailing');

        $this->assertEquals('Mr Sender', $mailing->getFromName());
    }

    /** @test */
    public function it_will_not_ignore_unsubscribed_emails_from_the_email_address_ratings_table_for_a_different_account()
    {
        // Given a Mailing
        /** @var \App\Models\Mailing $mailing */
        $mailing = factory(Mailing::class)->create([
            'send_at' => Carbon::now()->addMinute()->toDateTimeString(),
            'from_name' => 'Mr Sender',
        ]);

        // Setup contacts to come back positively
        $contacts = $this->setupMockContactsCollection(5);

        //Take the first contact returned, and create entries forEmailAddress and UnsubscribedRecipient
        $contactToUnsub = $contacts->first();
        $emailAddress = factory(EmailAddress::class)->create(['address' => $contactToUnsub->email]);
        $unsubEmail = factory(EmailAddressRating::class)->create([
            "email_address_id" => $emailAddress->id,
            "account_id" => $mailing->account_id + 1, //just update the account id to another account
            "product_id" => $mailing->getProduct(),
            "unsubscribed_at" => Carbon::parse("3 days ago")
        ]);

        //send the mailing
        $this->getBulkMailer()->send($mailing);

        $mailing->refresh();

        //rest results
        $this->assertEquals('sent', $mailing->stateIs());

        $this->assertEquals(5, MailingRecipient::count());
    }

    /** @test */
    public function it_will_not_ignore_unsubscribed_emails_from_the_email_address_ratings_table_for_a_different_product()
    {
        // Given a Mailing
        /** @var \App\Models\Mailing $mailing */
        $mailing = factory(Mailing::class)->create([
            'send_at' => Carbon::now()->addMinute()->toDateTimeString(),
            'from_name' => 'Mr Sender',
        ]);

        // Setup contacts to come back positively
        $contacts = $this->setupMockContactsCollection(5);

        //Take the first contact returned, and create entries forEmailAddress and UnsubscribedRecipient
        $contactToUnsub = $contacts->first();
        $emailAddress = factory(EmailAddress::class)->create(['address' => $contactToUnsub->email]);
        $unsubEmail = factory(EmailAddressRating::class)->create([
            "email_address_id" => $emailAddress->id,
            "account_id" => $mailing->account_id,
            "product_id" => $mailing->getProduct() + 1, //just update the product id so it is different than the one used by the mailing
            "unsubscribed_at" => Carbon::parse("3 days ago")
        ]);

        //send the mailing
        $this->getBulkMailer()->send($mailing);

        $mailing->refresh();

        //rest results
        $this->assertEquals('sent', $mailing->stateIs());

        $this->assertEquals(5, MailingRecipient::count());
    }

    /** @test */
    public function it_does_not_resend_to_recipients_who_received_any_related_emails()
    {
        //Set up the contacts
        $newContacts = $this->setupMockContactsCollection(7);

        // Create original parent
        /** @var \Modules\Mailing\Models\Mailing $mailing */
        $parentMailing = factory(Mailing::class)->create([
            'product'   => Plan::PLAN_ID_LOCAL_EVENT,
            'state'     => Mailing::STATE_SENT,
            'book_id'   => null,
            'send_at'   => Carbon::now()->subMinutes(10)->toDateTimeString(),
            'from_name' => 'Mr Sender',
        ]);

        //take the first 2 contact for the original email
        $originalRecipients = $newContacts->splice(0, 2);

        //convert them to Recipient models, and add them as MailingRecipient models
        $originalRecipients->map(function (Contact $contact) {
            return Recipient::fromContact($contact);
        })->each(function (Recipient $recipient) use ($parentMailing) {
            MailingRecipient::createFromMailingAndRecipient($parentMailing, $recipient);
        });

        //assert the are 2 mailing recipients for the original mailing
        $dbRecipientsCount = MailingRecipient::where('mailing_id', $parentMailing->getId())->count();
        $this->assertEquals($originalRecipients->count(), $dbRecipientsCount);

        //Create first resent mailing, resending the parent mailing
        $firstResendMailing = factory(Mailing::class)->create([
            'send_at'             => Carbon::now()->subMinutes(5)->toDateTimeString(),
            'resend_of'           => $parentMailing->id,
            'product'             => $parentMailing->product,
            'featured_image_id'   => $parentMailing->featured_image_id,
            'from_name'           => $parentMailing->from_name,
            'from_email'          => $parentMailing->from_email,
            'reply_email'         => $parentMailing->reply_email,
            'subject'             => $parentMailing->subject,
            'letter'              => $parentMailing->letter,
            'book_id'             => null,
            'recipient_group_uuids' => $parentMailing->recipient_group_uuids,
        ]);

        //take 2 more contact for the first resend
        $firstResendRecipients = $newContacts->splice(0, 2);

        //convert them to Recipient models, and add them as MailingRecipient models
        $firstResendRecipients->map(function (Contact $contact) {
            return Recipient::fromContact($contact);
        })->each(function (Recipient $recipient) use ($firstResendMailing) {
            MailingRecipient::createFromMailingAndRecipient($firstResendMailing, $recipient);
        });

        //assert the are 2 mailing recipients for the original mailing
        $dbFirstResentRecipientsCount = MailingRecipient::where('mailing_id', $firstResendMailing->getId())->count();
        $this->assertEquals($firstResendRecipients->count(), $dbFirstResentRecipientsCount);

        //Create second resent mailing, resending the parent mailing
        $otherResendMailing = factory(Mailing::class)->create([
            'send_at'             => Carbon::now()->subMinutes(5)->toDateTimeString(),
            'resend_of'           => $parentMailing->id,
            'product'             => $parentMailing->product,
            'featured_image_id'   => $parentMailing->featured_image_id,
            'from_name'           => $parentMailing->from_name,
            'from_email'          => $parentMailing->from_email,
            'reply_email'         => $parentMailing->reply_email,
            'subject'             => $parentMailing->subject,
            'letter'              => $parentMailing->letter,
            'book_id'             => null,
            'recipient_group_ids' => $parentMailing->recipient_group_ids,
        ]);

        //take 2 more contact for the second resend
        $otherResendRecipients = $newContacts->splice(0, 2);

        //convert them to Recipient models, and add them as MailingRecipient models
        $firstResendRecipients->map(function (Contact $contact) {
            return Recipient::fromContact($contact);
        })->each(function (Recipient $recipient) use ($otherResendMailing) {
            MailingRecipient::createFromMailingAndRecipient($otherResendMailing, $recipient);
        });

        //assert the are 2 mailing recipients for the original mailing
        $dbOtherResentRecipientsCount = MailingRecipient::where('mailing_id', $otherResendMailing->getId())->count();
        $this->assertEquals($firstResendRecipients->count(), $dbOtherResentRecipientsCount);

        //Create a new mailing, resending the first reend
        $newMailing = factory(Mailing::class)->create([
            'send_at'             => Carbon::now()->addMinute()->toDateTimeString(),
            'resend_of'           => $firstResendMailing->id,
            'product'             => $firstResendMailing->product,
            'featured_image_id'   => $firstResendMailing->featured_image_id,
            'from_name'           => $firstResendMailing->from_name,
            'from_email'          => $firstResendMailing->from_email,
            'reply_email'         => $firstResendMailing->reply_email,
            'subject'             => $firstResendMailing->subject,
            'letter'              => $firstResendMailing->letter,
            'book_id'             => null,
            'recipient_group_ids' => $firstResendMailing->recipient_group_ids,
        ]);

        //send the mailing
        $this->getBulkMailer()->send($newMailing);

        //verify it was sent
        $this->assertEquals('sent', $newMailing->stateIs());

        //verify there is a new mailing recipient
        $dbNewRecipientsCount = MailingRecipient::where('mailing_id', $newMailing->getId())->count();
        $this->assertEquals($newContacts->count(), $dbNewRecipientsCount);

        //verify the data in the new recipient
        $expectedEmailId = EmailAddress::getForAddress($newContacts->first()->email)->id;
        $this->assertDatabaseHas('mailing_recipients', [
            'mailing_id' => $newMailing->getId(),
            'contact_uuid' => $newContacts->first()->uuid,
            'email_address_id' => $expectedEmailId
        ], 'mailing');
    }

    /** @test */
    public function it_rejects_recipients_who_have_invalid_email_address_values()
    {
        // Given a Mailing
        /** @var \Modules\Mailing\Models\Mailing $mailing */
        $mailing = factory(Mailing::class)->create([
            'send_at'   => Carbon::now()->addMinute()->toDateTimeString(),
            'from_name' => 'Mr Sender',
        ]);

        // Intended to go to certain recipients
        $contacts = $this->setupMockContactsCollection();

        //replace the email on the first contact with an invalid email
        $contacts->first()->email = "invalidEmail";

        $this->getBulkMailer()->send($mailing);

        //assert the mailing was still sent
        $this->assertEquals('sent', $mailing->stateIs());

        $expectedRecipients = $contacts->count() - 1;
        $this->assertEquals($expectedRecipients, MailingRecipient::count());
    }

    /** @test */
    public function it_can_send_a_mailing_to_cc()
    {
        // Given a Mailing
        /** @var \Modules\Mailing\Models\Mailing $mailing */
        $mailing = factory(Mailing::class)->create([
            'send_at'   => Carbon::now()->addMinute()->toDateTimeString(),
            'from_name' => 'Mr Sender',
        ]);

        // Intended to go to certain recipients
        $this->setupMockContactsCollection(2);

        $this->getBulkMailer()->send($mailing);

        $this->assertEquals('sent', $mailing->stateIs());

        $this->assertEquals(2, MailingRecipient::count());

        $this->assertDatabaseHas('external_messages', ['mailing_id' => $mailing->id], 'mailing');

        $this->assertEquals('Mr Sender', $mailing->getFromName());
    }

    protected function getBulkMailer() : BulkMailer
    {
        return app(BulkMailer::class);
    }
}

class FailingTransport extends Transport
{
    public function send(BulkMailable $mailable, Collection $recipients) : void
    {
        throw new \Exception("Unable to send mailing");
    }

    public function driver() : Transport
    {
        return $this;
    }
}
