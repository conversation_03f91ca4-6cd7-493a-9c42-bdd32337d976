<?php

namespace Modules\Mailing\Tests;

use App\Contracts\AccountRepository;
use App\Models\Account;
use Mockery;
use Tests\DoesNotCreateAccount;

abstract class TestCase extends \Tests\TestCase implements DoesNotCreateAccount
{
    /** @var \App\Contracts\AccountRepository|\Mockery\LegacyMockInterface|\Mockery\MockInterface */
    private $mockAccountRepository;

    protected function setUpTraits()
    {
        parent::setUpTraits();

        $uses = array_flip(class_uses_recursive(static::class));

        if (isset($uses[ RefreshMailingDatabase::class ])) {
            $this->refreshMailingDatabase();
        }

        if (isset($uses[ TestsMailGunApi::class ])) {
            $this->bootTestsMailGunApi();
        }
    }

    public function setupAccountRepositoryMockForAccount(Account $account): void
    {
        if (! $this->mockAccountRepository) {
            $this->mockAccountRepository = Mockery::mock(AccountRepository::class);
        }

        $this->mockAccountRepository->shouldReceive('find')
            ->with($account->id)
            ->andReturn($account);

        $this->app->instance(
            AccountRepository::class,
            $this->mockAccountRepository
        );
    }
}
