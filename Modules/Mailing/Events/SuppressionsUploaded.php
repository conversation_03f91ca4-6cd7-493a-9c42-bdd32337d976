<?php

namespace Modules\Mailing\Events;

use Illuminate\Support\Collection;

abstract class SuppressionsUploaded
{
    /** @var string */
    protected $domain;

    /** @var \Illuminate\Support\Collection */
    protected $events;

    /** @var int|null */
    protected $accountId;

    public function __construct(string $domain, Collection $events, ?int $accountId = null)
    {
        $this->domain = $domain;
        $this->events = $events;
        $this->accountId = $accountId;
    }

    public function getDomain() : string
    {
        return $this->domain;
    }

    public function getEvents() : Collection
    {
        return $this->events;
    }

    public function getAccountId() : ?int
    {
        return $this->accountId;
    }
}
