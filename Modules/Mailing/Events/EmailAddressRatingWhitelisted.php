<?php

namespace Modules\Mailing\Events;

use Carbon\Carbon;
use Modules\Mailing\Models\EmailAddressRating;

class EmailAddressRatingWhitelisted
{
    /** @var string */
    public $emailAddress;

    /** @var int */
    public $accountId;

    /** @var int */
    public $productId;

    /** @var \Carbon\Carbon */
    public $whitelistedAt;

    public function __construct(EmailAddressRating $emailAddressRating, Carbon $whitelistedAt)
    {
        $this->emailAddress = $emailAddressRating->email->address;
        $this->accountId = $emailAddressRating->account_id;
        $this->productId = $emailAddressRating->product_id;
        $this->whitelistedAt = $whitelistedAt;
    }
}
