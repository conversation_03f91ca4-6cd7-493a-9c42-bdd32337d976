<?php

namespace Modules\Mailing\Events\Mailgun;

use Modules\Mailing\Models\Mailing;
use Carbon\Carbon;
use Exception;

class SendFailed
{
    /** @var \Carbon\Carbon */
    public $occurredAt;

    /** @var \Modules\Mailing\Models\Mailing */
    public $mailing;

    /** @var \Exception */
    public $exception;

    public function __construct(Mailing $mailing, Exception $exception, Carbon $occurredAt)
    {
        $this->occurredAt = $occurredAt;
        $this->mailing = $mailing;
        $this->exception = $exception;
    }
}
