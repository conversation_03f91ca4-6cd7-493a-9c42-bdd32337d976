<?php

namespace Modules\Mailing\Http\Controllers\Api;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Modules\Mailing\Jobs\FetchEventsForMailing;
use Modules\Mailing\Models\Mailing;
use Symfony\Component\HttpFoundation\Response;

class FetchEventsForMailingController
{
    public function __invoke(Request $request)
    {
        $mailingId = $request->get('mailing_id');
        $cache_key = sprintf('fetch-mailing-events:%s', $mailingId);
        $cache_exists = Cache::get($cache_key);
        $mailing = Mailing::find($mailingId);

        if ($mailing && !$cache_exists) {
            dispatch(new FetchEventsForMailing($mailing));
            Cache::put($cache_key, true, 30);
            return new JsonResponse(['data' => ['job was dispatched']], Response::HTTP_OK);
        }

        return new JsonResponse(['data' => ['job was not sent']], Response::HTTP_OK);
    }
}
