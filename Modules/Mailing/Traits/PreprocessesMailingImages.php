<?php

namespace Modules\Mailing\Traits;

trait PreprocessesMailingImages
{
    public function getImageUrlForMailing($currentUrl, ?bool $preprocessImage = false): string
    {
        //if the passed url is empty, return emprty string
        if (empty($currentUrl)) {
            return '';
        }

        //if we don't need to preprocess the image, return the current url
        if (!$preprocessImage) {
            return $currentUrl;
        }

        //check if the url is a DAM asset
        $parsedUrl = parse_url($currentUrl);

        //if the url is not a DAM asset, return the current url
        if (
            (!isset($parsedUrl['host']) || strpos($parsedUrl['host'], 'dam') === false) ||
            (!isset($parsedUrl['path']) || strpos($parsedUrl['path'], 'api/asset') === false)
        ) {
            return $currentUrl;
        }

        //We have a url, and we need to preprocess it
        //Make a curl request to the DAM for the image, adding the return cdn parameter
        //return the response
        \Illuminate\Support\Facades\Log::info('CB CDN: Preprocessing image for mailing: ' . $currentUrl);

        // Add the cdn parameters to the url
        $getCdnUrl = $currentUrl;
        $getCdnUrl .= (isset($parsedUrl['query'])) ? '&' : '?';
        $getCdnUrl.= 'cdn=1&response_format=link';

        //Make the curl request
        $ch = curl_init($getCdnUrl);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $resultUrl = curl_exec($ch);

        if (curl_errno($ch)) {
            // Handle error
            $error_msg = curl_error($ch);
            \Illuminate\Support\Facades\Log::info('CB CDN: Preprocessing error: ' . $error_msg);
            curl_close($ch);
            return $currentUrl;
        }

        // Close curl, and return the result
        curl_close($ch);
        return $resultUrl;
    }
}
