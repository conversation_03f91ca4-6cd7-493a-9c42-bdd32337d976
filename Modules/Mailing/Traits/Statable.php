<?php

namespace Modules\Mailing\Traits;

use SM\Factory\FactoryInterface;
use SM\StateMachine\StateMachine;

trait Statable
{
    /** @var \SM\StateMachine\StateMachine */
    protected $stateMachine;

    public function history()
    {
        return $this->hasMany(self::HISTORY_MODEL['name']);
    }

    public function stateMachine() : StateMachine
    {
        if (! $this->stateMachine) {
            $this->stateMachine = app(FactoryInterface::class)->get($this, self::SM_CONFIG);
        }

        return $this->stateMachine;
    }

    public function stateIs()
    {
        return $this->stateMachine()->getState();
    }

    public function transition(string $transition)
    {
        return $this->stateMachine()->apply($transition);
    }

    public function transitionAllowed(string $transition) : bool
    {
        return $this->stateMachine()->can($transition);
    }

    public function canTransition(string $transition) : bool
    {
        return $this->transitionAllowed($transition);
    }

    public function addHistoryLine(array $transitionData)
    {
        $this->save();

        return $this->history()->create($transitionData);
    }
}
