<?php

namespace Modules\Mailing\Providers;

use Mo<PERSON>les\Mailing\Events\HardBouncesUploaded;
use Modules\Mailing\Events\UnsubscribesUploaded;
use Modules\Mailing\Interservice\Listeners\CancelMailingsSubscriber;
use Mo<PERSON><PERSON>\Mailing\Interservice\Listeners\FromNameSubscriber;
use Mo<PERSON>les\Mailing\Listeners\LogSuppressionsUploaded;
use Modules\Mailing\Listeners\MailgunEventsSubscriber;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\Mailing\Listeners\StateManagement\PostTransition;
use SM\Event\SMEvents;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        SMEvents::POST_TRANSITION   => [
            PostTransition::class,
        ],
        HardBouncesUploaded::class  => [
            LogSuppressionsUploaded::class,
        ],
        UnsubscribesUploaded::class => [
            LogSuppressionsUploaded::class,
        ],
    ];

    protected $subscribe = [
        MailgunEventsSubscriber::class,
        FromNameSubscriber::class,
        CancelMailingsSubscriber::class,
    ];
}
