<?php

namespace Modules\Mailing\Providers;

use App\Models\EmailAddress;
use Carbon\Carbon;
use Faker\Generator;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Database\Eloquent\Factory;
use Illuminate\Queue\Console\RestartCommand;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\ServiceProvider;
use Infrastructure\Contacts\Models\EmailAddress as ContactEmailAddress;
use Modules\Mailing\Console\CollectEvents;
use Modules\Mailing\Console\PurgeOldEvents;
use Modules\Mailing\Console\PurgeOldMailingRecipients;
use Modules\Mailing\Console\ScheduleMailings;
use Modules\Mailing\Console\SetAccountDomain;
use Modules\Mailing\Contracts\MailingRepository as MailingRepositoryContract;
use Modules\Mailing\Models\MailgunEventDetail;
use Modules\Mailing\Models\MailingRecipient;
use Modules\Mailing\Models\MailingRecipientEvent;
use Modules\Mailing\Observers\ContactEmailAddressSummaryObserver;
use Modules\Mailing\Observers\EmailEventObserver;
use Modules\Mailing\Observers\MailingRecipientObserver;
use Modules\Mailing\Observers\MailingRecipientEmailAddressSummaryObserver;
use Modules\Mailing\Observers\WhitelistProfileEmailAddressesObserver;
use Modules\Mailing\Repositories\EloquentMailingRepository;

class MailingServiceProvider extends ServiceProvider
{
    /**
     * Indicates if loading of the provider is deferred.
     *
     * @var bool
     */
    protected $defer = false;

    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->registerFactories();

        //
        MailgunEventDetail::observe(EmailEventObserver::class);
        MailingRecipientEvent::observe(MailingRecipientObserver::class);
        EmailAddress::observe(WhitelistProfileEmailAddressesObserver::class);

        // Email Address Summary Observer
        ContactEmailAddress::observe(ContactEmailAddressSummaryObserver::class);
        MailingRecipient::observe(MailingRecipientEmailAddressSummaryObserver::class);

        $this->app->booted(function () {
            $this->schedule(app(Schedule::class));
        });
    }

    public function register()
    {
        // todo: mailing repository contract
        $this->registerRepositories();

        $this->registerCommands();
    }

    protected function registerConfig()
    {
        $this->publishes([
            __DIR__ . '/../Config/config.php'        => config_path('mailing.php'),
            __DIR__ . '/../Config/bulk-mailing.php'  => config_path('bulk-mailing.php'),
            __DIR__ . '/../Config/state-machine.php' => config_path('mailing-state-machine.php'),
        ], 'config');

        $this->mergeConfigFrom(__DIR__ . '/../Config/config.php', 'mailing');
        $this->mergeConfigFrom(__DIR__ . '/../Config/bulk-mailing.php', 'bulk-mailing');
        $this->mergeConfigFrom(__DIR__ . '/../Config/state-machine.php', 'state-machine');
    }

    public function registerViews()
    {
        $viewPath = resource_path('views/modules/mailing');

        $sourcePath = __DIR__.'/../Resources/views';

        $this->publishes([
            $sourcePath => $viewPath
        ], 'views');

        $paths = collect(Config::get('view.paths'))
            ->map(function ($path) {
                return $path . '/modules/mailing';
            })
            ->merge([$sourcePath])
            ->filter(function ($path) {
                return is_dir($path);
            })->toArray();

        $this->loadViewsFrom($paths, 'mailing');
    }

    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/book');

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'book');
        } else {
            $this->loadTranslationsFrom(__DIR__ . '/../Resources/lang', 'book');
        }
    }

    public function registerFactories()
    {
        if (! class_exists(Generator::class)) {
            return;
        }

        app(Factory::class)->load(__DIR__ . '/../Database/factories');
    }

    public function provides()
    {
        return [];
    }

    private function registerRepositories()
    {
        $this->app->bind(MailingRepositoryContract::class, function () {
            return $this->app->make(EloquentMailingRepository::class);
        });
    }

    private function registerCommands()
    {
        $this->commands([
            CollectEvents::class,
            ScheduleMailings::class,
            PurgeOldEvents::class,
            PurgeOldMailingRecipients::class,
            SetAccountDomain::class,
        ]);
    }

    protected function schedule(Schedule $schedule)
    {
        $startOfDay = Carbon::parse('midnight', 'America/New_York')
            ->setTimezone(config('app.timezone'));

        $config = config('bulk-mailing');

        // Collect events every 5 minutes, for events that occurred in the past 15 minutes.
        if (Arr::get($config, 'event_collection.daily_enabled', false)) {
            $this->scheduleMailingEventCollection($schedule);
        }

        // Schedule mailings to be sent
        if ($config['enabled']) {
            $schedule->command(ScheduleMailings::class)
                ->everyFiveMinutes()
                ->withoutOverlapping();
        }

        // Used to combat memory creep issues that I can't solve otherwise at this moment.
        $schedule->command(RestartCommand::class)->hourly();

        //set up the event purge command
        $schedule->command(PurgeOldEvents::class, [
            '--interval'  => Arr::get($config, 'purge-events.default_interval'),
        ])
            ->dailyAt(
                (Carbon::parse(Arr::get($config, 'purge-events.default_schedule_time'), 'America/New_York'))
                    ->timezone('UTC') // App is configured to operate in UTC, so adjust back to UTC
                    ->format('H:i')
            );

        //set up the recipient purge command
        //CORE-1567 - Disable cron until it is reworked
        //$schedule->command(PurgeOldMailingRecipients::class)
        //    ->dailyAt(
        //        $startOfDay->copy()->addHours(4)->format('H:i')
        //    );
    }

    private function scheduleMailingEventCollection(Schedule $schedule)
    {
        // Gather reports every two hours for mailings sent within the last 36 hours.
        // Skip any emails sent within 2 hours. because those will be within 30 minutes.
        $schedule->command(CollectEvents::class, [
            '--sent_after'  => '-36 hours',
            '--sent_before' => '-2 hours'
        ])
            ->cron('0 */2 * * *');

        // Gather reports every day at midnight EDT/EST for mailings sent in the past thirty days
        $schedule->command(CollectEvents::class, [
            '--sent_after'  => 'midnight -30 days',
        ])
            ->dailyAt(
                (Carbon::parse("midnight", 'America/New_York'))
                    ->timezone('UTC') // App is configured to operate in UTC, so adjust back to UTC
                    ->format('H:i')
            );
    }
}
