<?php

namespace Modules\Mailing\Mailgun\Suppressions;

use Modules\Mailing\Models\EventType;
use Modules\Mailing\Models\MailgunEventDetail;
use Modules\Mailing\Models\MailingRecipientEvent;
use Carbon\Carbon;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class Suppression implements Arrayable, Jsonable
{
    /** @var \Modules\Mailing\Mailgun\Suppressions\SuppressionType */
    private $type;

    /** @var string */
    protected $email;

    /** @var \Carbon\Carbon */
    protected $createdAt;

    /** @var null|string */
    protected $error = null;

    public function __construct(SuppressionType $type, string $email, Carbon $createdAt, ?string $error = null)
    {
        $this->type = $type;
        $this->email = $email;
        $this->createdAt = $createdAt;
        $this->error = $error;
    }

    /**
     * @return string
     */
    public function getEmail() : string
    {
        return $this->email;
    }

    /**
     * @param string $email
     *
     * @return Suppression
     */
    public function setEmail(string $email) : Suppression
    {
        $this->email = $email;

        return $this;
    }

    public function getCreatedAt() : Carbon
    {
        return $this->createdAt;
    }

    public function setCreatedAt(Carbon $createdAt) : Suppression
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getError() : ?string
    {
        return $this->error;
    }

    public function setError(?string $error) : Suppression
    {
        $this->error = $error;

        return $this;
    }

    public function getType() : SuppressionType
    {
        return $this->type;
    }

    public static function fromRecipientEvent(MailingRecipientEvent $event)
    {
        switch ($event->event_type_id) {
            case EventType::HARD_BOUNCE:
                $type = new Hardbounce;
                break;
            case EventType::UNSUBSCRIBE:
                $type = new Unsubscribe;
                break;
            case EventType::SPAM_COMPLAINT:
                $type = new Complaint;
                break;
        }

        if (! $type) {
            throw new \InvalidArgumentException("Not a valid event type to convert to a suppression");
        }

        return new self(
            $type,
            $event->recipient->email->address,
            $event->occurred_at
        );
    }

    public static function fromMailgunEvent(MailgunEventDetail $event) : self
    {
        switch (get_class($event->getType())) {
            case \Modules\Mailing\Entities\EmailEvents\HardBounce::class:
                $type = new Hardbounce;
                break;
            case \Modules\Mailing\Entities\EmailEvents\Unsubscribe::class:
                $type = new Unsubscribe;
                break;
            case \Modules\Mailing\Entities\EmailEvents\SpamComplaint::class:
                $type = new Complaint;
                break;
            default:
                $type = null;
        }

        if (! $type) {
            throw new \InvalidArgumentException("Not a valid event type to convert to a suppression");
        }

        return new self(
            $type,
            $event->getEmailAddress(),
            $event->getOccurredAt()
        );
    }

    public function toArray() : array
    {
        return [
            'address'    => $this->getEmail(),
            'created_at' => $this->getCreatedAt()->toRfc7231String(),
        ];
    }

    public function toJson($options = 0)
    {
        return json_encode($this->toArray(), $options);
    }
}
