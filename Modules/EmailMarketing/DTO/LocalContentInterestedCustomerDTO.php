<?php

namespace Modules\EmailMarketing\DTO;

use App\Models\Location;
use Illuminate\Support\Arr;
use Spatie\DataTransferObject\DataTransferObject;

class LocalContentInterestedCustomerDTO extends DataTransferObject
{
    /** @var string|null */
    public $address1;

    /** @var string|null */
    public $address2;

    /** @var string|null */
    public $city;

    /** @var string|null */
    public $state;

    /** @var string|null */
    public $zip;

    /** @var float|null */
    public $latitude;

    /** @var float|null */
    public $longitude;

    public static function fromLocation(Location $location) : self
    {
        return new self(Arr::only(
            $location->toArray(),
            [
                'address1',
                'address2',
                'city',
                'state',
                'zip',
                'latitude',
                'longitude',
            ]
        ));
    }
}
