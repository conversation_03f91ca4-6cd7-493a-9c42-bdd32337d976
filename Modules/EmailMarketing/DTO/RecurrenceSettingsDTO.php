<?php

namespace Modules\EmailMarketing\DTO;

use Modules\EmailMarketing\Models\RecurrenceSettings;
use Spatie\DataTransferObject\DataTransferObject;

class RecurrenceSettingsDTO extends DataTransferObject
{
    /** @var string */
    public $frequency;

    /** @var int */
    public $email_from_id;

    /** @var \Carbon\Carbon */
    public $mailing_date;

    /** @var string|null */
    public $mailing_subject;

    /** @var bool */
    public $persist_subject;

    /** @var string|null */
    public $mailing_heading;

    /** @var bool */
    public $persist_heading;

    /** @var string|null */
    public $mailing_body;

    /** @var array */
    public $recipient_group_ids;

    /** @var bool */
    public $is_enabled;

    /** @var bool */
    public $failed;

    /** @var array|null */
    public $mailing_customizations;

    public static function fromModel(RecurrenceSettings $recurrenceSettings)
    {
        return new self([
            'frequency'              => $recurrenceSettings->frequency,
            'email_from_id'          => $recurrenceSettings->email_from_id,
            'mailing_date'           => $recurrenceSettings->mailing_date,
            'mailing_subject'        => $recurrenceSettings->mailing_subject,
            'persist_subject'        => (bool)$recurrenceSettings->persist_subject,
            'mailing_heading'        => $recurrenceSettings->mailing_heading,
            'persist_heading'        => (bool)$recurrenceSettings->persist_heading,
            'mailing_body'           => $recurrenceSettings->mailing_body,
            'recipient_group_ids'    => $recurrenceSettings->recipient_group_ids,
            'is_enabled'             => $recurrenceSettings->is_enabled,
            'failed'                 => $recurrenceSettings->failed,
            'mailing_customizations' => $recurrenceSettings->mailing_customizations
        ]);
    }
}
