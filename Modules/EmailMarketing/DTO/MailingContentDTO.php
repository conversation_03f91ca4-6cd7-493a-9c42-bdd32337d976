<?php

namespace Modules\EmailMarketing\DTO;

use Carbon\Carbon;
use Domain\Mailings\DTO\MailingContentCustomizationsDTO;
use Domain\Mailings\Enums\LocalEventsBackgroundImages;
use Illuminate\Support\Arr;
use Modules\Mailing\Models\Mailing;
use Ramsey\Uuid\Uuid;
use <PERSON>tie\DataTransferObject\DataTransferObject;

class MailingContentDTO extends DataTransferObject
{
    /** @var int|null */
    public $mailing_id;

    /** @var \Ramsey\Uuid\UuidInterface|null */
    public $uuid;

    /** @var string */
    public $market_uuid;

    /** @var string|null */
    public $email_from_address;

    /** @var string|null */
    public $mailing_subject;

    /** @var string|null */
    public $mailing_heading;

    /** @var string|null */
    public $mailing_body;

    /** @var array */
    public $recipient_group_uuids;

    /** @var \Carbon\Carbon|null */
    public $event_dates_start_at;

    /** @var \Carbon\Carbon|null */
    public $event_dates_end_at;

    /** @var \Carbon\Carbon */
    public $sent_on;

    /** @var string|null */
    public $featured_image_url;

    /** @var string|null */
    public $mail_content_html;

    /** @var string|null */
    public $mail_content_text;

    /** @var array|null */
    public $properties;

    /** @var MailingContentCustomizationsDTO|null */
    public $customizations;

    public static function fromModel(Mailing $mailing)
    {
        $newDto = new self([
            'mailing_id' => $mailing->getId(),
            'uuid' => Uuid::fromString($mailing->uuid),
            'market_uuid' => $mailing->properties['market_uuid'],
            'email_from_address' => $mailing->getReplyEmail(),
            'mailing_subject' => $mailing->getSubject(),
            'mailing_body' => $mailing->getLetter(),
            'recipient_group_uuids' => $mailing->getRecipientGroupUuids(),
            'event_dates_start_at' => Carbon::parse($mailing->properties['event_dates_start_at']),
            'event_dates_end_at' => Carbon::parse($mailing->properties['event_dates_end_at']),
            'sent_on' => $mailing->getSendAt(),
            'featured_image_url' => $mailing->featuredImage->url,
            'mail_content_html' => $mailing->getHtmlBody(),
            'mail_content_text' => $mailing->getTextBody(),
            'properties' => $mailing->getProperties(),
        ]);

        $newDto->customizations = MailingContentCustomizationsDTO::fromOptionsArray(
            LocalEventsBackgroundImages::class,
            Arr::get($mailing->getProperties(), 'customizations', [])
        );

        return $newDto;
    }
}
