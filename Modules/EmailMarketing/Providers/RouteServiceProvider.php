<?php

namespace Modules\EmailMarketing\Providers;

use Illuminate\Support\Facades\Route;
use Infrastructure\Http\AbstractRouteServiceProvider;
use Modules\EmailMarketing\Http\Controllers\LocalEvents\FetchSharedMailingController;

class RouteServiceProvider extends AbstractRouteServiceProvider
{
    /**
     * This namespace is applied to your controller routes.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'Modules\EmailMarketing\Http\Controllers';

    public function routeDirectory() : string
    {
        return module_path('EmailMarketing') . '/routes';
    }

    protected function mapWebRoutes()
    {
        parent::mapWebRoutes();

        // We do this here to ensure we have a domain and don't fall back into not having one.
        // Build one by default if there isn't one provided in the .env/config
        Route::domain(config('emailmarketing.local_events.share_mailings_url') ?? $this->buildEventsSubDomainUrl())
            ->middleware('web')
            ->group(function () {
                Route::get('share-local-events/{mailing}', route_action(FetchSharedMailingController::class))
                    ->name('local-events-mailings.shared-mailing-content');

                //Keeping the old route just in case for existing share links that may have been saved
                Route::get('share-local-content/{mailing}', route_action(FetchSharedMailingController::class))
                    ->name('local-events-mailings.old-shared-mailing-content');

                Route::get('share-branded-post/{mailing}', route_action(FetchSharedMailingController::class))
                    ->name('branded-posts.shared-mailing-content');

                // Catches all other routes on this domain alone and redirects them to the main website
                Route::redirect('{catchall}', config('app.url').'/')
                    ->where('catchall', '.*')
                    ->name('local-events-mailings.subdomain-redirect');
            });
    }

    /**
     * Builds a default url based on the configuration's app.url value.
     * @return string
     */
    protected function buildEventsSubDomainUrl() : string
    {
        // Parse the value and only get the hostname.
        $parts = explode('.', parse_url(config('app.url'))['host']);

        // Can't do this in one line because array_shift requires a variable, not an in-memory array.
        array_shift($parts); // toss the original subdomain and keep the rest.

        return 'events.' . implode('.', $parts);
    }
}
