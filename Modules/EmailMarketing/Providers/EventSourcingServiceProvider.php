<?php

namespace Modules\EmailMarketing\Providers;

use App\EventSourcing\ServiceProvider as BaseServiceProvider;
use Modules\EmailMarketing\Domain\ContactBlock\Projectors\ContactBlockProjector;
use Modules\EmailMarketing\Domain\ContactBlock\Reactors\CreateInitialContactBlockOnEnrollmentReactor;
use Modules\EmailMarketing\Domain\ContactBlock\Reactors\ContactBlockReactor;
use Modules\EmailMarketing\Domain\ContactBlock\Reactors\RecalculateContactBlockReactor;
use Modules\EmailMarketing\Domain\LocalEvents\Projectors\MarketProjector;
use Modules\EmailMarketing\Domain\LocalEvents\Projectors\RecurrenceSettingsProjector;

class EventSourcingServiceProvider extends BaseServiceProvider
{
    /*** @var array */
    protected $projectors = [
        ContactBlockProjector::class,
        RecurrenceSettingsProjector::class,
        MarketProjector::class
    ];

    /** @var array */
    protected $reactors = [
        ContactBlockReactor::class,
        RecalculateContactBlockReactor::class,
        CreateInitialContactBlockOnEnrollmentReactor::class,
    ];
}
