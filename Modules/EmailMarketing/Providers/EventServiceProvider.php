<?php

namespace Modules\EmailMarketing\Providers;

use Domain\Contacts\Events\ContactGroupsContactsUpdatedEvent;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\EmailMarketing\Domain\LocalEvents\Listeners\DetachDeletedMarketFromEventsListener;
use Modules\EmailMarketing\Domain\LocalEvents\Listeners\RecurrenceSettingsContactGroupsContactsUpdatedListener;
use Modules\EmailMarketing\Domain\Location\Events\LocalContentMarketDeleted;
use Modules\EmailMarketing\Events\BlogContentUpdated;
use Modules\EmailMarketing\Listeners\IdentifyAccountMarket;
use Modules\EmailMarketing\Listeners\OccasionGeniusSubscriber;
use Modules\EmailMarketing\Listeners\UpdateRecurrencesWhenBlogIsEnabledListener;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        ContactGroupsContactsUpdatedEvent::class =>  [
            RecurrenceSettingsContactGroupsContactsUpdatedListener::class,
        ],
        LocalContentMarketDeleted::class => [
            DetachDeletedMarketFromEventsListener::class
        ],
        BlogContentUpdated::class => [
            UpdateRecurrencesWhenBlogIsEnabledListener::class
        ]
    ];

    /** @var array */
    protected $subscribe = [
        OccasionGeniusSubscriber::class,
        IdentifyAccountMarket::class
    ];
}
