<?php

namespace Modules\EmailMarketing\Providers;

use Elasticsearch\Client;
use Elasticsearch\ClientBuilder as ElasticBuilder;
use Faker\Generator;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Database\Eloquent\Factory;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\ServiceProvider;
use Infrastructure\Repositories\RestRepository\DefaultParser;
use Infrastructure\Repositories\RestRepository\RestClient;
use Infrastructure\Scout\ElasticsearchEngine;
use Infrastructure\Traits\CreatesGuzzleClients;
use InvalidArgumentException;
use Laravel\Scout\EngineManager;
use Modules\EmailMarketing\Console\ConfirmMarkets;
use Modules\EmailMarketing\Console\ProcessRecurrences;
use Modules\EmailMarketing\Console\RemoveEventsWithNoMarkets;
use Modules\EmailMarketing\Console\RemoveOutdatedEvents;
use Modules\EmailMarketing\Console\SetupIndexes;
use Modules\EmailMarketing\Console\SyncMarkets;
use Modules\EmailMarketing\Domain\BlogEmail\Entities\BlogContentSettings;
use Modules\EmailMarketing\Domain\BlogEmail\Entities\MarketValidationSettings;
use Modules\EmailMarketing\Domain\BlogEmail\Repositories\BlogContentSettingsRepositoryInterface;
use Modules\EmailMarketing\Domain\BlogEmail\Repositories\EloquentBlogContentSettingsRepositoryInterface;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\EloquentMarketValidationSettingsRepositoryInterface;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\EventRepositoryInterface;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\LocationRepositoryInterface;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\MarketValidationSettingsRepositoryInterface;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface;
use Modules\EmailMarketing\Entities\GlobalMarketId;
use Modules\EmailMarketing\Models\Setting;
use Modules\EmailMarketing\OccasionGenius\ApiClient;
use Modules\EmailMarketing\Repositories\CachedOccasionGeniusEventRepository;
use Modules\EmailMarketing\Repositories\CachedOccasionGeniusLocationRepository;
use Modules\EmailMarketing\Repositories\EloquentRecurrenceSettingsRepository;
use Modules\EmailMarketing\Services\BlogMailingContentService;
use Modules\EmailMarketing\Services\EventMailingContentService;
use Modules\EmailMarketing\Services\MailingContentService;
use Ramsey\Uuid\Uuid;
use Throwable;

class EmailMarketingServiceProvider extends ServiceProvider
{
    use CreatesGuzzleClients;

    /**
     * Indicates if loading of the provider is deferred.
     *
     * @var bool
     */
    protected $defer = false;

    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerFactories();
        $this->loadMigrationsFrom(__DIR__ . '/../Database/Migrations');

        $this->app->singleton('elasticsearch.client', function () {
            return ElasticBuilder::create()
                ->setHosts($this->getHosts())
                ->build();
        });

        $this->app->bind(Client::class, 'elasticsearch.client');

        app(EngineManager::class)->extend('elasticsearch', function () {
            return new ElasticsearchEngine(
                $this->app->make('elasticsearch.client'),
                config('scout.prefix')
            );
        });

        $this->registerMailingContentService();

        $this->app->booted(function () {
            $this->schedule();
        });
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->registerOccasionGeniusClient();
        $this->registerRepositories();

        $this->commands([
            ProcessRecurrences::class,
            SetupIndexes::class,
            SyncMarkets::class,
            RemoveOutdatedEvents::class,
            RemoveEventsWithNoMarkets::class
        ]);
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            __DIR__ . '/../Config/config.php' => config_path('emailmarketing.php'),
        ], 'config');
        $this->mergeConfigFrom(
            __DIR__ . '/../Config/config.php',
            'emailmarketing'
        );
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/emailmarketing');

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'emailmarketing');
        } else {
            $this->loadTranslationsFrom(__DIR__ . '/../Resources/lang', 'emailmarketing');
        }
    }

    /**
     * Register an additional directory of factories.
     *
     * @return void
     */
    public function registerFactories()
    {
        if (! class_exists(Generator::class)) {
            return;
        }

        app(Factory::class)->load(__DIR__ . '/../Database/factories');
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }

    private function registerOccasionGeniusClient() : void
    {
        $this->app->singleton('occasiongenius.api_client', function () {
            $config = config('emailmarketing.local_events.transports.occasion_genius');

            if (! $config['domain']) {
                throw new InvalidArgumentException("Invalid configuration for OccasionGenius");
            }

            return new ApiClient(
                new RestClient(
                    $this->buildClient([
                        'base_uri' => $config['domain'],
                        'headers' => [
                            'Authorization' => "Token {$config['token']}"
                        ]
                    ]),
                    new DefaultParser
                )
            );
        });

        $this->app->bind(ApiClient::class, 'occasiongenius.api_client');

        // setup global market id as an object
        $this->app->singleton(GlobalMarketId::class, function () {
            //get the global market from the OG uuid
            return GlobalMarketId::fromUuid(Uuid::fromString(config('emailmarketing.local_events.transports.occasion_genius.global_virtual_area_uuid')));
        });
    }

    private function registerRepositories() : void
    {
        // Locations
        $this->app->bind(LocationRepositoryInterface::class, CachedOccasionGeniusLocationRepository::class);

        // Events
        $this->app->bind(EventRepositoryInterface::class, CachedOccasionGeniusEventRepository::class);

        // Blog Content
        $this->app->bind(
            BlogContentSettingsRepositoryInterface::class,
            EloquentBlogContentSettingsRepositoryInterface::class
        );
        $this->app->bind(BlogContentSettings::class, function () {
            try {
                return $this->app->make(BlogContentSettingsRepositoryInterface::class)->get();
            } catch (Throwable $e) {
                // When settings table doesn't exist, just return an empty settings
                return new BlogContentSettings;
            }
        });

        // Market Validation
        $this->app->bind(
            MarketValidationSettingsRepositoryInterface::class,
            EloquentMarketValidationSettingsRepositoryInterface::class
        );
        $this->app->bind(MarketValidationSettings::class, function () {
            try {
                return $this->app->make(MarketValidationSettingsRepositoryInterface::class)->get();
            } catch (Throwable $e) {
                // When settings table doesn't exist, just return an empty settings
                return new MarketValidationSettings;
            }
        });

        // Mailings
        $this->app->bind(RecurrenceSettingsRepositoryInterface::class, EloquentRecurrenceSettingsRepository::class);
    }

    private function schedule() : void
    {
        /** @var \Illuminate\Console\Scheduling\Schedule $schedule */
        $schedule = app(Schedule::class);

        $processRecurrences = config('emailmarketing.local_events.process_recurrences');

        if ($processRecurrences) {
            $schedule
                ->command(ProcessRecurrences::class, [10, 10])
                ->everyFiveMinutes();
        }

        $schedule->command(RemoveOutdatedEvents::class)->daily();
        $schedule->command(RemoveEventsWithNoMarkets::class)->dailyAt("23:30");

        $syncEnabled = config('emailmarketing.local_events.sync_markets');

        if ($syncEnabled) {
            $schedule
                ->command(SyncMarkets::class, [
                    '-S thursday',
                    '-A'
                ])
                ->dailyAt('7:00');

            $schedule
                ->command(SyncMarkets::class, ['-S thursday +2 weeks'])
                ->dailyAt('7:05');
        }
    }

    private function registerMailingContentService() : void
    {
        $this->app->singleton(MailingContentService::class, function () {
            if (Setting::get('blog.enabled', false)) {
                return $this->app->make(BlogMailingContentService::class);
            }

            return $this->app->make(EventMailingContentService::class);
        });
    }

    private function getHosts() : array
    {
        return explode(',', config('scout.elasticsearch.hosts'));
    }
}
