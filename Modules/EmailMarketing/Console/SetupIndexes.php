<?php

namespace Modules\EmailMarketing\Console;

use Elasticsearch\Client;
use Illuminate\Console\Command;

class SetupIndexes extends Command
{
    /** @var string */
    protected $signature = 'email-marketing:setup-indexes {--force} {--import}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set up indexes.';

    /** @var \Elasticsearch\Client */
    private $client;

    public function handle(Client $client)
    {
        $this->client = $client;

        $this->info("Creating indexes...");

        collect($this->getIndexes())
            ->each(function ($settings, $modelClass) use ($client) {
                $index = $this->getIndexName($modelClass);

                $this->createIndex($index, $settings);

                if ($this->option('import')) {
                    $this->call('scout:import', [
                        'model' => $modelClass
                    ]);
                }
            });

        $this->info("\n DONE");
    }

    public function getIndexes() : array
    {
        return [
            \Modules\EmailMarketing\Models\Market::class => [
                "properties" => [
                    "market"         => [
                        "type" => "geo_shape",
                        "strategy" => 'quadtree',
                    ],
                    "address"          => [
                        "type"   => "text",
                        "fields" => [
                            "keyword" => [
                                "type"         => "keyword",
                                "ignore_above" => 256,
                            ],
                        ],
                    ],
                    "name"             => [
                        "type"   => "text",
                        "fields" => [
                            "keyword" => [
                                "type"         => "keyword",
                                "ignore_above" => 256,
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }

    private function getIndexName(string $model)
    {
        /** @var \Laravel\Scout\Searchable $model */
        $model = new $model;

        return $model->searchableAs();
    }

    private function createIndex(string $index, array $settings)
    {
        $indices = $this->client->indices();

        $exists = $indices->exists(['index' => $index]);

        if ($this->option('force') && $exists) {
            $this->info("- $index: deleting");
            $indices->delete(['index' => $index]);
            $this->info("- $index: deleted");
        } elseif ($exists) {
            $this->info("- $index: already exists, skipping!");
            return;
        }

        $this->info("- $index: creating");

        $this->client->indices()->create([
            'index' => $index,
            'body'  => [
                'mappings' => [
                    $index => $settings,
                ],
            ],
        ]);
        $this->info("- $index: created");
    }
}
