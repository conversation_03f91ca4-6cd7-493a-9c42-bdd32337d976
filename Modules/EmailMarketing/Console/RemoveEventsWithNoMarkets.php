<?php

namespace Modules\EmailMarketing\Console;

use Illuminate\Console\Command;
use Modules\EmailMarketing\Models\Event;

class RemoveEventsWithNoMarkets extends Command
{
    /** @var string */
    protected $signature = 'email-marketing:remove-events-with-no-markets';

    /** @var string */
    protected $description = 'Removes events that have no markets associated to them';

    public function handle() : void
    {
        //Delete all events that have no markets attached
        Event::whereDoesntHave('markets')->delete();
    }
}
