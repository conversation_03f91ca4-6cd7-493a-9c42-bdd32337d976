<?php

namespace Modules\EmailMarketing\Console;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Modules\EmailMarketing\Models\Event;

class RemoveOutdatedEvents extends Command
{
    /** @var string */
    protected $signature = 'email-marketing:remove-outdated-events';

    /** @var string */
    protected $description = 'Removes events that are in the past';

    public function handle() : void
    {
        //Get today's date (at midnight)
        $startOfToday = new Carbon('today');

        //Delete all events that have a max end date in the past
        Event::endingNoLaterThan(\Carbon\Carbon::parse($startOfToday))->delete();
    }
}
