<?php

namespace Modules\EmailMarketing\Console;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface;
use Modules\EmailMarketing\Jobs\ProcessRecurrence;
use Modules\EmailMarketing\Jobs\ProcessDisabledRecurrence;
use Modules\EmailMarketing\Models\RecurrenceSettings;

class ProcessRecurrences extends Command
{
    /** @var string */
    protected $signature = 'email-marketing:process-recurrences
                            {sending_before=10 : Minutes before now}
                            {sending_after=10 : Minutes from now}';

    public function handle(RecurrenceSettingsRepositoryInterface $repository)
    {
        // Create the start and end times with with the passed options
        $startAt = Carbon::now()->subMinutes($this->argument('sending_before'));
        $endAt = Carbon::now()->addMinutes($this->argument('sending_after'));

        //get and process enabled recurrences
        $enabledRecurrences = $repository->getEnabledRecurrencesSendingBetween(
            $startAt,
            $endAt
        );

        $this->handleEnabledRecurrences($enabledRecurrences);

        //get and reset disabled recurrences
        $disabledRecurrences = $repository->getDisabledRecurrencesSendingBetween(
            $startAt,
            $endAt
        );

        $this->handleDisabledRecurrences($disabledRecurrences);

        $this->info("\n DONE!");
    }

    protected function handleEnabledRecurrences(Collection $recurrences) : void
    {
        if ($recurrences->isEmpty()) {
            $this->info('No enabled recurrences found');

            return;
        }

        $this->info("Dispatching {$recurrences->count()} jobs..");

        $progress = $this->output->createProgressBar($recurrences->count());

        $recurrences->each(function (RecurrenceSettings $recurrence) use ($progress) {
            $job = new ProcessRecurrence($recurrence);
            $recurrence->accountId()->execute(function () use ($job) {
                dispatch($job);
            });
            $progress->advance();
        });

        $progress->finish();

        $this->info("Done dispatching jobs for enabled recurrences");
    }

    protected function handleDisabledRecurrences(Collection $recurrences) : void
    {
        if ($recurrences->isEmpty()) {
            $this->info('No disabled recurrences found');

            return;
        }

        $this->info("Dispatching {$recurrences->count()} jobs..");

        $progress = $this->output->createProgressBar($recurrences->count());

        $recurrences->each(function (RecurrenceSettings $recurrence) use ($progress) {
            $job = new ProcessDisabledRecurrence($recurrence);

            $recurrence->accountId()->execute(function () use ($job) {
                dispatch($job);
            });

            $progress->advance();
        });

        $this->info("Done dispatching jobs for disabled recurrences");
    }
}
