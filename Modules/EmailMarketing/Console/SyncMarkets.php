<?php

namespace Modules\EmailMarketing\Console;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Modules\EmailMarketing\Jobs\SyncMarkets as SyncMarketsJob;

class SyncMarkets extends Command
{
    /** @var string */
    protected $signature = 'email-marketing:sync-markets
                       {--S|start=now : When to start (can be whatever php can parse)}
                       {--D|days=13 : How far from start, in days, to pull}
                       {--A|analyze-market-quality : Will dispatch the analyze job }';

    /** @var string */
    protected $description = 'Trigger a sync of locations from OG';

    public function handle() : void
    {
        $start = Carbon::parse($this->option('start'));
        $end = $start->copy()->addDays((int)$this->option('days'));
        $analyzeMarketQuality = $this->option('analyze-market-quality') ?? false;

        SyncMarketsJob::dispatch($start, $end, $analyzeMarketQuality);
    }
}
