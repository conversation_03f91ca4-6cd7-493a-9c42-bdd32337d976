<?php

namespace Modules\EmailMarketing\Listeners;

use App\Scopes\BelongsToAccountScope;
use Illuminate\Contracts\Queue\ShouldQueue;
use Modules\EmailMarketing\Domain\LocalEvents\AggregateRoots\RecurrenceAggregateRoot;
use Modules\EmailMarketing\DTO\RecurrenceSettingsDTO;
use Modules\EmailMarketing\DTO\UpdateRecurrenceSettingsDTO;
use Modules\EmailMarketing\Events\BlogContentUpdated;
use Modules\EmailMarketing\Models\RecurrenceSettings;

class UpdateRecurrencesWhenBlogIsEnabledListener implements ShouldQueue
{
    /** @var \Modules\EmailMarketing\Domain\LocalEvents\AggregateRoots\RecurrenceAggregateRoot */
    private $aggregateRoot;

    public function __construct(RecurrenceAggregateRoot $aggregateRoot)
    {
        $this->aggregateRoot = $aggregateRoot;
    }

    public function handle(BlogContentUpdated $event) : void
    {
        // If they didn't enable the settings, as well as tick this box, don't do anything here.
        if (! ($event->enabled && $event->updateRecurrences)) {
            return;
        }

        // when enabled, we need to update existing recurrences to the new default subject/body.
        RecurrenceSettings::query()
            ->withoutGlobalScope(BelongsToAccountScope::class)
            ->get()
            ->each(function (RecurrenceSettings $settings) use ($event) {
                // set the context
                $dto = $this->prepareDto($settings, $event);
                // todo - context was removed from here - do we still need this?
                $this->aggregateRoot
                    ->update($dto)
                    ->persist();
            });
    }

    private function prepareDto(RecurrenceSettings $settings, BlogContentUpdated $event) : UpdateRecurrenceSettingsDTO
    {
        $oldDto = RecurrenceSettingsDTO::fromModel($settings);

        //update the model with the new variable passed (this is done so when we create the DTO, all the attributes are there)
        $settings->fill([
            'mailing_subject' => $event->subject,
            'mailing_body'    => $event->body,
        ]);
        $newDto = RecurrenceSettingsDTO::fromModel($settings);

        return new UpdateRecurrenceSettingsDTO([
            'old' => $oldDto,
            'new' => $newDto,
        ]);
    }
}
