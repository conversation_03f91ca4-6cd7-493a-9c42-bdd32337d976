<?php

namespace Modules\EmailMarketing\Listeners;

use App\Context\AccountId;
use App\Models\Account;
use Domain\LocalEvents\Actions\UpdateAccountMarketToNearest;
use Illuminate\Auth\Events\Login;
use Infrastructure\EventListeners\EventSubscriber;
use App\Events\UserLoggedInFromCrm;
use Throwable;

class IdentifyAccountMarket extends EventSubscriber
{
    /** @var array */
    protected static $events = [
        Login::class,
        UserLoggedInFromCrm::class,
    ];

    /** @var \Domain\LocalEvents\Actions\UpdateAccountMarketToNearest */
    private $action;

    public function __construct(UpdateAccountMarketToNearest $action)
    {
        $this->action = $action;
    }

    public function onLogin(Login $event)
    {
        $this->identifyMarket();
    }

    public function onUserLoggedInFromCrm(UserLoggedInFromCrm $event)
    {
        $this->identifyMarket($event->account);
    }

    private function identifyMarket(?Account $account = null)
    {
        if (! $account) {
            $account = optional(AccountId::current())->account();
        }

        if (! $account) {
            return;
        }

        try {
            $this->action->execute($account);
        } catch (Throwable $e) {
            // do nothing.
        }
    }
}
