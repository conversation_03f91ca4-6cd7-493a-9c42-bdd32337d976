<?php

namespace Modules\EmailMarketing\Listeners;

use App\TeamsNotification\TeamsNotifiable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Infrastructure\EventListeners\EventSubscriber;
use Modules\EmailMarketing\Events\EventParsingFailed;
use Modules\EmailMarketing\Notifications\EventFailedToParseNotification;

class OccasionGeniusSubscriber extends EventSubscriber implements ShouldQueue
{
    protected static $events = [
        EventParsingFailed::class
    ];

    /** @var \App\TeamsNotification\TeamsNotifiable */
    private $notifiable;

    public function __construct(TeamsNotifiable $notifiable)
    {
        $this->notifiable = $notifiable;
    }

    public function onEventParsingFailed(EventParsingFailed $event)
    {
        $this->notifiable->notify(
            new EventFailedToParseNotification($event->event, $event->exception)
        );
    }
}
