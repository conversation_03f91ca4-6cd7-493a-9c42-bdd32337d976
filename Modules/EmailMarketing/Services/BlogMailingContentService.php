<?php

namespace Modules\EmailMarketing\Services;

use Domain\ContactBlock\Repositories\ContactBlockRepository;
use App\Models\ContactBlock;
use Carbon\Carbon;
use Modules\EmailMarketing\Domain\BlogEmail\Entities\BlogContentSettings;
use Modules\EmailMarketing\DTO\MailingContentDTO;

class BlogMailingContentService implements MailingContentService
{
    /** @var ContactBlockRepository */
    private $contactBlockRepository;

    /** @var \Modules\EmailMarketing\Domain\BlogEmail\Entities\BlogContentSettings */
    private $blogContentSettings;

    /** @var ContactBlockParser */
    private $contactBlockParser;

    public function __construct(
        ContactBlockRepository $contactBlockRepository,
        BlogContentSettings $blogContentSettings,
        ContactBlockParser $contactBlockParser
    ) {
        $this->contactBlockRepository = $contactBlockRepository;
        $this->blogContentSettings = $blogContentSettings;
        $this->contactBlockParser = $contactBlockParser;
    }

    public function generate(MailingContentDTO $mailingDto, int $accountId = null, ?bool $preprocessCbImages = false) : array
    {
        //get the contact block
        $contactBlock = $this->contactBlockRepository->findByProduct(ContactBlock::PRODUCT_LOCAL_EVENTS, $accountId);
        $parsedBlock = $this->contactBlockParser->parse($contactBlock, $preprocessCbImages);

        //build the view data
        $viewData = [
            'message'       => $mailingDto->mailing_body,
            'contact_block' => $parsedBlock,
            'copyright'     => "&copy;" . Carbon::now()->year . " ReminderMedia, All&nbsp;Rights&nbsp;Reserved."
        ];

        //render the views and return the data
        return [
            'featured_event'     => [], //empty since there are no events in this context
            'featured_image_url' => $this->blogContentSettings->getFeaturedImage(),
            'mail_content_html'  => view('emails.local-events.blog-html', $viewData)->render(),
            'mail_content_text'  => view('emails.local-events.blog-text', $viewData)->render(),
            'number_of_events'   => 1 //hard coded to 1 since we need this to pass
        ];
    }
}
