<?php

namespace Modules\EmailMarketing\Services;

use App\Models\ContactBlock;
use Carbon\Carbon;
use Domain\ContactBlock\Repositories\ContactBlockRepository;
use DOMDocument;
use DOMXPath;
use Modules\EmailMarketing\DTO\MailingContentDTO;
use Modules\EmailMarketing\Models\Event;
use Modules\EmailMarketing\Models\Market;

class EventMailingContentService implements MailingContentService
{
    /** @var ContactBlockRepository */
    private $contactBlockRepository;

    /** @var ContactBlockParser */
    private $contactBlockParser;

    public function __construct(
        ContactBlockRepository $contactBlockRepository,
        ContactBlockParser $contactBlockParser
    ) {
        $this->contactBlockRepository = $contactBlockRepository;
        $this->contactBlockParser = $contactBlockParser;
    }

    public function generate(MailingContentDTO $mailingDto, int $accountId = null, ?bool $preprocessCbImages = false) : array
    {
        //Check if this is a resend
        if (optional($mailingDto->properties)["manual_resend"]
        && !strpos($mailingDto->mail_content_html, 'Some events in this mailing may have already occurred.')) {
            //Add warning if needed
            return $this->addWarningForPastEvents([
                'mail_content_html'  => $mailingDto->mail_content_html,
                'mail_content_text'  => $mailingDto->mail_content_text,
            ]);
        } elseif (optional($mailingDto->properties)["manual_resend"]) {
            //Otherwise just return the mailing content
            return [
                'mail_content_html'  => $mailingDto->mail_content_html,
                'mail_content_text'  => $mailingDto->mail_content_text,
            ];
        }

        //If not a resend
        //Step 1 : get the market from dto market uuid
        /** @var \Modules\EmailMarketing\Models\Market $market */
        $market = Market::find($mailingDto->market_uuid);

        //Step 2 : get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $market,
            $mailingDto->event_dates_start_at,
            $mailingDto->event_dates_end_at,
            $accountId
        );

        //Step 3 : get the contact block
        $contactBlock = $this->contactBlockRepository->findByProduct(ContactBlock::PRODUCT_LOCAL_EVENTS, $accountId);
        $parsedBlock = $this->contactBlockParser->parse($contactBlock, $preprocessCbImages);

        //Step 4: jam it all together
        $viewData = [
            'heading' => $mailingDto->mailing_heading,
            'message' => $mailingDto->mailing_body,
            'events' => [
                'start_date' => $mailingDto->event_dates_start_at,
                'end_date'   => $mailingDto->event_dates_end_at,
                'featured'   => ($eventsColl->featuredEvent) ? $this->formatEventForDisplay($market, $eventsColl->featuredEvent) : null,
                'other'      => $eventsColl
                    ->events
                    ->untype()
                    ->map(function ($event) use ($market) {
                        return $this->formatEventForDisplay($market, $event);
                    })->toArray()
            ],
            'contact_block' => $parsedBlock,
            'customizations' => ($mailingDto->customizations)
                ? $mailingDto->customizations->toArray()
                : [],
            'copyright'       => "&copy;" . Carbon::now()->year . " ReminderMedia, All&nbsp;Rights&nbsp;Reserved."
        ];

        //render the views and return the data
        return [
            'featured_event' => ($eventsColl->featuredEvent) ? $this->formatFeaturedEventForTracking($eventsColl->featuredEvent) : [],
            'featured_image_url' => $eventsColl->featuredEvent->image_url ?? "",
            'mail_content_html'  => view('emails.local-events.default-html', $viewData)->render(),
            'mail_content_text'  => view('emails.local-events.default-text', $viewData)->render(),
            'number_of_events'   => $eventsColl->events->count() + ($eventsColl->featuredEvent ? 1 : 0)
        ];
    }

    private function formatFeaturedEventForTracking(Event $featuredEvent) : array
    {
        return [
            "event_name"        => $featuredEvent->name,
            "event_uuid"        => $featuredEvent->uuid,
            "event_recurring_uuid" => $featuredEvent->recurring_event_uuid,
            "venue_name"        => $featuredEvent->venue->name,
            "venue_latitude"     => $featuredEvent->venue->latitude,
            "venue_longitude"      => $featuredEvent->venue->longitude
        ];
    }

    private function formatEventForDisplay(?Market $location, Event $event) : array
    {
        //get the start and end date
        //start date - get the earliest start date returned
        $eventStartDate = $event->times->sortBy('starts_at')->first();
        $startDateObj = null;
        if ($eventStartDate) {
            //create a new Carbon object from the date
            $startDateObj = $eventStartDate->starts_at->copy();
            //if a location is passed with a time zone, adjust the timezone to the location
            if ($location && $location->timezone) {
                $startDateObj->setTimezone($location->timezone);
            }
        }

        //end date date - get the last start date returned
        $eventEndDate = $event->times->sortByDesc('ends_at')->first();
        $endDateObj = null;
        if ($eventEndDate) {
            //create a new Carbon object from the date
            $endDateObj = $eventEndDate->ends_at->copy();
            //if a location is passed with a time zone, adjust the timezone to the location
            if ($location && $location->timezone) {
                $endDateObj->setTimezone($location->timezone);
            }
        }

        //return the data
        return [
            "event_name"        => $event->name,
            "event_description" => $event->description,
            "event_image_url"   => $event->image_url,
            "event_url"         => $event->source_url,
            "start_date"        => $startDateObj, //pass back carbon objects, and let the template do the formatting
            "end_date"          => $endDateObj,
            "venue_name"        => strtoupper($event->venue->name),
            "venue_url"         => $event->venue->url,
            "is_in_person"      => $event->hasAllFlags([Event::EVENT_FLAG_IN_PERSON]),
            "is_virtual"        => $event->hasAnyFlags([Event::EVENT_FLAG_LOCAL_VIRTUAL, Event::EVENT_FLAG_GLOBAL_VIRTUAL]),
            "has_covid_restrictions" => $event->hasAllFlags([Event::EVENT_FLAG_COVID_PRECAUTIONS])
        ];
    }

    private function addWarningForPastEvents($mailingData)
    {
        //if the mailing is a resend, we add the a warning for passed events
        libxml_use_internal_errors(true);
        $dom = new DOMDocument();
        $dom->loadHTML($mailingData['mail_content_html']);
        $finder = new DomXPath($dom);
        $classname="inline-font";
        $nodes = $finder->query("//*[contains(concat(' ', normalize-space(@class), ' '), ' $classname ')]");

        $nodeToUpdate = $nodes->item(0)->parentNode;

        $rowNode = $dom->createElement("tr");
        $rowNode->setAttribute('style', "background-color:#eef9ff; text-align:center");
        $cellNode = $dom->createElement("td");
        $pNode = $dom->createElement("p");
        $pNode->setAttribute('style', "font-family: 'Lato','Ubuntu',Helvetica,Arial,sans-serif; margin: 0; font-size: 14px; font-weight: bold; color: #586671;margin: 20px 0 0 0;");
        $pNode->nodeValue = "Some events in this mailing may have already occurred.";
        $cellNode->appendChild($pNode);
        $rowNode->appendChild($cellNode);
        $nodeToUpdate->appendChild($rowNode);

        $mailingData['mail_content_html'] = $dom->saveHTML();
        $mailingData['mail_content_text'] = str_replace(
            'Your Local Events',
            'Some events in this mailing may have already occurred.\n\nYour Local Events',
            $mailingData['mail_content_text']
        );

        return $mailingData;
    }
}
