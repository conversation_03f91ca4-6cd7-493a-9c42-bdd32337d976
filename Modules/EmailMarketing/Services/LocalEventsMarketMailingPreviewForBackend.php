<?php

namespace Modules\EmailMarketing\Services;

use Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface;
use Modules\EmailMarketing\DTO\MailingContentDTO;
use Modules\EmailMarketing\Entities\MarketId;
use Modules\EmailMarketing\Models\Market;
use Modules\EmailMarketing\Models\RecurrenceSettings;

class LocalEventsMarketMailingPreviewForBackend
{
    /** @var \Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface */
    private $recurrenceRepository;

    /** @var \Modules\EmailMarketing\Services\MailingContentService */
    private $mailingContentService;

    /** @var int */
    private $sampleAccountId;

    public function __construct(
        RecurrenceSettingsRepositoryInterface $recurrenceRepository,
        MailingContentService $mailingContentService
    ) {
        $this->recurrenceRepository = $recurrenceRepository;
        $this->mailingContentService = $mailingContentService;
        $this->sampleAccountId = config('app.sample_account_id');
    }

    public function __invoke(Market $market)
    {
        // Step 1: Get the next mailing date for the market
        $nextMailingDate = $this->recurrenceRepository->getNextAvailableMailingDate($market->timezone);

        // Step 2 : get the events date range

        // get the start date
        $startDate = RecurrenceSettings::getEventsStartDate($nextMailingDate, $market->timezone);
        // get the end date
        //for now, we hard code a frequency of 'bi-weekly'
        $endDate = RecurrenceSettings::getEventsEndDate($nextMailingDate, $market->timezone, 'bi-weekly');

        //Step 3 : create a MailingContentDTO object using the information
        $mailingDto = new MailingContentDTO([
            "market_uuid"          => $market->uuid,
            "mailing_subject"      => "Test mailing subject for mailing going out " . $nextMailingDate->format("Y-m-d"),
            "mailing_body"         => "Test mailing body message.",
            "recipient_group_uuids"  => [],
            "event_dates_start_at" => $startDate,
            "event_dates_end_at"   => $endDate,
            "sent_on"              => $nextMailingDate,
        ]);

        //Step 4 : Call the mailing content service to get the mailing content
        $mailContents = $this->mailingContentService->generate($mailingDto, $this->sampleAccountId);

        //Step 5 : get the mailing html content
        //Step 6 : get a default subject - display name and featured event
        $locationId = MarketId::fromString($market->uuid);

        return [
            'subject' => $this->recurrenceRepository->getDefaultMailingSubject($locationId, 'bi-weekly',
                $nextMailingDate, $this->sampleAccountId),
            'html'    => $mailContents['mail_content_html'],
            'text'    => $mailContents['mail_content_text']
        ];
    }
}
