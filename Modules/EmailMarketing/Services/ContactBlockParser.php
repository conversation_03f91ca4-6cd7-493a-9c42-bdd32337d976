<?php

namespace Modules\EmailMarketing\Services;

use App\Models\ContactBlock;
use App\Models\ContactBlockItem;
use App\Models\Plan;
use Illuminate\Support\Arr;
use Modules\Mailing\Traits\PreprocessesMailingImages;

class ContactBlockParser
{
    use PreprocessesMailingImages;
    /**
     * Function will get the contact block for Local Events for the user, and will parse the items
     * TODO - might need to parse differently depending on how the template is set up
     *
     * @param \App\Models\ContactBlock|null $contactBlock
     *
     * @return array
     */
    public function parse(ContactBlock $contactBlock, ?bool $preprocessCbImages = false): array
    {
        //default return array
        $parsedBlock = [
            'name' => '',
            'subtitle' => '',
            'head_shot' => '',
            'company_logo' => '',
            'industry_logo' => '',
            'disclaimer' => '',
            'items' => [],
            'call_to_action' => [],
            'landing_pages' => [],
            'head_shot_display_as' => [],
        ];

        if ($contactBlock->items->isEmpty()) {
            return $parsedBlock;
        }

        //loop on the items
        $contactBlock->items->each(function (ContactBlockItem $cbItemObj) use (&$parsedBlock, $preprocessCbImages) {
            //convert to array so we can get the item data if necessary
            $cbItem = $cbItemObj->toArray();

            //switch on the item type, and put the data in the correct spot
            switch ($cbItem['item_type']) {
                //LOGOS
                //Head shot
                case ContactBlockItem::ITEM_TYPE_HEADSHOT:
                    $itemData = json_decode($cbItem['custom'], true);
                    $parsedBlock['head_shot'] = $this->getImageUrlForMailing(url(Arr::get($itemData, 'urls.product_url')), $preprocessCbImages);
                    break;

                case ContactBlockItem::ITEM_TYPE_HEADSHOT_DISPLAY_AS:
                    $itemData = json_decode($cbItem['custom'], true);
                    $parsedBlock['head_shot_display_as'] = Arr::get($itemData, 'shape');
                    break;

                //Company Logo
                case ContactBlockItem::ITEM_TYPE_OFFICE_LOGO:
                    $itemData = json_decode($cbItem['custom'], true);
                    $parsedBlock['company_logo'] = $this->getImageUrlForMailing(url(Arr::get($itemData, 'urls.product_url')), $preprocessCbImages);
                    break;

                //Industry Logo
                case ContactBlockItem::ITEM_TYPE_INDUSTRY_LOGO:
                    $itemData = json_decode($cbItem['custom'], true);
                    $parsedBlock['industry_logo'] = url(Arr::get($itemData, 'urls.product_url'));
                    break;

                //NAME
                //Display Name
                case ContactBlockItem::ITEM_TYPE_DISPLAY_NAME:
                    //can include custom
                    $parsedBlock['name'] =
                        ($cbItem['item_id']) ? $cbItem['item_data']['display_name'] : $cbItem['custom'];
                    break;

                case ContactBlockItem::ITEM_TYPE_TEAM:
                case ContactBlockItem::ITEM_TYPE_TEAM_MEMBER:
                    $parsedBlock['name'] = $cbItem['item_data']['name'];
                    break;

                //SUBTITLE
                //Professional Title
                case ContactBlockItem::ITEM_TYPE_PROFESSIONAL_TITLE:
                    //can include custom
                    $parsedBlock['subtitle'] =
                        ($cbItem['item_id']) ? $cbItem['item_data']['data']['professional_title'] : $cbItem['custom'];
                    break;

                //Professional designation
                case ContactBlockItem::ITEM_TYPE_PROFESSIONAL_DESIGNATION:
                    $parsedBlock['subtitle'] = $cbItem['item_data']['data']['professional_designation'];
                    break;

                //License Number
                case ContactBlockItem::ITEM_TYPE_LICENSE_NUMBER:
                    $parsedBlock['subtitle'] = $cbItem['item_data']['data']['license_number'];
                    break;

                //Taglines
                case ContactBlockItem::ITEM_TYPE_TAGLINE:
                    $parsedBlock['subtitle'] = $cbItem['item_data']['data']['tagline'];
                    break;

                //CONTACT INFORMATION
                //Phone Number
                case ContactBlockItem::ITEM_TYPE_PHONE_NUMBER:
                    //get only the digits for number
                    $phoneNumber = preg_replace('/\D/', '', $cbItem['item_data']['number']);
                    //get the extension
                    $extension = $cbItem['item_data']['extension'];

                    //Build the display value
                    $phoneNumberPrefix = '';

                    if (11 == strlen($phoneNumber)) {
                        $phoneNumberPrefix = substr($phoneNumber, 0, 1) . '-';
                        $phoneNumber = substr($phoneNumber, 1);
                    }

                    if (10 == strlen($phoneNumber)) {
                        $phoneNumber = implode('', [
                            '(',
                            substr($phoneNumber, 0, 3),
                            ') ',
                            substr($phoneNumber, 3, 3),
                            '-',
                            substr($phoneNumber, 6),
                        ]);
                    }

                    if ($extension) {
                        $extension = ' x' . $extension;
                    }

                    //add the number
                    $parsedBlock['items'][] = [
                        'type' => 'phone',
                        'data' => $phoneNumberPrefix . $phoneNumber . $extension,
                        'label' => $cbItem['item_data']['label']
                    ];
                    break;

                //Email Address
                case ContactBlockItem::ITEM_TYPE_EMAIL_ADDRESS:
                    $data = '';
                    $parsedBlock['items'][] = [
                        'type' => 'email',
                        'data' => $cbItem['item_data']['email'],
                    ];
                    break;

                //Website
                case ContactBlockItem::ITEM_TYPE_WEBSITE:
                    $data = '';
                    $parsedBlock['items'][] = [
                        'type' => 'website',
                        'data' => $cbItem['item_data']['uri'],
                    ];
                    break;

                //Social Media Account
                case ContactBlockItem::ITEM_TYPE_SOCIAL_MEDIA:
                    $data = '';
                    $parsedBlock['items'][] = [
                        'type' => 'website', //TODO - set as website for now until template is updated
                        'data' => $cbItem['item_data']['value'],
                    ];
                    break;

                //Location
                case ContactBlockItem::ITEM_TYPE_LOCATION:
                    $data = '';
                    $parsedBlock['items'][] = [
                        'type' => 'address',
                        'data' => [
                            "label" => $cbItem['item_data']['label'],
                            "addressee" => $cbItem['item_data']['name'],
                            "address1" => $cbItem['item_data']['address1'],
                            "address2" => $cbItem['item_data']['address2'],
                            "city" => $cbItem['item_data']['city'],
                            "state" => $cbItem['item_data']['state'],
                            "zip" => $cbItem['item_data']['zip'],
                        ],
                    ];
                    break;

                //Disclaimer
                case ContactBlockItem::ITEM_TYPE_DISCLAIMER:
                    $parsedBlock['disclaimer'] =
                        ($cbItem['item_id']) ? $cbItem['item_data']['disclosure'] : $cbItem['custom'];
                    break;

                //Calls To Action
                case ContactBlockItem::ITEM_TYPE_CTA:
                    $parsedItem = $this->parseCallToAction($cbItem);
                    // do not add a CTA if it's disabled
                    if (!$parsedItem['enabled']) {
                        break;
                    }

                    $parsedItem['type'] !== ContactBlockItem::ITEM_TYPE_LANDING_PAGE
                        ? $parsedBlock['call_to_action'][] = $parsedItem
                        : $parsedBlock['landing_pages'][] = $parsedItem;
                    break;
            }
        });

        //return the result
        return $parsedBlock;
    }

    private function parseCallToAction($cbItem): array
    {
        $item = json_decode($cbItem['custom'], true);

        // by default, we use the value and buttonText for the text template
        $formatted_value = null;
        $item['text_template_button_text'] = $item['buttonText'];

        switch ($item['type']) {
            case ContactBlockItem::ITEM_TYPE_PHONE_NUMBER:
                // format the number so it is (###) ###-####
                $formatted_value = preg_replace(
                    '/^(\d{3})(\d{3})(\d{4})$/',
                    '($1) $2-$3',
                    $cbItem['item_data']['number']
                );
                $item['value'] = preg_replace('/\D/', '', $cbItem['item_data']['number']);
                break;
            case ContactBlockItem::ITEM_TYPE_EMAIL_ADDRESS:
                $formatted_value = $cbItem['item_data']['email'];
                $item['value'] = $cbItem['item_data']['email'];
                break;
            case ContactBlockItem::ITEM_TYPE_LANDING_PAGE:
                $item['value'] = $item['longUrl'] . '&pi=' . Plan::PLAN_ID_LOCAL_EVENT;
                break;
            default:
                // No value found, return the item
                return $item;
        }

        // add the formatted value to the text template, if it doesn't already exist
        if (stripos($item['text_template_button_text'], $formatted_value) === false) {
            $item['text_template_button_text'] .= ' : ' . $formatted_value;
        }

        return $item;
    }
}
