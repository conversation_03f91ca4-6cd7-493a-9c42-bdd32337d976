<?php

namespace Modules\EmailMarketing\Services;

use Modules\EmailMarketing\DTO\MailingContentDTO;
use Modules\Mailing\BulkMailing\Contracts\BodyFactory;
use Modules\Mailing\Models\Mailing;

class LocalEventsBodyFactory implements BodyFactory
{
    /** @var \Modules\EmailMarketing\Services\MailingContentService */
    private $bodyService;

    /** @var array */
    private $renderedEmails = [];

    public function __construct(MailingContentService $bodyService)
    {
        $this->bodyService = $bodyService;
    }

    public function renderSubject(Mailing $mailing) : string
    {
        return $mailing->getSubject();
    }

    public function renderHtml(Mailing $mailing) : string
    {
        $results = $this->render($mailing);

        return $results['html'];
    }

    public function renderText(Mailing $mailing) : string
    {
        $results = $this->render($mailing);

        return $results['text'];
    }

    private function render(Mailing $mailing) : array
    {
        if (! array_key_exists($mailing->getId(), $this->renderedEmails)) {
            //generate the rendered email
            $mailingContentDto = MailingContentDTO::fromModel($mailing);
            $mailContents = $this->bodyService->generate(
                $mailingContentDto,
                null,
                true
            );

            //update the mailing with the featured event returned by the body service
            $mailing->setProperties(['featured_event' => $mailContents['featured_event']]);
            $mailing->save();

            //store the rendered email
            $this->renderedEmails[ $mailing->getId() ] = [
                "html" => $mailContents['mail_content_html'],
                "text" => $mailContents['mail_content_text']
            ];
        }

        return $this->renderedEmails[ $mailing->getId() ];
    }
}
