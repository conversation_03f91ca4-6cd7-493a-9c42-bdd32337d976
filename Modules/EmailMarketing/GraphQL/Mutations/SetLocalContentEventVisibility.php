<?php

namespace Modules\EmailMarketing\GraphQL\Mutations;

use App\Http\GraphQL\Support\Context;
use GraphQL\Type\Definition\ResolveInfo;
use Modules\EmailMarketing\Models\Event;

class SetLocalContentEventVisibility
{
    public function resolve($rootValue, array $args, Context $context, ResolveInfo $resolveInfo)
    {
        /** @var \Modules\EmailMarketing\Models\Event $event */
        $event = Event::findOrFail($args['uuid']);

        $event->update([
            'is_hidden' => $args['is_hidden'],
        ]);

        return $event->refresh();
    }
}
