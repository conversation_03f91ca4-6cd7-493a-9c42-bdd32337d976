<?php

namespace Modules\EmailMarketing\GraphQL\Mutations;

use App\Models\Plan;
use Illuminate\Support\Arr;
use Infrastructure\Repositories\Titan\TitanApiRepository;
use Modules\EmailMarketing\DTO\RecurrenceSettingsDTO;
use Modules\EmailMarketing\DTO\UpdateRecurrenceSettingsDTO;
use Modules\EmailMarketing\Models\RecurrenceSettings;
use Modules\EmailMarketing\Domain\LocalEvents\Actions\AddRecurrenceSettings as AddLocalEventsRecurrenceSettingsAction;
use Modules\EmailMarketing\Domain\LocalEvents\Actions\UpdateRecurrenceSettings as UpdateLocalEventsRecurrenceSettingsAction;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface;
use App\Http\GraphQL\Support\Context;
use GraphQL\Type\Definition\ResolveInfo;

class SaveLocalEventsRecurrenceSettings
{
    /** @var \Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface */
    private $repository;

    /** @var \Modules\EmailMarketing\Domain\LocalEvents\Actions\AddRecurrenceSettings */
    private $addAction;

    /** @var \Modules\EmailMarketing\Domain\LocalEvents\Actions\UpdateRecurrenceSettings */
    private $updateAction;

    /** @var TitanApiRepository */
    private $titanApiRepository;

    public function __construct(
        RecurrenceSettingsRepositoryInterface $repository,
        AddLocalEventsRecurrenceSettingsAction $addAction,
        UpdateLocalEventsRecurrenceSettingsAction $updateAction,
        TitanApiRepository $titanApiRepository
    ) {
        $this->repository = $repository;
        $this->addAction= $addAction;
        $this->updateAction = $updateAction;
        $this->titanApiRepository = $titanApiRepository;
    }

    public function resolve($rootValue, array $args, Context $context, ResolveInfo $resolveInfo)
    {
        //see if we have a recurrence setting for the account
        $recurrenceSettings = $this->repository->getRecurrenceSettingsForAccount();

        //if it exists, we need to update, otherwise, we add
        if ($recurrenceSettings) {
            //create the old dto
            $oldDto = RecurrenceSettingsDTO::fromModel($recurrenceSettings);

            //update the model with the new variable passed (this is done so when we create the DTO, all the attributes are there)
            $recurrenceSettings->fill($args);
            $newDto = RecurrenceSettingsDTO::fromModel($recurrenceSettings);

            //create the update DTO
            $rsUpdateDto = new UpdateRecurrenceSettingsDTO([
                'old' => $oldDto,
                'new' => $newDto,
            ]);

            $returnRecurrence = $this->updateAction->execute($rsUpdateDto);
        } else {

            //create the DTO
            $rsDto = RecurrenceSettingsDTO::fromModel(new RecurrenceSettings($args));

            //execute the action
            $returnRecurrence = $this->addAction->execute($rsDto);
        }

        //Call Titan to get the selected recipient groups
        $titanGroups = $this->titanApiRepository->getRecipientGroups($returnRecurrence->account_id, [
            'productPlanId' => (string)Plan::PLAN_ID_LOCAL_EVENT,
        ]);

        //Map the recipient groups
        $returnRecurrence->recipient_group_ids = collect(Arr::get($titanGroups, 'recipientGroups', []))
            ->pluck('id')
            ->values()
            ->toArray();

        return $returnRecurrence;
    }
}
