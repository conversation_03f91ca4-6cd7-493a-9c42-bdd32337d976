<?php

namespace Modules\EmailMarketing\GraphQL\Mutations;

use Modules\EmailMarketing\Domain\Location\Actions\UpdateLocalContentMarket as UpdateLocalContentMarketAction;
use Modules\EmailMarketing\DTO\LocalContentMarketDTO;
use App\Http\GraphQL\Support\Context;
use GraphQL\Type\Definition\ResolveInfo;

class UpdateLocalContentMarket
{
    /** @var \Modules\EmailMarketing\Domain\Location\Actions\UpdateLocalContentMarket */
    private $action;

    public function __construct(UpdateLocalContentMarketAction $action)
    {
        $this->action = $action;
    }

    public function resolve($rootValue, array $args, Context $context, ResolveInfo $resolveInfo)
    {
        return $this->action->execute(
            new LocalContentMarketDTO([
                'uuid' => $args['uuid'],
                'name' => $args['name'],
                'address' => $args['address'],
                'latitude' => (float)$args['latitude'],
                'longitude' => (float)$args['longitude'],
                'eventRadius' => (int)$args['eventRadius'],
                'suggestedRadius' => (int)$args['suggestedRadius']
            ])
        );
    }
}
