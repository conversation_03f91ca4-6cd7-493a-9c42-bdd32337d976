<?php

namespace Modules\EmailMarketing\GraphQL\Mutations;

use App\Http\GraphQL\Support\Context;
use App\Models\Location;
use GraphQL\Type\Definition\ResolveInfo;
use Modules\EmailMarketing\Domain\Location\Actions\AddLocalContentInterestedCustomer;
use Modules\EmailMarketing\DTO\LocalContentInterestedCustomerDTO;

class CreateLocalContentInterestedCustomer
{
    /** @var \Modules\EmailMarketing\Domain\Location\Actions\AddLocalContentInterestedCustomer */
    private $action;

    public function __construct(AddLocalContentInterestedCustomer $action)
    {
        $this->action = $action;
    }

    public function resolve($rootValue, array $args, Context $context, ResolveInfo $resolveInfo)
    {
        /** @var \App\Models\Location $location */
        $location = Location::query()->primaryOnly()->firstOrFail();

        return [
            'result' => $this->action->execute(
                LocalContentInterestedCustomerDTO::fromLocation($location)
            ),
        ];
    }
}
