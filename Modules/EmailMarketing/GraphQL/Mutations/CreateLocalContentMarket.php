<?php

namespace Modules\EmailMarketing\GraphQL\Mutations;

use Modules\EmailMarketing\Domain\Location\Actions\AddLocalContentMarket as AddLocalContentMarketAction;
use Modules\EmailMarketing\DTO\LocalContentMarketDTO;
use App\Http\GraphQL\Support\Context;
use GraphQL\Type\Definition\ResolveInfo;

class CreateLocalContentMarket
{
    /** @var \Modules\EmailMarketing\Domain\Location\Actions\AddLocalContentMarket */
    private $action;

    public function __construct(AddLocalContentMarketAction $action)
    {
        $this->action = $action;
    }

    public function resolve($rootValue, array $args, Context $context, ResolveInfo $resolveInfo)
    {
        return $this->action->execute(
            new LocalContentMarketDTO([
                'uuid' => $args['uuid'] ?? '', //an empty input in the UI might send this as null
                'name' => $args['name'],
                'address' => $args['address'],
                'latitude' => (float)$args['latitude'],
                'longitude' => (float)$args['longitude'],
                'eventRadius' => (int)$args['eventRadius'],
                'suggestedRadius' => (int)$args['suggestedRadius']
            ])
        );
    }
}
