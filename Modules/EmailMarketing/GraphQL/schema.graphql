##
type LocalContentMarket {
    uuid: ID!
    name: String!
    address: String!
    latitude: Float!
    longitude: Float!
    event_radius: Int!
    suggested_radius: Int!
    events: [LocalContentEvent] @belongsToMany
    filteredEvents: [LocalContentEvent] @belongsToMany
    enrolled_accounts_count: Int! @count(relation: "enrolledAccounts")
    event_count: Int! @count(relation: "events")
    filtered_event_count: Int! @count(relation: "filteredEvents")
    created_at: DateTime!
    updated_at: DateTime!
}

type LocalContentEvent {
    uuid: ID!
    name: String!
    description: String!
    image_url: String!
    source_url: String
    flags: [String]
    popularity: Int!
    is_hidden: Boolean!
    times: [LocalContentEventDate]! @hasMany
    venue: LocalContentEventVenue
    created_at: DateTime!
    updated_at: DateTime!
}

type LocalContentEventDate {
    uuid: ID!
    event: LocalContentEvent!
    starts_at: DateTime!
    ends_at: DateTime!
    ticket_url: String
}

type LocalContentEventVenue {
    name: String
    line1: String
    line2: String
    city: String
    region: String
    postcode: String
    country: String
    phone: String
    url: String
    latitude: Float
    longitude: Float
    distance: Float
}

type LocalEventNextRecurrenceCustomColors {
    background : String
    main_heading : String
    article_heading: String
    button: String
}

type LocalEventNextRecurrenceMailingCustomizations {
    background_image : String
    color_theme : String
    custom_colors : LocalEventNextRecurrenceCustomColors
}

type LocalEventsRecurrenceSettings {
    id : ID
    frequency : String
    email_from_id : Int
    mailing_date: DateTime
    mailing_subject : String
    persist_subject : Boolean
    mailing_heading : String
    persist_heading : Boolean
    mailing_body : String
    recipient_group_ids : [String]
    is_enabled : Boolean
    tododo: String
    mailing_customizations : LocalEventNextRecurrenceMailingCustomizations
    failed: Boolean
}

type LocalEventEvent {
    uuid : ID!
    name : String!
    description : String!
    image_url : String!
    source_url : String!
}

type LocalEventNextFeaturedEvent {
    event : LocalEventEvent
    default_subject : String
}

type MailingRollupDeliveryCounts {
    attempt : Int
    success : Int
    bounce : Int
}

type MailingRollupUniqueCounts {
    open : Int
    click : Int
    unsubscribe : Int
    spam : Int
}

type MailingRollup {
    delivery : MailingRollupDeliveryCounts
    unique : MailingRollupUniqueCounts
}

input LocalEventNextRecurrenceCustomColorsInput {
    background : String
    main_heading : String
    article_heading: String
    button: String
}

input LocalEventNextRecurrenceMailingCustomizationsInput {
    background_image : String
    color_theme : String
    custom_colors : LocalEventNextRecurrenceCustomColorsInput
}

## aggregation
extend type Query @middleware(checks: ["scope:*"]) {
    localContentMarkets(query: String @search, orderBy: [OrderByClause!] @orderBy): [LocalContentMarket!]! @paginate(
        type: "paginator"
        model: "Modules\\EmailMarketing\\Models\\Market"
        defaultCount: 25
    ) @can(ability: "local-content.markets.index", model: "Modules\\EmailMarketing\\Models\\Market")

    localContentEvents(market: ID, query: String @fuzzysearch, orderBy: [OrderByClause!] @orderBy): [LocalContentEvent]! @paginate(
        type: "paginator"
        builder: "Modules\\EmailMarketing\\GraphQL\\Queries\\LocalEventsPaginatedBuilder@resolve",
        defaultCount: 25
        scopes: ["market"]
    ) @can(ability: "local-content.events.index", model: "Modules\\EmailMarketing\\Models\\Event")

    localContentMarket(uuid: ID @eq): LocalContentMarket
        @find(model: "Modules\\EmailMarketing\\Models\\Market")

    localEventsRecurrenceSettings : LocalEventsRecurrenceSettings
        @field(resolver: "Modules\\EmailMarketing\\GraphQL\\Queries\\LocalEventsRecurrenceSettings@resolve")

    nextFeaturedEvent(market_uuid: ID!, mailing_date: DateTime!, frequency: String!) : LocalEventNextFeaturedEvent
        @field(resolver: "Modules\\EmailMarketing\\GraphQL\\Queries\\FeaturedEventForMarket@resolve")
}

extend type Mutation @middleware(checks: ["scope:*"]) {
    createLocalContentMarket(
        uuid: ID @rules(
            apply: [
                "nullable",
                "string",
                "uuid",
                "unique:local_event_markets,uuid"
            ],
            messages: {
                uuid: "You must provide a valid UUID.",
            }
        ),
        name: String! @rules(apply: ["required", "string"]),
        address: String! @rules(apply: ["required", "string"]),
        latitude: Float! @rules(apply: ["required", "numeric"]),
        longitude: Float! @rules(apply: ["required", "numeric"]),
        eventRadius: Int! @rules(apply: ["required", "numeric"]),
        suggestedRadius: Int! @rules(apply: ["required", "numeric"])
    ): LocalContentMarket
        @can(ability: "local-content.markets.store", model: "Modules\\EmailMarketing\\Models\\Market")

    updateLocalContentMarket(
        uuid: ID! @rules(
            apply: [
                "required",
                "string",
                "uuid",
                "exists:local_event_markets,uuid"
            ],
            messages: {
                uuid: "You must provide a valid UUID.",
            }
        ),
        name: String! @rules(apply: ["required", "string"]),
        address: String! @rules(apply: ["required", "string"]),
        latitude: Float! @rules(apply: ["required", "numeric"]),
        longitude: Float! @rules(apply: ["required", "numeric"]),
        eventRadius: Int! @rules(apply: ["required", "numeric"]),
        suggestedRadius: Int! @rules(apply: ["required", "numeric"])
    ): LocalContentMarket
        @can(ability: "local-content.markets.update", model: "Modules\\EmailMarketing\\Models\\Market")

    deleteLocalContentMarket(uuid: ID!): Result @deleteAction(
        model: "Modules\\EmailMarketing\\Models\\Market"
        action: "Modules\\EmailMarketing\\Domain\\Location\\Actions\\DeleteLocalContentMarket"
    )
        @can(ability: "local-content.markets.destroy", model: "Modules\\EmailMarketing\\Models\\Market")

    createLocalContentInterestedCustomer: Result

    saveLocalEventsRecurrenceSettings(
        frequency: String,
        email_from_id: ID @rules(apply: [
            "exists:email_addresses,id",
            "email_value_for_id_is_valid"
        ] ),
        mailing_date : DateTime,
        mailing_subject : String,
        persist_subject : Boolean,
        mailing_heading : String,
        persist_heading : Boolean,
        mailing_body : String,
        recipient_group_ids : [String],
        is_enabled : Boolean,
        failed : Boolean,
        mailing_customizations : LocalEventNextRecurrenceMailingCustomizationsInput
    ) : LocalEventsRecurrenceSettings

    setLocalContentEventVisibility(uuid: ID!, is_hidden: Boolean) : LocalContentEvent
        @can(ability: "local-content.events.update", model: "Modules\\EmailMarketing\\Models\\Event")
}
