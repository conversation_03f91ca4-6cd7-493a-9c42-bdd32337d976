<?php
namespace Modules\EmailMarketing\GraphQL\Queries;

use App\Http\GraphQL\Support\Context;
use GraphQL\Type\Definition\ResolveInfo;
use Modules\EmailMarketing\Models\Event;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class LocalEventsPaginatedBuilder
{
    public function resolve($root, array $args, Context $context, ResolveInfo $resolveInfo) : Builder
    {
        return Event::select(DB::raw('local_event_events.*, MIN(local_event_event_dates.starts_at) as event_starts_at'))
            ->join('local_event_event_dates', 'local_event_event_dates.event_uuid', '=', 'local_event_events.uuid')
            ->groupBy('local_event_events.uuid');
    }

}
