<?php
namespace Modules\EmailMarketing\GraphQL\Queries;

use App\Context\AccountId;
use App\Models\Plan;
use Illuminate\Support\Arr;
use Infrastructure\Repositories\Titan\TitanApiRepository;
use Modules\EmailMarketing\Domain\BlogEmail\Entities\BlogContentSettings;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface;

class LocalEventsRecurrenceSettings
{
    /** @var \Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface */
    private $repository;

    /** @var \Modules\EmailMarketing\Domain\BlogEmail\Entities\BlogContentSettings */
    private $blogContentSettings;

    /** @var TitanApiRepository */
    private $titanApiRepository;

    public function __construct(
        RecurrenceSettingsRepositoryInterface $repository,
        BlogContentSettings $blogContentSettings,
        TitanApiRepository $titanApiRepository
    ) {
        $this->repository = $repository;
        $this->blogContentSettings = $blogContentSettings;
        $this->titanApiRepository = $titanApiRepository;
    }

    public function resolve()
    {
        //Try to get any saved recurrence settings for the account
        $recurrence = $this->repository->getRecurrenceSettingsForAccount();

        //if none exists, return a default set of value
        if (!$recurrence) {
            $recurrence = $this->repository->getDefaultRecurrenceSettingsValues();
        } else {
            $account = AccountId::current()->account();

            //if the current settings are disabled, make sure the mailing_date is returned as the next available mailing
            //this is because re-enabling the recurrence will set the mailing date to the next available date
            //Note: Reset the date first, as it will affect the default mailing subject
            if (!$recurrence->is_enabled) {
                $recurrence->mailing_date = $this->repository->getNextAvailableMailingDate($account->localContentMarket->timezone);
            }

            //check if the next mailing subject is set in the recurrence, and local content is set to use blog email
            if (! $recurrence->mailing_subject && $this->blogContentSettings->isEnabled()) {
                $recurrence->mailing_subject = $this->blogContentSettings->getSubject();
            }

            //check if the next mailing body is not set, and local content is set to use the blog email
            if (! $recurrence->mailing_body && $this->blogContentSettings->isEnabled()) {
                $recurrence->mailing_body = $this->blogContentSettings->getBody();
            }

            //Call Titan to get the selected recipient groups
            $titanGroups = $this->titanApiRepository->getRecipientGroups($recurrence->account_id, [
                'productPlanId' => (string)Plan::PLAN_ID_LOCAL_EVENT,
            ]);

            //Map the recipient groups
            $recurrence->recipient_group_ids = collect(Arr::get($titanGroups, 'recipientGroups', []))
                ->pluck('id')
                ->values()
                ->toArray();
        }

        //return the recurrence
        return $recurrence;
    }
}
