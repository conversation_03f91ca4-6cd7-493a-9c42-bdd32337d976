<?php
namespace Modules\EmailMarketing\GraphQL\Queries;

use App\Http\GraphQL\Support\Context;
use Carbon\Carbon;
use GraphQL\Type\Definition\ResolveInfo;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface;
use Modules\EmailMarketing\Models\Event;
use Modules\EmailMarketing\Models\Market;
use Modules\EmailMarketing\Entities\MarketId;
use Modules\EmailMarketing\Models\RecurrenceSettings;

class FeaturedEventForMarket
{
    /** @var \Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface */
    private $repository;

    public function __construct(RecurrenceSettingsRepositoryInterface $repository)
    {
        $this->repository = $repository;
    }

    public function resolve($rootValue, array $args, Context $context, ResolveInfo $resolveInfo)
    {
        //build the params from the passed arguments
        $market = Market::find($args['market_uuid']);
        $frequency = $args['frequency'];
        $mailingDate = new Carbon($args['mailing_date']);

        //Get the starts on (for now, just start  use the mailing date)
        $startsOn = RecurrenceSettings::getEventsStartDate($mailingDate, $market->timezone);

        //add 2 weeks to get the end date - assumes frequency is always 2 weeks (bi-weekly)
        $endsOn = RecurrenceSettings::getEventsEndDate($mailingDate, $market->timezone, $frequency);

        //get a location id from the market
        $locationId = MarketId::fromString($market->uuid);

        //get the featured event
        $featuredEvent = $this->repository->getFeaturedEventForMarket($locationId, $startsOn, $endsOn);

        //get the default subject
        $defaultSubject = $this->repository->getDefaultMailingSubject($locationId, $frequency, $mailingDate);

        //return the result
        return [
            "event" => $featuredEvent,
            "default_subject" => $defaultSubject
        ];
    }

}
