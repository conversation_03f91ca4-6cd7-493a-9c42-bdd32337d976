<?php

namespace Modules\EmailMarketing\Events;

use Modules\EmailMarketing\OccasionGenius\Entities\Event;
use Throwable;

class EventParsingFailed
{
    /** @var \Modules\EmailMarketing\OccasionGenius\Entities\Event */
    public $event;

    /** @var \Throwable */
    public $exception;

    public function __construct(Event $event, Throwable $exception)
    {
        $this->event = $event;
        $this->exception = $exception;
    }
}
