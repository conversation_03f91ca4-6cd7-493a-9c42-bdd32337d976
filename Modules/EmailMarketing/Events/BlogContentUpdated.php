<?php

namespace Modules\EmailMarketing\Events;

use App\Models\User;
use Illuminate\Support\Facades\Auth;

class BlogContentUpdated
{
    /** @var bool */
    public $enabled;

    /** @var string|null */
    public $featuredImage;

    /** @var string|null */
    public $subject;

    /** @var string|null */
    public $body;

    /** @var \App\Models\User */
    public $user;

    /** @var bool */
    public $updateRecurrences;

    public function __construct(
        bool $enabled,
        bool $updateRecurrences = false,
        ?string $featuredImage = null,
        ?string $subject = null,
        ?string $body = null,
        ?User $user = null
    ) {
        $this->enabled = $enabled;
        $this->updateRecurrences = $updateRecurrences;
        $this->featuredImage = $featuredImage;
        $this->subject = $subject;
        $this->body = $body;
        $this->user = $user ?? Auth::user();
    }
}
