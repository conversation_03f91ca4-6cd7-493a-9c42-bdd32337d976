<?php

namespace Modules\EmailMarketing\Repositories;

use Carbon\Carbon;
use Exception;
use Illuminate\Support\Str;
use Infrastructure\Support\LazyCollection;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\Event;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\EventCollection;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\EventDate;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\Venue;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\EventRepositoryInterface;
use Modules\EmailMarketing\Entities\MarketId;
use Modules\EmailMarketing\Events\EventParsingFailed;
use Modules\EmailMarketing\OccasionGenius\ApiClient;
use Modules\EmailMarketing\OccasionGenius\Entities\Event as OccasionGeniusEvent;
use Modules\EmailMarketing\OccasionGenius\Entities\Venue as OccasionGeniusVenue;
use Mo<PERSON><PERSON>\EmailMarketing\OccasionGenius\Enums\VirtualRule;
use Ramsey\Uuid\Uuid;
use RRule\RRule;
use Throwable;

class OccasionGeniusEventRepository implements EventRepositoryInterface
{
    /** @var \Modules\EmailMarketing\OccasionGenius\ApiClient */
    private $api;

    public function __construct(ApiClient $api)
    {
        $this->api = $api;
    }

    public function getForLocation(
        MarketId $location,
        ?int $distance = null,
        ?Carbon $startOn = null,
        ?Carbon $endOn = null
    ) : EventCollection {
        return $this->mapToEventCollection(
            $this->api->getAllEventsForLocation($location, $distance, $startOn, $endOn)
        );
    }

    protected function mapToEventCollection(LazyCollection $collection) : EventCollection
    {
        return new EventCollection(
            $collection->map(function (OccasionGeniusEvent $event) {
                try {
                    return new Event([
                        'uuid'        => Uuid::fromString($event->uuid),
                        'recurring_event_uuid' => empty($event->recurring_event_uuid) ? null : $event->recurring_event_uuid,
                        'name'        => $event->name,
                        'description' => $event->description,
                        'dates'       => $this->convertDates($event),
                        'venue'       => $this->convertVenue($event->venue),
                        'popularity'  => $event->popularity_score,
                        'flags'       => $event->flags,
                        'imageUrl'    => $event->image_url,
                        'url'         => $event->source_url,
                        'cancelled'   => $event->cancelled,
                        'isVirtual'   => VirtualRule::isVirtual($event),
                        'isInPerson'  => VirtualRule::isInPerson($event)
                    ]);
                } catch (Throwable $e) {
                    event(new EventParsingFailed($event, $e));
                }
            })
        );
    }

    private function convertDates(OccasionGeniusEvent $event) : array
    {
        //check if there is an instance_date
        if ($event->instance_date) {
            $instanceDate = Carbon::parse($event->instance_date);

            $eventDate = new EventDate([
                'startsAt' => $instanceDate->copy()->startOfDay(),
                'endsAt'   => $instanceDate->copy()->endOfDay(),
                'url'      => $event->ticket_url,
            ]);
        } else {
            //simple create the event date
            $eventDate = new EventDate([
                'startsAt' => Carbon::parse($event->start_date),
                'endsAt'   => Carbon::parse($event->end_date),
                'url'      => $event->ticket_url,
            ]);
        }

        //return the event date in an array
        return [$eventDate];
    }

    /**
     * @deprecated We are now using the instance_date field, and no longer calculate recurring dates
     * @param \Modules\EmailMarketing\OccasionGenius\Entities\Event $event
     * @return array
     */
    private function convertRecurringDates(OccasionGeniusEvent $event) : array
    {
        //try the rrule first
        try {
            $dates = $this->getDatesFromRRule($event);
        } catch (Exception $e) {
            $dates = [];
        }

        //If there are no dates returned, call the second function
        if (empty($dates)) {
            $dates = $this->getDatesFromEventParams($event);
        }

        //return the dates
        return $dates;
    }

    /**
     * @deprecated We are now using the instance_date field, and no longer calculate from RRULE
     * @param \Modules\EmailMarketing\OccasionGenius\Entities\Event $event
     * @return array
     */
    private function getDatesFromRRule(OccasionGeniusEvent $event): array
    {
        //get the first occurrence start date and time
        $firstStartDateTime = Carbon::parse($event->start_date);

        //get the last occurence start date and time
        $lastEndDateTime = Carbon::parse($event->end_date);

        //from here, calculate the end time of the first occurrence
        $firstEndDateTime = $firstStartDateTime->copy()->setTimeFromTimeString($lastEndDateTime->toTimeString());

        //if the end time if before the start time (meaning the time was after midnight, we should add a day);
        if ($firstEndDateTime->isBefore($firstStartDateTime)) {
            $firstEndDateTime->addDays(1);
        }

        $ruleString = $event->rrule;
        if (! Str::contains($event->rrule, "UNTIL") && ! Str::contains($event->rrule, "COUNT")) {
            $ruleString .= ";UNTIL={$lastEndDateTime->format('Ymd\This')}Z";
        }

        $rRule = new RRule($ruleString, $firstStartDateTime);

        return collect($rRule)->map(function ($dt) use ($event, $firstEndDateTime) {
            $carbonDate = Carbon::make($dt);
            $endDate = $carbonDate
                ->copy()
                ->setTime($firstEndDateTime->hour, $firstEndDateTime->minute, $firstEndDateTime->second);

            return new EventDate([
                'startsAt' => $carbonDate,
                'endsAt'   => $endDate,
                'url'      => $event->ticket_url,
            ]);
        })
            ->toArray();
    }

    /**
     * @deprecated We are now using the instance_date field, and no longer calculate recurring event dates
     * @param \Modules\EmailMarketing\OccasionGenius\Entities\Event $event
     * @return array
     */
    private function getDatesFromEventParams(OccasionGeniusEvent $event) : array
    {
        //assume full day events from start to end dates
        $cursor = Carbon::parse($event->start_date)->startOfDay();
        $limit = Carbon::parse($event->end_date)->endOfDay();

        //loop on the cursor until it passed the limit
        $eventDates = [];
        while ($cursor->isBefore($limit)) {
            //add the event date
            $eventDates[] = new EventDate([
                //parse only the date and time strings, conversion with timezone will happen later
                'startsAt' => Carbon::parse($cursor->copy()->startOfDay()->format('Y-m-d H:i:s')),
                'endsAt' => Carbon::parse($cursor->copy()->endOfDay()->format('Y-m-d H:i:s')),
                'url' => $event->ticket_url,
            ]);

            //increase the cursor
            $cursor->addDay(1);
        }

        //return the dates
        return $eventDates;
    }

    private function convertVenue(OccasionGeniusVenue $venue) : Venue
    {
        return new Venue($venue->toArray());
    }
}
