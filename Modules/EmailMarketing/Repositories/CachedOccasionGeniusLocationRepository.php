<?php

namespace Modules\EmailMarketing\Repositories;

use BadMethodCallException;
use Illuminate\Contracts\Cache\Repository;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\Market;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\MarketCollection;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\LocationRepositoryInterface;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\Geocode;

class CachedOccasionGeniusLocationRepository implements LocationRepositoryInterface
{
    /** @var \Modules\EmailMarketing\Repositories\OccasionGeniusLocationRepository */
    private $repository;

    /** @var \Illuminate\Contracts\Cache\Repository */
    private $cache;

    public function __construct(OccasionGeniusLocationRepository $repository, Repository $cache)
    {
        $this->repository = $repository;
        $this->cache = $cache;
    }

    public function getAll() : MarketCollection
    {
        try {
            $cache = $this->cache->tags(['occasion_genius', 'locations']);
        } catch (BadMethodCallException $e) {
            $cache = $this->cache;
        }

        return $cache->remember("occasion_genius_locations", 60, function () {
            return $this->repository->getAll();
        });
    }

    public function getWithinRange(Geocode $geocode) : MarketCollection
    {
        $locations = $this->getAll();

        return $locations->filter(function (Market $location) use ($geocode) {
            return $location->radius >= $location->getGeocode()->getDistanceFromAnotherGeocode($geocode);
        });
    }
}