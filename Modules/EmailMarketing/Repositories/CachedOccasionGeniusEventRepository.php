<?php

namespace Modules\EmailMarketing\Repositories;

use BadMethodCallException;
use Carbon\Carbon;
use Illuminate\Contracts\Cache\Repository;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\EventCollection;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\EventRepositoryInterface;
use Modules\EmailMarketing\Entities\MarketId;

class CachedOccasionGeniusEventRepository implements EventRepositoryInterface
{
    /** @var \Modules\EmailMarketing\Repositories\OccasionGeniusEventRepository */
    private $repository;

    /** @var \Illuminate\Contracts\Cache\Repository */
    private $cache;

    public function __construct(OccasionGeniusEventRepository $repository, Repository $cache)
    {
        $this->repository = $repository;
        $this->cache = $cache;
    }

    public function getForLocation(
        MarketId $location,
        ?int $distance = null,
        ?Carbon $startOn = null,
        ?Carbon $endOn = null
    ) : EventCollection {

        try {
            $cache = $this->cache->tags(['occasion_genius', 'events']);
        } catch (BadMethodCallException $e) {
            $cache = $this->cache;
        }

        $key = sprintf(
            "occasion_genius_events:%s-%d-%s-%s",
            $location->getValue()->toString(),
            $distance,
            ($startOn ? $startOn->toString() : 'null'),
            ($endOn ? $endOn->toString() : 'null')
        );

        return $cache->remember(
            $key,
            60,
            function () use ($location, $distance, $startOn, $endOn) {
                return $this->repository->getForLocation($location, $distance, $startOn, $endOn);
            });
    }
}
