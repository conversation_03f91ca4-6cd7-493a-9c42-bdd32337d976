<?php

namespace Modules\EmailMarketing\Repositories;

use Modules\EmailMarketing\Domain\LocalEvents\Entities\Market;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\MarketCollection;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\LocationRepositoryInterface;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\Geocode;
use Modules\EmailMarketing\OccasionGenius\ApiClient;
use Modules\EmailMarketing\OccasionGenius\Entities\Location as OccasionGeniusLocation;

class OccasionGeniusLocationRepository implements LocationRepositoryInterface
{
    /** @var \Modules\EmailMarketing\OccasionGenius\ApiClient */
    private $api;

    public function __construct(ApiClient $api)
    {
        $this->api = $api;
    }

    public function getAll() : MarketCollection
    {
        return new MarketCollection(
            $this->api
                ->getLocations()
                ->map(function (OccasionGeniusLocation $location) {
                    return $location->toEntity();
                })
        );
    }

    public function getWithinRange(Geocode $geocode) : MarketCollection
    {
        $locations = $this->getAll();

        return $locations->filter(function (Market $location) use ($geocode) {
            return $location->radius >= $location->getGeocode()->getDistanceFromAnotherGeocode($geocode);
        });
    }
}