<?php

namespace Modules\EmailMarketing\Repositories;

use App\Context\AccountId;
use App\Models\Plan;
use DateTimeZone;
use App\Models\ContactBlock;
use Domain\Mailings\DTO\AppearanceCustomizationsDTO;
use Domain\Mailings\Enums\LocalEventsBackgroundImages;
use Domain\Mailings\Enums\MailingColorThemes;
use Illuminate\Support\Facades\App;
use Infrastructure\Repositories\Titan\TitanApiRepository;
use Modules\EmailMarketing\Services\ContactBlockParser;
use Domain\ContactBlock\Repositories\ContactBlockRepository;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use InvalidArgumentException;
use Modules\EmailMarketing\Domain\BlogEmail\Entities\BlogContentSettings;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface;
use Modules\EmailMarketing\Entities\MarketId;
use Modules\EmailMarketing\Models\Event;
use Modules\EmailMarketing\Models\Market;
use Modules\EmailMarketing\Models\RecurrenceSettings;
use Ramsey\Uuid\Uuid;

class EloquentRecurrenceSettingsRepository implements RecurrenceSettingsRepositoryInterface
{
    /** @var ContactBlockParser */
    private $contactBlockParser;

    /** @var \Domain\ContactBlock\Repositories\ContactBlockRepository */
    private $contactBlockRepository;

    /** @var \Modules\EmailMarketing\Domain\BlogEmail\Entities\BlogContentSettings */
    private $blogContentSettings;

    /** @var TitanApiRepository */
    private $titanApiRepository;

    public function __construct(
        ContactBlockParser $contactBlockParser,
        ContactBlockRepository $contactBlockRepository,
        BlogContentSettings $blogContentSettings,
        TitanApiRepository $titanApiRepository
    ) {
        $this->contactBlockParser = $contactBlockParser;
        $this->contactBlockRepository = $contactBlockRepository;
        $this->blogContentSettings = $blogContentSettings;
        $this->titanApiRepository = $titanApiRepository;
    }

    public function getRecurrenceSettingsForAccount() : ?RecurrenceSettings
    {
        //the model should already belong to account, so we get the first one
        return RecurrenceSettings::first();
    }

    public function getDefaultRecurrenceSettingsValues() : ?RecurrenceSettings
    {
        $recurrenceSettings = null;

        //get the account from the context
        // todo - refactor this so we don't need to use the Account object directly
        /** @var \App\Models\Account $account */
        $account = AccountId::current()->account();

        //with the account, and the location, we can calculate start times, and default title
        if (! $account->localContentMarket) {
            return $recurrenceSettings;
        }

        //FREQUENCY
        $frequency = "bi-weekly"; //default for now

        //MAILING DATE
        $mailingDate = $this->getNextAvailableMailingDate($account->localContentMarket->timezone);

        //NEXT MAILING SUBJECT
        //get the featured event for the mailing period
        $locationId = MarketId::fromString($account->localContentMarket->uuid);
        $defaultMailingSubject = $this->getDefaultMailingSubject($locationId, $frequency, $mailingDate);

        //NEXT MAILING BODY
        $mailingBody = $this->blogContentSettings->isEnabled()
            ? $this->blogContentSettings->getBody()
            : null;

        //create a new RecurrenceSettings object with some default values
        $recurrenceSettings = new RecurrenceSettings([
            'frequency'              => $frequency,
            'email_from_id'          => $account->primary_email_address->id ?? null,
            'mailing_date'           => $mailingDate,
            'mailing_subject'        => $defaultMailingSubject,
            'mailing_body'           => $mailingBody,
            'mailing_customizations' => [
                'background_image' => LocalEventsBackgroundImages::BG_IMAGE_INDEX_URBAN,
                'color_theme'      => MailingColorThemes::COLOR_THEME_INDEX_DEFAULT
            ]
        ]);

        //return the object
        return $recurrenceSettings;
    }

    public function addRecurrenceSettings(
        string $frequency,
        int $emailFromId,
        Carbon $mailingDate,
        ?string $mailingSubject,
        bool $persistSubject,
        ?string $mailingHeading,
        bool $persistHeading,
        ?string $mailingBody,
        array $recipientGroupIds,
        bool $isEnabled,
        bool $failed,
        AppearanceCustomizationsDTO $mailingCustomizations
    ) : RecurrenceSettings
    {
        $recurrence = RecurrenceSettings::create([
            'frequency'           => $frequency,
            'email_from_id'       => $emailFromId,
            'mailing_date'        => $mailingDate,
            'mailing_subject'     => $mailingSubject,
            'persist_subject'  => $persistSubject,
            'mailing_heading'     => $mailingHeading,
            'persist_heading'  => $persistHeading,
            'mailing_body'        => $mailingBody,
            'is_enabled'          => $isEnabled,
            'failed'              => $failed,
            'recipient_group_ids' => [], //TODO - temporary until we remove the column
            'mailing_customizations' => $mailingCustomizations->toArray()
        ]);

        //Call Titan to associate groups
        $this->titanApiRepository->updateDigitalProductAssociations(
            $recurrence->account_id,
            Plan::PLAN_ID_LOCAL_EVENT,
            $recipientGroupIds
        );

        return $recurrence;
    }

    public function updateRecurrenceSettings(
        int $id,
        string $frequency,
        int $emailFromId,
        Carbon $mailingDate,
        ?string $mailingSubject,
        bool $persistSubject,
        ?string $mailingHeading,
        bool $persistHeading,
        ?string $mailingBody,
        array $recipientGroupIds,
        bool $isEnabled,
        bool $failed,
        AppearanceCustomizationsDTO $mailingCustomizations
    ) : RecurrenceSettings
    {
        $recurrenceSettings = RecurrenceSettings::find($id);

        if (! $recurrenceSettings) {
            throw new InvalidArgumentException("No recurrence settings by that id found");
        }

        $recurrenceSettings->update([
            'frequency'           => $frequency,
            'email_from_id'       => $emailFromId,
            'mailing_subject'     => $mailingSubject,
            'persist_subject'  => $persistSubject,
            'mailing_heading'     => $mailingHeading,
            'persist_heading'  => $persistHeading,
            'mailing_date'        => $mailingDate,
            'mailing_body'        => $mailingBody,
            'is_enabled'          => $isEnabled,
            'failed'              => $failed,
            'recipient_group_ids' => [], //TODO - temporary until we remove the column
            'mailing_customizations' => $mailingCustomizations->toArray()
        ]);

        //if enabled call titan to update recipient groups
        if ($isEnabled) {
            //Call Titan to associate groups
            $this->titanApiRepository->updateDigitalProductAssociations(
                $recurrenceSettings->account_id,
                Plan::PLAN_ID_LOCAL_EVENT,
                $recipientGroupIds
            );
        }

        return $recurrenceSettings;
    }

    public function getFeaturedEventForMarket(MarketId $locationId, Carbon $startOn, Carbon $endOn) : ?Event
    {
        //default return
        $featuredEvent = null;

        //check if local content is set to use the blog instead of events
        //if so, we need to use the cached subject
        if ($this->blogContentSettings->isEnabled()) {
            return new Event([
                "uuid"        => Uuid::uuid4(),
                "name"        => $this->blogContentSettings->getSubject(),
                "description" => $this->blogContentSettings->getBody(),
                "image_url"   => $this->blogContentSettings->getFeaturedImage(),
                "source_url"  => "",
            ]);
        }

        //otherwise, we search the events for the next featured event
        //get the EventsCollection
        $eventsColl = Event::getForMailing(
            Market::find($locationId->getId()->toString()),
            $startOn,
            $endOn
        );

        return $eventsColl->featuredEvent;
    }

    public function getDefaultMailingSubject(MarketId $locationId, String $frequency, Carbon $nextMailingDate, ?int $accountId = null) : String
    {
        //first things first, check if the local content blog is enabled.
        //if so, we need to use the cached subject
        if ($this->blogContentSettings->isEnabled()) {
            return $this->blogContentSettings->getSubject();
        }

        //start by getting the contact block
        $contactBlock = $this->contactBlockRepository->findByProduct(ContactBlock::PRODUCT_LOCAL_EVENTS, $accountId);

        //get the market
        $market = Market::find($locationId->getValue()->toString());

        //Get the starts on (for now, just start  use the mailing date)
        $startOn = RecurrenceSettings::getEventsStartDate($nextMailingDate, $market->timezone);

        //add 2 weeks to get the end date - assumes frequency is always 2 weeks (bi-weekly)
        $endOn = RecurrenceSettings::getEventsEndDate($nextMailingDate, $market->timezone, $frequency);

        //get the featured event
        $featuredEvent = $this->getFeaturedEventForMarket($locationId, $startOn, $endOn);

        //parse the contact block in the mailing repo
        $parsedContactBlockPayload = $this->contactBlockParser->parse($contactBlock);

        //Begin
        $defaultSubject = "Your local events";

        //if there is a name set in the parsed block, use it
        if ("" != $parsedContactBlockPayload['name']) {
            $defaultSubject = $parsedContactBlockPayload['name'] . " presents your local events";
        }

        //if there is a featured event, use its name in the default subject
        if ($featuredEvent) {
            $defaultSubject = $defaultSubject . ": " . $featuredEvent->name . " and more";
        }

        //return the subject
        return $defaultSubject;
    }

    /**
     * Calculates the next available mailing date for a location based on time zone
     * @param String $locationTimezone
     * @return Carbon
     */
    public function getNextAvailableMailingDate(DateTimeZone $locationTimezone) : Carbon
    {
        //get the current date and time in the correct timezone for the location
        $today = Carbon::now($locationTimezone);

        //if today is thursday, we need to check if it is before 9AM
        $nextMailingDate = null;
        if ($today->isThursday()) {
            $limit = $today->copy()->setTime(9, 0, 0);

            //if today is before the limit, then the limit is the next sending date
            if ($today->isBefore($limit)) {
                $nextMailingDate = $limit->copy();
            }
        }

        //mailing date has not yet been set
        if (!$nextMailingDate) {
            //set it to next thursday local time
            $nextMailingDate =  Carbon::parse('next thursday 9AM', $locationTimezone);
        }

        //reset to the app time zone
        $nextMailingDate->setTimezone(config('app.timezone'));

        //return the date
        return $nextMailingDate;
    }

    public function getEnabledRecurrencesSendingBetween(Carbon $start, Carbon $end) : Collection
    {
        return RecurrenceSettings::allAccounts()
            ->with(['account', 'account.localContentMarket'])
            ->sendingBetween($start, $end)
            ->enabled()
            ->failed(false)
            ->get();
    }

    public function getDisabledRecurrencesSendingBetween(Carbon $start, Carbon $end) : Collection
    {
        return RecurrenceSettings::allAccounts()
            ->with(['account', 'account.localContentMarket'])
            ->sendingBetween($start, $end)
            ->enabled(false)
            ->failed(false)
            ->get();
    }

    public function enableRecurrenceSettings(int $id, Carbon $mailingDate) : bool
    {
        return $this->setRecurrenceSettingsEnabled($id, true, $mailingDate);
    }

    public function disableRecurrenceSettings(int $id) : bool
    {
        return $this->setRecurrenceSettingsEnabled($id, false);
    }

    private function setRecurrenceSettingsEnabled(int $id, $enabled = true, Carbon $mailingDate = null) : bool
    {
        /** @var \Modules\EmailMarketing\Models\RecurrenceSettings|null $recurrenceSettings */
        $recurrenceSettings = RecurrenceSettings::find($id);

        if (! $recurrenceSettings) {
            throw new InvalidArgumentException("No recurrence settings by that id found");
        }

        //If disabled, remove all recipient groups
        if (!$enabled) {
            //Call Titan to associate groups
            $this->titanApiRepository->updateDigitalProductAssociations(
                $recurrenceSettings->account_id,
                Plan::PLAN_ID_LOCAL_EVENT,
                []
            );
        }

        return $recurrenceSettings->update([
            'is_enabled' => $enabled,
            'mailing_date' => $enabled ? $mailingDate : $recurrenceSettings->mailing_date,
        ]);
    }
}
