<?php

namespace Modules\EmailMarketing\Repositories;

use Illuminate\Support\Collection;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\Geocode;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\MarketCollection;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\LocationRepositoryInterface;
use Modules\EmailMarketing\Models\Market;

class EloquentLocationRepository implements LocationRepositoryInterface
{
    public function getAll() : MarketCollection
    {
        return $this->mapToLocalContentMarket(
            Market::get()
        );
    }

    public function getWithinRange(Geocode $geocode) : MarketCollection
    {
        return $this->mapToLocalContentMarket(
            Market::forGeocode($geocode)->get()
        );
    }

    private function mapToLocalContentMarket(Collection $locations) : MarketCollection
    {
        return new MarketCollection($locations);
    }
}
