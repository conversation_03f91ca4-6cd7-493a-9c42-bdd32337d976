<?php

namespace Modules\EmailMarketing\OccasionGenius\Entities;

use Spatie\DataTransferObject\DataTransferObject;

class Event extends DataTransferObject
{
    /** @var string|null */
    public $uuid;

    /** @var string|null */
    public $recurring_event_uuid;

    /** @var string */
    public $name;

    /** @var string */
    public $description;

    /** @var string|null */
    public $summary;

    /** @var array|string[] */
    public $flags;

    /** @var \Modules\EmailMarketing\OccasionGenius\Entities\Venue */
    public $venue;

    /** @var int */
    public $popularity_score;

    /** @var string|null (full url from image) */
    public $image_url;

    /** @var string|null */
    public $category;

    /** @var int|bool|null */
    public $annual;

    /** @var string|null */
    public $source_url;

    /** @var string|null */
    public $updated_at;

    /** @var string|null */
    public $source_date;

    /** @var string|null */
    public $ticket_url;

    /** @var string|null */
    public $virtual_address;

    /** @var string|null */
    public $virtual_rule;

    /** @var string|null */
    public $start_date;

    /** @var string|null */
    public $end_date;

    /** @var array|null */
    public $event_dates;

    /** @var string|null */
    public $rrule;

    /** @var array|null */
    public $cancelled;

    /** @var string|null */
    public $stated_covid_precautions;

    /** @var string|null */
    public $instance_date;
}
