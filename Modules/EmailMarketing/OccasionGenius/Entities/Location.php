<?php

namespace Modules\EmailMarketing\OccasionGenius\Entities;

use DateTimeZone;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\Market as LocationEntity;
use Ramsey\Uuid\Uuid;
use Spatie\DataTransferObject\DataTransferObject;

class Location extends DataTransferObject
{
    /** @var string */
    public $uuid;

    /** @var string */
    public $name;

    /** @var string */
    public $address;

    /** @var float|int */
    public $latitude;

    /** @var float|int */
    public $longitude;

    /** @var int */
    public $radius;

    /** @var DateTimeZone|null */
    public $timezone;

    public function toEntity() : LocationEntity
    {
        return new LocationEntity([
            'uuid' => Uuid::fromString($this->uuid),
            'name' => $this->name,
            'address' => $this->address,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'radius'    => $this->radius,
            'timezone' => $this->timezone
        ]);
    }

    public static function fromApiResponse(array $apiParams) : self
    {
        return new self([
            'uuid'      => $apiParams['uuid'],
            'name'      => $apiParams['name'],
            'address'   => $apiParams['address'],
            'latitude'  => (float)$apiParams['latitude'],
            'longitude' => (float)$apiParams['longitude'],
            'radius'    => (int)$apiParams['radius'],
            'timezone'  => $apiParams['time_zone'] ? new DateTimeZone($apiParams['time_zone']) : null
        ]);
    }
}
