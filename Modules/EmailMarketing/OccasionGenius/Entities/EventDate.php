<?php

namespace Modules\EmailMarketing\OccasionGenius\Entities;

use Carbon\Carbon;
use Spatie\DataTransferObject\DataTransferObject;

class EventDate extends DataTransferObject
{
    /** @var string (uuid) */
    public $instance_id;

    /** @var string (uuid) */
    public $uuid;

    /** @var string (YYYY-MM-DD) */
    public $date;

    /** @var string|null (HH:MM:SS) */
    public $start_time;

    /** @var string|null (HH:MM:SS) */
    public $end_time;

    /** @var string|null */
    public $ticket_url;

    public function getStartDate() : Carbon
    {
        return Carbon::parse(sprintf("%s %s", $this->date, $this->start_time ?? '00:00:00'));
    }

    public function getEndDate() : Carbon
    {
        return Carbon::parse(sprintf("%s %s", $this->date, $this->end_time ?? '23:59:59'));
    }
}
