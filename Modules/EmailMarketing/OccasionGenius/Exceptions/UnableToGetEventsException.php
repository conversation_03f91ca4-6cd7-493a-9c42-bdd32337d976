<?php

namespace Modules\EmailMarketing\OccasionGenius\Exceptions;

use Exception;
use Modules\EmailMarketing\Entities\MarketId;
use RuntimeException;

class UnableToGetEventsException extends RuntimeException
{
    public static function fromMarketAndException(MarketId $marketId, Exception $exception)
    {
        return new self(
            "Unable to get events for Market '{$marketId->getValue()->toString()}'",
            $exception->getCode(),
            $exception
        );
    }
}