<?php

namespace Modules\EmailMarketing\OccasionGenius\Exceptions;

use InvalidArgumentException;
use Modules\EmailMarketing\OccasionGenius\Entities\Event;

class InvalidVirtualRuleProvided extends InvalidArgumentException
{
    public static function withIncorrectValue($value)
    {
        $invalidType = is_scalar($value)
            ? 'scalar'
            : get_class($value);

        return new self(sprintf("Expected a string or %s, got %s", Event::class, $invalidType));
    }
}
