<?php

namespace Modules\EmailMarketing\OccasionGenius\Enums;

use Modules\EmailMarketing\OccasionGenius\Entities\Event;
use Modules\EmailMarketing\OccasionGenius\Exceptions\InvalidVirtualRuleProvided;

class VirtualRule
{
    const NOT_VIRTUAL = "Not Virtual";
    const VIRTUAL_AND_IN_PERSON = "Virtual and In Person";
    const ONLY_VIRTUAL = "100% Virtual";

    const VIRTUAL_RULES = [self::ONLY_VIRTUAL, self::VIRTUAL_AND_IN_PERSON];
    const IN_PERSON_RULES = [self::NOT_VIRTUAL, self::VIRTUAL_AND_IN_PERSON];

    public static function isInPerson($virtualRule) : bool
    {
        if ($virtualRule instanceof Event) {
            $virtualRule = $virtualRule->virtual_rule;
        }

        if (! is_string($virtualRule)) {
            throw InvalidVirtualRuleProvided::withIncorrectValue($virtualRule);
        }

        return in_array($virtualRule, self::IN_PERSON_RULES);
    }

    public static function isVirtual($virtualRule) : bool
    {
        if ($virtualRule instanceof Event) {
            $virtualRule = $virtualRule->virtual_rule;
        }

        if (! is_string($virtualRule)) {
            throw InvalidVirtualRuleProvided::withIncorrectValue($virtualRule);
        }

        return in_array($virtualRule, self::VIRTUAL_RULES);
    }
}
