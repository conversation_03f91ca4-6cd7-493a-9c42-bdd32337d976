<?php

namespace Modules\EmailMarketing\OccasionGenius\Enums;


class CancelledStatus
{
    const CANCELLED = "Cancelled";
    const CHANGED_TO_VIRTUAL = "Changed to Virtual";
    const SOLD_OUT = "Sold Out";
    const POSTPONED_TBD = "Postponed, New Date TBD";
    const POSTPONED_UPDATED = "Postponed, Date Updated";
    const UNKNOWN = "Unknown";
    const EVENT_NOT_FOUND_BROKEN_URL_404 = "Event Not Found/Broken URL/404 Error";
    const DUPLICATE = "Duplicate";
    const EVENT_ENDED = "Event Ended";

    const EXCLUDE_FROM_IMPORT_STATUSES = [
        self::CANCELLED,
        self::POSTPONED_TBD,
        self::EVENT_NOT_FOUND_BROKEN_URL_404,
        self::DUPLICATE,
        self::EVENT_ENDED,
        self::SOLD_OUT,
    ];
}
