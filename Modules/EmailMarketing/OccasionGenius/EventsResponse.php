<?php

namespace Modules\EmailMarketing\OccasionGenius;

use Illuminate\Support\Arr;
use Modules\EmailMarketing\OccasionGenius\Entities\Event;
use Psr\Http\Message\ResponseInterface;

class EventsResponse
{
    /** @var \Illuminate\Support\Collection */
    public $events;

    /** @var int */
    public $total;

    public static function fromResponse(ResponseInterface $response)
    {
        $payload = json_decode($response->getBody(), true);

        $response = new self;

        $response->events = collect(Arr::get($payload, 'results', []))
            ->map(function (array $data) {

                return new Event(Arr::only(
                    $data,
                    [
                        'uuid',
                        'recurring_event_uuid',
                        'name',
                        'description',
                        'summary',
                        'flags',
                        'venue',
                        'popularity_score',
                        'image_url',
                        'category',
                        'annual',
                        'source_url',
                        'updated_at',
                        'source_date',
                        'ticket_url',
                        'virtual_address',
                        'virtual_rule',
                        'start_date',
                        'end_date',
                        'event_dates',
                        'rrule',
                        'cancelled',
                        'stated_covid_precautions',
                        'instance_date'
                    ]
                ));
            });
        $response->total = (int)Arr::get($payload, 'count', 0);

        return $response;
    }
}
