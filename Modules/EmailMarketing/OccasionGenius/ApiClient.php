<?php

namespace Modules\EmailMarketing\OccasionGenius;

use Carbon\Carbon;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Infrastructure\Repositories\RestRepository\RestClient;
use Infrastructure\Support\LazyCollection;
use Modules\EmailMarketing\Entities\MarketId;
use Modules\EmailMarketing\OccasionGenius\Entities\Location;
use Modules\EmailMarketing\OccasionGenius\Exceptions\UnableToGetEventsException;
use Modules\EmailMarketing\OccasionGenius\Exceptions\UnableToGetLocationsException;

class ApiClient
{
    const DEFAULT_LIMIT = 100;

    /** @var \Infrastructure\Repositories\RestRepository\RestClient */
    private $client;

    public function __construct(RestClient $apiClient)
    {
        $this->client = $apiClient;
    }

    public function getLocations() : Collection
    {
        try {
            $response = $this->client->get('api/areas', ['query' => ['limit' => 300]]);
        } catch (ClientException | ServerException $e) {
            Log::critical("Unable to get locations: " . $e->getMessage(), [
                'exception' => get_class($e)
            ]);

            throw UnableToGetLocationsException::fromException($e);
        }

        $data = json_decode($response->getBody(), true);
        return collect(Arr::get($data, 'results', []))->map(function (array $location) {
            return Location::fromApiResponse($location);
        });
    }

    public function getEventsForLocation(
        MarketId $location,
        ?int $distance = null,
        ?Carbon $startOn = null,
        ?Carbon $endOn = null,
        ?int $limit = null,
        int $offset = 0
    ) : EventsResponse {

        return $this->getEventsFromApi(
            $location,
            $distance,
            null,
            $startOn,
            $endOn,
            $limit ?? static::DEFAULT_LIMIT, $offset
        );
    }

    public function getAllEventsForLocation(
        MarketId $location,
        ?int $distance = null,
        ?Carbon $startOn = null,
        ?Carbon $endOn = null
    ) : LazyCollection {
        // need to page through results
        return new LazyCollection(function () use ($location, $distance, $startOn, $endOn) {

            $totalCollected = 0;

            while (true) {

                $events = $this->getEventsFromApi($location, $distance, null, $startOn, $endOn, static::DEFAULT_LIMIT, $totalCollected);

                $total = $events->total;

                $events = $events->events;

                $totalCollected += count($events);

                foreach ($events as $event) {
                    yield $event;
                }

                if ($totalCollected == $total) {
                    break;
                }
            }
        });
    }

    public function getRawEventsFromApi(
        MarketId $marketId,
        ?int $distance,
        ?Carbon $startOn,
        ?Carbon $endOn,
        ?array $flags = null,
        ?int $limit = 256,
        ?int $offset = 0) : array
    {
        $uuid = $marketId->getValue()->toString();

        $query = [
            'area_uuid' => $uuid,
            'limit'         => $limit,
            'offset'        => $offset
        ];

        if ($flags) {
            $query['and_flags'] = implode(',', $flags);
        }

        if ($startOn) {
            $query['start_date'] = $startOn->toDateString();
        }

        if ($endOn) {
            $query['end_date'] = $endOn->toDateString();
        }

        if ($distance) {
            $query['distance'] = $distance;
        }

        try {
            $response = $this->client->get('api/events', ['query' => $query]);
        }
        catch (ClientException | ServerException $e) {
            Log::critical("Unable to get events for market: " . $e->getMessage(), [
                'market'    => $uuid,
                'exception' => get_class($e)
            ]);

            throw UnableToGetEventsException::fromMarketAndException($marketId, $e);
        }

        return json_decode($response->getBody(), true);
    }

    private function getEventsFromApi(
        MarketId $marketId,
        ?int $distance,
        ?array $flags,
        ?Carbon $startOn,
        ?Carbon $endOn,
        ?int $limit = 256,
        ?int $offset = 0) : EventsResponse
    {
        $uuid = $marketId->getValue()->toString();

        $query = [
            'area_uuid' => $uuid,
            'limit'         => $limit,
            'offset'        => $offset
        ];

        if ($flags) {
            $query['and_flags'] = implode(',', $flags);
        }

        if ($startOn) {
            $query['start_date'] = $startOn->toDateString();
        }

        if ($endOn) {
            $query['end_date'] = $endOn->toDateString();
        }

        if ($distance) {
            $query['distance'] = $distance;
        }

        try {
            $response = $this->client->get('api/events', ['query' => $query]);
        }
        catch (ClientException | ServerException $e) {
            Log::critical("Unable to get events for market: " . $e->getMessage(), [
                'market'    => $uuid,
                'exception' => get_class($e)
            ]);

            throw UnableToGetEventsException::fromMarketAndException($marketId, $e);
        }

        return EventsResponse::fromResponse($response);
    }
}
