<?php

namespace Modules\EmailMarketing\Http\Controllers\LocalEvents\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Geocoder\Contracts\CoordinatesRepository;

/**
 * Class SearchZipCodesController
 *
 * Given the passed search string, will search for matching Zip codes
 *
 * @package Modules\EmailMarketing\Http\Controllers\LocalEvents\Api
 */
class SearchZipCodesController extends Controller
{
    /** @var \App\Geocoder\Contracts\CoordinatesRepository */
    private $zipRepo;

    //set up the repositories
    public function __construct(CoordinatesRepository $zipRepo)
    {
        $this->zipRepo = $zipRepo;
    }

    public function __invoke(Request $request)
    {
        //default results are empty
        $results = [];

        //get the zip to search from the request
        $zipParam = $request->get("zip", "");

        //take only the first numeric part of the search param, since we're only searching on Zip code
        $regEx = '/^[0-9]+/';
        preg_match($regEx, $zipParam, $matches);
        $zipToSearch = (count($matches) > 0) ? $matches[0] : "";

        //if the zip is at least 3 character long, do the search
        if (strlen($zipToSearch) >= 3) {
            $results = $this->zipRepo->searchForZip($zipToSearch);
        }

        //return
        return [ "results" => $results ];
    }
}
