<?php

namespace Modules\EmailMarketing\Http\Controllers\LocalEvents\Api;

use App\Context\AccountId;
use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Domain\Mailings\DTO\MailingContentCustomizationsDTO;
use Domain\Mailings\DTO\ReplacementValuesDTO;
use Domain\Mailings\Services\ReplacementValuesService;
use Illuminate\Http\Request;
use Modules\EmailMarketing\DTO\MailingContentDTO;
use Modules\EmailMarketing\Models\RecurrenceSettings;
use Modules\EmailMarketing\Services\MailingContentService;

class FetchCustomizablePreviewContentController extends Controller
{
    /** @var \Modules\EmailMarketing\Services\MailingContentService */
    private $mailingContentService;

    public function __construct(MailingContentService $mailingContentService)
    {
        $this->mailingContentService = $mailingContentService;
    }

    public function __invoke(Request $request)
    {
        return ["html" => $this->generatePreviewHtml($request)];
    }

    private function generatePreviewHtml(Request $request) : string
    {
        $market = AccountId::current()->account()->localContentMarket;

        //if there's no location, return early with an error string
        if (!$market) {
            return "There was an error retrieving the preview";
        }

        //We have a location, start building the preview
        //Step 1 : dates and mailing object
        //get mailing date
        $mailingDate = new Carbon($request->get('mailing_date'));

        //Calculate the start and end dates for the events date range, based on the mailing date
        $startOn = RecurrenceSettings::getEventsStartDate($mailingDate, $market->timezone);
        //add 2 weeks to get the end date - assumes frequency is always 2 weeks (bi-weekly)
        $endOn = RecurrenceSettings::getEventsEndDate($mailingDate, $market->timezone, 'bi-weekly');

        //check the passed heading
        $passedHeading = $request->get('mailing_heading', "");

        //create a mailing DTO object
        $mailingDto = new MailingContentDTO([
            "market_uuid"           => $market->uuid,
            "mailing_heading"       => (empty($passedHeading)) ? null : $passedHeading,
            "mailing_body"          => $request->get('message_body', ''),
            "recipient_group_uuids"   => [],
            "event_dates_start_at"  => $startOn,
            "event_dates_end_at"    => $endOn,
            "sent_on"               => $mailingDate,
        ]);
        $mailingDto->customizations = new MailingContentCustomizationsDTO([
            'background_image' => MailingContentCustomizationsDTO::REPLACE_VALUE_BACKGROUND_IMAGE_URL,
            'background'       => MailingContentCustomizationsDTO::REPLACE_VALUE_BACKGROUND_COLOR,
            'main_heading'     => MailingContentCustomizationsDTO::REPLACE_VALUE_MAIN_HEADING_COLOR,
            'article_heading'  => MailingContentCustomizationsDTO::REPLACE_VALUE_ARTICLE_HEADING_COLOR,
            'button'           => MailingContentCustomizationsDTO::REPLACE_VALUE_BUTTON_COLOR
        ]);

        //Step 2 : Call the mailing content service to get the mailing content
        $mailContents = $this->mailingContentService->generate($mailingDto);

        //Step 3 : return the email body
        //Replace the [recipient_id] and [mailing_id] with 0 found for preview purposes
        return ReplacementValuesService::replaceMailingVariables(
            $mailContents['mail_content_html'],
            new ReplacementValuesDTO([
                'recipientId' => base64_encode(0),
                'mailingId' => base64_encode(0)
            ])
        );
    }

}
