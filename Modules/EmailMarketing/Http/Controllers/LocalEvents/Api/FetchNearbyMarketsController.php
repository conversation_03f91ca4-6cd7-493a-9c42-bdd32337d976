<?php

namespace Modules\EmailMarketing\Http\Controllers\LocalEvents\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\Geocode;
use Modules\EmailMarketing\Models\Market;
use Modules\EmailMarketing\Http\Requests\FetchNearbyMarketsRequest;

/**
 * Class FetchNearbyMarketsController
 * Given latitude and longitude parameters, it will query the existing markets to see how many have a radius
 * that includes the passed coordinates.
 *
 * @package Modules\EmailMarketing\Http\Controllers\LocalEvents\Api
 */
class FetchNearbyMarketsController extends Controller
{
    public function __invoke(FetchNearbyMarketsRequest $request)
    {
        //default results are empty
        $markets = collect([]);

        //get the request params
        $latitude = $request->get("latitude", null);
        $longitude = $request->get("longitude", null);
        $getCount = $request->get("count", false);

        //Make sure the lat and long are set
        if ($latitude && $longitude) {
            //get the geocode using the co-ordinates
            $geocode = new Geocode($latitude, $longitude);

            //get the markets
            $markets = Market::forGeocode($geocode)->get();
        }

        //return depending on the getCount param
        if ($getCount) {
            return [ "count" => $markets->count() ];
        }

        //otherwise, return the markets
        return [ "markets" => $markets ];
    }
}
