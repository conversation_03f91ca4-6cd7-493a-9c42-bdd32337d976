<?php

namespace Modules\EmailMarketing\Http\Controllers\LocalEvents\Api;

use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Modules\EmailMarketing\Jobs\SyncMarkets;

class FireMarketSyncController extends Controller
{
    public function __invoke()
    {
        $dates = collect([
            Carbon::parse('thursday'),
            Carbon::parse('thursday +2 weeks')
        ])->map(function (Carbon $startAt) {
            $dates = [
                'start_at' => $startAt,
                'end_at'   => $startAt->copy()->addDays(13)
            ];

            SyncMarkets::dispatch(...array_values($dates));

            return $dates;
        });

        return ['ranges' => $dates->toArray()];
    }
}
