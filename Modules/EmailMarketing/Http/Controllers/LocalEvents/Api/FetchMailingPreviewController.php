<?php

namespace Modules\EmailMarketing\Http\Controllers\LocalEvents\Api;

use App\Context\AccountId;
use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Domain\Mailings\DTO\MailingContentCustomizationsDTO;
use Domain\Mailings\DTO\ReplacementValuesDTO;
use Domain\Mailings\Enums\LocalEventsBackgroundImages;
use Domain\Mailings\Services\ReplacementValuesService;
use Illuminate\Http\Request;
use Modules\EmailMarketing\DTO\MailingContentDTO;
use Modules\EmailMarketing\Models\Market;
use Modules\EmailMarketing\Models\RecurrenceSettings;
use Modules\EmailMarketing\Services\MailingContentService;
use Modules\Mailing\Models\Mailing;

class FetchMailingPreviewController extends Controller
{
    /** @var \Modules\EmailMarketing\Services\MailingContentService */
    private $mailingContentService;

    public function __construct(MailingContentService $mailingContentService)
    {
        $this->mailingContentService = $mailingContentService;
    }

    public function __invoke(Request $request)
    {
        return ["html" => $this->generatePreviewHtml($request)];
    }

    private function generatePreviewHtml(Request $request) : string
    {
        //If it's a resend we just need to get the data from the original mailing and set the data
        if ($request->has('resend_of')) {
            /** @var Mailing $mailing */
            $mailing = Mailing::find($request->get('resend_of'));
            $mailingDto = MailingContentDTO::fromModel($mailing);
            $mailingDto->properties = array_merge($mailingDto->properties, ['manual_resend' => true]);

        } else {
            $marketUUID = $request->has('market_uuid');

            $market = $marketUUID
                ? Market::find($marketUUID)
                : AccountId::current()->account()->localContentMarket;

            //if there's no location, return early with an error string
            if (!$market) {
                return "There was an error retrieving the preview";
            }

            //We have a location, start building the preview
            //Step 1 : dates and mailing object
            //get mailing date
            $mailingDate = new Carbon($request->get('mailing_date'));

            if ($request->has('sent_on')
                && $request->has('start_on')
                && $request->has('end_on')
                &&  !$request->has('resend')
            ) {
                $startOn = new Carbon($request->get('start_on'));
                $endOn = new Carbon($request->get('end_on'));
            } else {
                //Calculate the start and end dates for the events date range, based on the mailing date
                $startOn = RecurrenceSettings::getEventsStartDate($mailingDate, $market->timezone);
                //add 2 weeks to get the end date - assumes frequency is always 2 weeks (bi-weekly)
                $endOn = RecurrenceSettings::getEventsEndDate($mailingDate, $market->timezone, 'bi-weekly');
            }

            //check the passed heading
            $passedHeading = $request->get('mailing_heading', "");

            //get the mailing customization
            $mailingCustomization = json_decode(
                $request->get('mailing_customizations', '[]'),
                true
            );

            //create a mailing DTO object
            $mailingDto = new MailingContentDTO([
                "market_uuid"           => $market->uuid,
                "mailing_heading"       => (empty($passedHeading)) ? null : $passedHeading,
                "mailing_body"          => $request->get('message_body', ''),
                "recipient_group_uuids"   => [],
                "event_dates_start_at"  => $startOn,
                "event_dates_end_at"    => $endOn,
                "sent_on"               => $mailingDate
            ]);
            $mailingDto->customizations = MailingContentCustomizationsDTO::fromOptionsArray(
                LocalEventsBackgroundImages::class,
                $mailingCustomization
            );
        }

        //Step 2 : Call the mailing content service to get the mailing content
        $mailContents = $this->mailingContentService->generate($mailingDto);

        //Step 3 : return the email body
        //Replace the [recipient_id] and [mailing_id] with the value 0 for preview purposes
        return ReplacementValuesService::replaceMailingVariables(
            $mailContents['mail_content_html'],
            new ReplacementValuesDTO([
                'recipientId' => base64_encode(0),
                'mailingId' => base64_encode(0)
            ])
        );
    }
}
