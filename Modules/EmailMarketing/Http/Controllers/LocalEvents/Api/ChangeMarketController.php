<?php

namespace Modules\EmailMarketing\Http\Controllers\LocalEvents\Api;

use Domain\Account\AccountAggregateRoot;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\EmailMarketing\Entities\GlobalMarketId;
use Modules\EmailMarketing\Entities\MarketId;
use Modules\EmailMarketing\Models\Market;

/**
 * Class ChangeMarketController
 *
 * Api resource controller for Change Market functionlity in Local Events
 *
 * @package Modules\EmailMarketing\Http\Controllers\LocalEvents\Api
 */
class ChangeMarketController extends Controller
{
    public function index(Request $request)
    {
        //get all the markets from the db and return them
        $markets = Market::get();

        return [ "markets" => $markets, "global_market_uuid" => app(GlobalMarketId::class)->getValue() ];
    }

    public function update(AccountAggregateRoot $accountAggregateRoot, Market $changeMarket) {
        //using the passed market, create a MarketId Object
        $marketId = MarketId::fromString($changeMarket->uuid);

        //call the AR function to dispatch the event
        $accountAggregateRoot->updateMarket($marketId);

        //return true
        return [true];
    }
}
