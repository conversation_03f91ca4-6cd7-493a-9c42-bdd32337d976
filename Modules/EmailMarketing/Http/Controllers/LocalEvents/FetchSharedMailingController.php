<?php

namespace Modules\EmailMarketing\Http\Controllers\LocalEvents;

use App\Http\Controllers\Controller;
use App\Models\Plan;
use DOMDocument;
use Illuminate\Http\Response;
use Modules\Mailing\Models\Mailing;

class FetchSharedMailingController extends Controller
{
    public function __invoke()
    {
        //get the mailing from the request
        $mailing = Mailing::where('uuid', request()->route('mailing'))
            ->where('state', 'sent')
            ->first();

        if (! $mailing || !$mailing->getHtmlBody()) {
            return Response::create(["message" => "Mailing Not found"], 404);
        }

        //get a new DOMDocument, and parse the saved HTML content
        $doc = new DOMDocument();
        $doc->loadHTML($mailing->getHtmlBody(), LIBXML_NOERROR);

        //get the head tag, we'll be using it a couple of times
        $head = $doc->getElementsByTagName('head')->item(0);

        //TITLE
        $title = $doc->getElementsByTagName('title')->item(0);
        if ($title) {
            //the title contents is in the first child (DOMText)
            $title->firstChild->data = $mailing->getSubject();
        } else {
            //no title found, append it to the head
            $title = $doc->createElement("title", $mailing->getSubject());
            $head->appendChild($title);
        }

        //META TAGS
        $metaTags = [];

        //og:ttl
        $ogTtl = $doc->createElement("meta");
        $ogTtl->setAttribute("property", "og:ttl");
        $ogTtl->setAttribute("content", "345600");
        $metaTags[] = $ogTtl;

        //og:locale
        $ogLocale = $doc->createElement("meta");
        $ogLocale->setAttribute("property", "og:locale");
        $ogLocale->setAttribute("content", "en_US");
        $metaTags[] = $ogLocale;

        //og:type
        $ogType = $doc->createElement("meta");
        $ogType->setAttribute("property", "og:type");
        $ogType->setAttribute("content", "website");
        $metaTags[] = $ogType;

        //og:site_name
        $siteName = ($mailing->getProduct() == Plan::PLAN_ID_SOCIAL_MEDIA_SHARES) ?
            'Branded Posts' :
            'Local Events';
        $ogSite = $doc->createElement("meta");
        $ogSite->setAttribute("property", "og:site_name");
        $ogSite->setAttribute("content", "ReminderMedia - " . $siteName);
        $metaTags[] = $ogSite;

        //og:url
        $routeName = ($mailing->getProduct() == Plan::PLAN_ID_SOCIAL_MEDIA_SHARES) ?
            'branded-posts.shared-mailing-content' :
            'local-events-mailings.shared-mailing-content';
        $ogUrl = $doc->createElement("meta");
        $ogUrl->setAttribute("property", "og:url");
        $ogUrl->setAttribute("content", route($routeName, [$mailing->uuid]));
        $metaTags[] = $ogUrl;

        //og:title
        $ogTitle = $doc->createElement("meta");
        $ogTitle->setAttribute("property", "og:title");
        $ogTitle->setAttribute("content", $mailing->getSubject());
        $metaTags[] = $ogTitle;

        //og:description
        if ($mailing->getLetter()) {
            $ogDescription = $doc->createElement("meta");
            $ogDescription->setAttribute("property", "og:description");
            $ogDescription->setAttribute("content", $mailing->getLetter());
            $metaTags[] = $ogDescription;
        }

        //og:image
        $ogImage = $doc->createElement("meta");
        $ogImage->setAttribute("property", "og:image");
        $ogImage->setAttribute("content", $mailing->featuredImage->url);
        $metaTags[] = $ogImage;

        //og:image:secure_url
        $ogImageSecUrl = $doc->createElement("meta");
        $ogImageSecUrl->setAttribute("property", "og:image:secure_url");
        $ogImageSecUrl->setAttribute("content", $mailing->featuredImage->url);
        $metaTags[] = $ogImageSecUrl;

        //og:image:alt
        $ogImageAlt = $doc->createElement("meta");
        $ogImageAlt->setAttribute("property", "og:image:alt");
        $ogImageAlt->setAttribute("content", $mailing->getSubject());
        $metaTags[] = $ogImageAlt;

        //twitter:card
        $twitterCard = $doc->createElement("meta");
        $twitterCard->setAttribute("property", "twitter:card");
        $twitterCard->setAttribute("content", "summary_large_image");
        $metaTags[] = $twitterCard;

        //twitter:title
        $twitterTitle = $doc->createElement("meta");
        $twitterTitle->setAttribute("property", "twitter:title");
        $twitterTitle->setAttribute("content", $mailing->getSubject());
        $metaTags[] = $twitterTitle;

        //twitter:description
        if ($mailing->getLetter()) {
            $twitterDescription = $doc->createElement("meta");
            $twitterDescription->setAttribute("property", "twitter:description");
            $twitterDescription->setAttribute("content", $mailing->getLetter());
            $metaTags[] = $twitterDescription;
        }

        //twitter:image
        $twitterImage = $doc->createElement("meta");
        $twitterImage->setAttribute("property", "twitter:image");
        $twitterImage->setAttribute("content", $mailing->featuredImage->url);
        $metaTags[] = $twitterImage;

        //Append the meta tags to the head
        foreach ($metaTags as $mTag) {
            $head->appendChild($mTag);
        }

        //FOOTER
        //get the old footer
        $oldFooter = $doc->getElementById('auto_assign_link_num_17')->parentNode;

        //create a new footer
        $newFooter = $doc->createDocumentFragment();
        $newFooter->appendXML("<div style=\"" . $oldFooter->getAttribute('style') . "\">Powered by <strong style=\"font-family: 'Lato','Ubuntu',Helvetica,Arial,sans-serif; font-weight: 400; letter-spacing: .25px;\">ReminderMedia</strong></div>");

        //replace the footer
        $footerParent = $oldFooter->parentNode;
        $footerParent->replaceChild($newFooter, $oldFooter);

        //OUTPUT
        //return the updated HTML
        return response($doc->saveHTML())
            ->header('Content-Type', 'text/html');
    }
}
