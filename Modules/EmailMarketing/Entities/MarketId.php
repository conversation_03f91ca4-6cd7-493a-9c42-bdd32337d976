<?php

namespace Modules\EmailMarketing\Entities;

use Infrastructure\Contracts\ValueObjectContract;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;

final class MarketId implements ValueObjectContract
{
    /** @var \Ramsey\Uuid\UuidInterface */
    private $id;

    public function __construct(UuidInterface $id)
    {
        $this->id = $id;
    }

    public static function fromString(string $id) : self
    {
        return new self(Uuid::fromString($id));
    }

    public function getId() : UuidInterface
    {
        return $this->id;
    }

    public function getValue() : UuidInterface
    {
        return $this->getId();
    }

    public function sameValueAs(ValueObjectContract $object) : bool
    {
        return ($object instanceof self)
            && $this->getValue()->toString() === $object->getValue()->toString();
    }
}
