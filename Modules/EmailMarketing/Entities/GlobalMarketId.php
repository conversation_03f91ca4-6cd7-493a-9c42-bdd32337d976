<?php

namespace Modules\EmailMarketing\Entities;

use Infrastructure\Contracts\ValueObjectContract;
use Ramsey\Uuid\UuidInterface;

class GlobalMarketId implements ValueObjectContract
{
    /** @var \Modules\EmailMarketing\Entities\MarketId */
    public $marketId;

    // prevent using constructor
    // why? i dunno right now.
    private function __construct()
    {
    }

    public static function fromUuid(UuidInterface $uuid) : GlobalMarketId
    {
        $id = new self;

        $id->marketId = new MarketId($uuid);

        return $id;
    }

    public function getValue()
    {
        return $this->marketId->getValue();
    }

    public function sameValueAs(ValueObjectContract $object) : bool
    {
        return ($object instanceof self)
               && $this->getValue()->equals($object->getValue());
    }
}
