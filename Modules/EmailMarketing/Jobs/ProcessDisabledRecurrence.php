<?php

namespace Modules\EmailMarketing\Jobs;

use App\Context\Jobs\AccountAware;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\EmailMarketing\Jobs\ProcessRecurrence\ResetDisabledRecurrence;
use Modules\EmailMarketing\Models\RecurrenceSettings;

class ProcessDisabledRecurrence implements ShouldQueue, AccountAware
{
    use InteractsWithQueue, Dispatchable, Queueable, SerializesModels;

    /** @var \Modules\EmailMarketing\Models\RecurrenceSettings */
    private $recurrence;

    public function __construct(RecurrenceSettings $recurrence)
    {
        $this->recurrence = $recurrence;
    }

    public function handle()
    {
        app(ResetDisabledRecurrence::class)($this->recurrence);
    }
}
