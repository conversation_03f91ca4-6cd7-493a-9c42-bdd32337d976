<?php

namespace Modules\EmailMarketing\Jobs;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\Market;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\LocationRepositoryInterface;
use Modules\EmailMarketing\Models\Market as EloquentMarket;

class SyncMarkets implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /** @var \Carbon\Carbon */
    private $startAt;

    /** @var \Carbon\Carbon */
    private $endAt;

    /** @var bool */
    private $analyzeMarkets;

    public function __construct(Carbon $startAt, Carbon $endAt, bool $analyzeMarkets = false)
    {
        $this->startAt = $startAt;
        $this->endAt = $endAt;
        $this->analyzeMarkets = $analyzeMarkets;
    }

    public function handle(LocationRepositoryInterface $locationRepository)
    {
        $locations = $locationRepository->getAll();

        $locations->each(function (Market $location) {
            $localLocation = EloquentMarket::createFromEntity($location);

            dispatch(new SyncEventsForMarket(
                $localLocation->local_uuid,
                $this->startAt,
                $this->endAt
            ));
        });

        // Only analyze the markets in specific cases
        if ($this->analyzeMarkets) {
            dispatch(new ConfirmMarkets(
                $this->startAt,
                $this->endAt
            ));
        }
    }
}
