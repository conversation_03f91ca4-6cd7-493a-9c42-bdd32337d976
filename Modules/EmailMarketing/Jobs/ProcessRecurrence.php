<?php

namespace Modules\EmailMarketing\Jobs;

use App\Context\Jobs\AccountAware;
use App\Models\Plan;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Domain\LocalEvents\Exceptions\NoEventsFound;
use Infrastructure\Repositories\Titan\TitanApiRepository;
use Modules\EmailMarketing\Jobs\ProcessRecurrence\CreateMailing;
use Modules\EmailMarketing\Jobs\ProcessRecurrence\MarkRecurrenceAsFailed;
use Modules\EmailMarketing\Jobs\ProcessRecurrence\ResetDisabledRecurrence;
use Modules\EmailMarketing\Jobs\ProcessRecurrence\SetNextRecurrence;
use Modules\EmailMarketing\Models\RecurrenceSettings;

class ProcessRecurrence implements ShouldQueue, AccountAware
{
    use InteractsWithQueue, Dispatchable, Queueable, SerializesModels;

    /** @var \Modules\EmailMarketing\Models\RecurrenceSettings */
    private $recurrence;

    public function __construct(RecurrenceSettings $recurrence)
    {
        $this->recurrence = $recurrence;
    }

    public function handle()
    {
        //get the recipient groups from titan
        $this->getTitanRecipientGroups();

        //if there are recipient groups, process the recurrence
        if (is_array($this->recurrence->recipient_group_ids) && count($this->recurrence->recipient_group_ids) > 0) {
            try {
                app(CreateMailing::class)($this->recurrence);
            } catch (NoEventsFound $exception) {
                // TODO - do something nice here - do we notify the account/user of the mailing
            } finally {
                app(SetNextRecurrence::class)($this->recurrence);
            }
        } else {
            app(ResetDisabledRecurrence::class)($this->recurrence);
        }
    }

    public function failed(Exception $exception)
    {
        //log the exception
        Log::critical("Failed to process Local Events Recurrence: " . $exception->getMessage(), [
                'recurrence_id' => $this->recurrence->id,
            ]);

        // TODO - some sort of alert, maybe? or just let the failed job logic take over.
        //mark the recurrence as failed
        app(MarkRecurrenceAsFailed::class)($this->recurrence);
    }

    private function getTitanRecipientGroups(): void
    {
        //get the recipient groups from titan
        $titanApiRepository = app(TitanApiRepository::class);
        $titanGroups = $titanApiRepository->getRecipientGroups($this->recurrence->account_id, [
            'productPlanId' => (string)Plan::PLAN_ID_LOCAL_EVENT,
        ]);

        //Map the recipient groups and set them to the recurrence
        $this->recurrence->recipient_group_ids = collect(Arr::get($titanGroups, 'recipientGroups', []))
            ->pluck('id')
            ->values()
            ->toArray();
    }
}
