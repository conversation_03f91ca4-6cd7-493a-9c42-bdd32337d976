<?php

namespace Modules\EmailMarketing\Jobs;

use App\ValueObjects\Database\IntegrityConstraint;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\Event;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\EventDate;
use Modules\EmailMarketing\Domain\LocalEvents\Events\FailedToLoadEventsForMarket;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\EventRepositoryInterface;
use Modules\EmailMarketing\Entities\GlobalMarketId;
use Modules\EmailMarketing\Entities\MarketId;
use Modules\EmailMarketing\Models\Event as EloquentEvent;
use Modules\EmailMarketing\Models\Market;
use Modules\EmailMarketing\OccasionGenius\Enums\CancelledStatus;
use PDOException;

class SyncEventsForMarket implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /** @var \Modules\EmailMarketing\Entities\MarketId */
    private $marketId;

    /** @var \Carbon\Carbon */
    private $startOn;

    /** @var \Carbon\Carbon */
    private $endOn;

    /** @var int */
    public $tries = 3;

    public function __construct(MarketId $locationId, Carbon $startOn, Carbon $endOn)
    {
        $this->marketId = $locationId;
        $this->startOn = $startOn;
        $this->endOn = $endOn;
    }

    public function handle(EventRepositoryInterface $eventsRepository, GlobalMarketId $globalMarketId)
    {
        /** @var \Modules\EmailMarketing\Models\Market $market */
        $market = Market::find($this->marketId->getValue()->toString());

        if (! $market) {
            event(FailedToLoadEventsForMarket::withMarketAndReason($this->marketId,
                'Location ID not found in database'));

            return;
        }

        $ogLocationId = $market->external_uuid;

        if (! $ogLocationId) {
            event(FailedToLoadEventsForMarket::withMarketAndReason($this->marketId, 'Does not have external id'));

            return;
        }

        $events = $eventsRepository->getForLocation(
            $ogLocationId,
            $market->event_radius,
            $this->startOn,
            $this->endOn
        );

        //collection to hold the event uuid - pluck and map don't work on the $events collection
        $ogEventUuids = collect();

        //reject cancelled or postponed tbd events, and loop on the rest
        $events->reject(function (Event $event) {
            return collect($event->cancelled)->intersect(collect(CancelledStatus::EXCLUDE_FROM_IMPORT_STATUSES))->count() > 0;
        })
            ->each(function (Event $event) use ($market, $ogEventUuids, $globalMarketId) {
                /** @var \Modules\EmailMarketing\Models\Event $cachedEvent */
                $cachedEvent = EloquentEvent::firstOrNew(['uuid' => $event->uuid]);

                //fill the event with the data to save
                $cachedEvent->fill([
                    'name'        => $event->name,
                    'recurring_event_uuid' => $event->recurring_event_uuid,
                    'description' => $event->description,
                    'image_url'   => $event->imageUrl,
                    'source_url'  => $event->url,
                    'flags'       => $this->buildFlagsForEvent($event, $globalMarketId),
                    'popularity'  => $event->popularity,
                    'venue'       => $event->venue->toArray(),
                ]);

                //check for is_hidden
                $cachedEvent->is_hidden = $this->shouldEventBeHidden($cachedEvent);

                //save the event
                $cachedEvent = $this->persistEvent($cachedEvent);

                //load the markets
                $cachedEvent->load('markets');

                // Update Dates
                $cachedEvent->times()->delete();

                collect($event->dates)->each(function (EventDate $date) use ($cachedEvent, $market) {
                    $cachedEvent->times()->create([
                        'starts_at'  => (new Carbon($date->startsAt, $market->timezone))->timezone('UTC'),
                        'ends_at'    => (new Carbon($date->endsAt, $market->timezone))->timezone('UTC'),
                        'ticket_url' => $date->url,
                    ]);
                });

                $cachedEvent->markets()->syncWithoutDetaching($market);

                //add the event uuid to the collection
                $ogEventUuids->push($event->uuid);
            });

        //Remove any events that were not included in the import for the date range
        EloquentEvent::market($market->uuid)
            ->startingAfter($this->startOn)
            ->startingBefore($this->endOn)
            ->whereNotIn('uuid', $ogEventUuids)
            ->each(function (EloquentEvent $ev) use ($market) {
                $ev->markets()->detach($market);
            });

        // Update the market synchronized_at timestamp
        $market->synchronized_at = Carbon::now();
        $market->save();
    }

    public function failed(Exception $exception)
    {
        Log::critical($exception->getMessage());
    }

    private function shouldEventBeHidden(EloquentEvent $event): bool
    {
        //if the event is new (!exists), and has any of the flags that cause an event to be auto hidden,
        // set the is_hidden field
        $autoHideFlags =
            explode(",", config('emailmarketing.local_events.transports.occasion_genius.hide_events_on_create_flags'));
        if (! $event->exists && count($autoHideFlags) && $event->hasAnyFlags($autoHideFlags)) {
            return true;
        } else {
            //return the current value of is_hidden (null will be returned as false)
            return (bool)$event->is_hidden;
        }
    }

    private function buildFlagsForEvent(Event $event, GlobalMarketId $globalMarketId): array
    {
        $flags = $event->flags ?? [];

        if ($event->isInPerson) {
            $flags[] = EloquentEvent::EVENT_FLAG_IN_PERSON;
        }

        if ($event->isVirtual) {
            $flags[] = $this->marketId->getValue()->equals($globalMarketId->getValue())
                ? EloquentEvent::EVENT_FLAG_GLOBAL_VIRTUAL
                : EloquentEvent::EVENT_FLAG_LOCAL_VIRTUAL;
        }

        //get the cancelled status flags
        $statusFlags = $this->buildFlagsForCancelledStatus($event);

        return array_merge($flags, $statusFlags);
    }

    private function buildFlagsForCancelledStatus(Event $event): array
    {
        //default return array
        $statusFlags = [];

        //loop on the cancelled status
        if ($event->cancelled) {
            foreach ($event->cancelled as $cancelledStatus) {
                switch ($cancelledStatus) {
                    case CancelledStatus::CHANGED_TO_VIRTUAL:
                        $statusFlags[] = EloquentEvent::FLAG_STATUS_CHANGED_TO_VIRTUAL;
                        break;

                    case CancelledStatus::SOLD_OUT:
                        $statusFlags[] = EloquentEvent::FLAG_STATUS_SOLD_OUT;
                        break;

                    case CancelledStatus::POSTPONED_UPDATED:
                        $statusFlags[] = EloquentEvent::FLAG_STATUS_POSTPONED_UPDATED;
                        break;

                    case CancelledStatus::UNKNOWN:
                        $statusFlags[] = EloquentEvent::FLAG_STATUS_UNKNOWN;
                        break;
                }
            }
        }

        //return the flags
        return $statusFlags;
    }

    private function persistEvent(EloquentEvent $cachedEvent): EloquentEvent
    {
        try {
            $cachedEvent->save();
        } catch (QueryException $e) {
            // Even with firstOrCreate, we can occasionally get duplicate entries if there are jobs firing closely
            // to one another.  This is intended to catch that scenario and quietly discard the error.
            $previous = $e->getPrevious();

            if ($previous instanceof PDOException
                && IntegrityConstraint::getErrorCode() == $previous->getErrorCode()
            ) {
                $storedEvent = static::find($cachedEvent->getUuidKey()->toString());

                $storedEvent->fill($cachedEvent->getAttributes());

                $storedEvent->save();

                return $storedEvent;
            }

            throw $e;
        } finally {
            return $cachedEvent;
        }
    }
}
