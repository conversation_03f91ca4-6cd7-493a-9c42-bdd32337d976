<?php

namespace Modules\EmailMarketing\Jobs\ProcessRecurrence;

use Modules\EmailMarketing\Domain\LocalEvents\AggregateRoots\RecurrenceAggregateRoot;
use Modules\EmailMarketing\DTO\RecurrenceSettingsDTO;
use Mo<PERSON>les\EmailMarketing\DTO\UpdateRecurrenceSettingsDTO;
use Modules\EmailMarketing\Models\RecurrenceSettings;

class SetNextRecurrence
{
    /** @var \Modules\EmailMarketing\Domain\LocalEvents\AggregateRoots\RecurrenceAggregateRoot */
    private $recurrenceAggregateRoot;

    public function __construct(RecurrenceAggregateRoot $recurrenceAggregateRoot)
    {
        $this->recurrenceAggregateRoot = $recurrenceAggregateRoot;
    }

    public function __invoke(RecurrenceSettings $recurrence)
    {
        $old = RecurrenceSettingsDTO::fromModel($recurrence);
        $new = clone $old;

        //reset the subject if it is not persistent
        if (!$old->persist_subject) {
            $new->mailing_subject = null;
        }

        //reset the heading if it is not persistent
        if (!$old->persist_heading) {
            $new->mailing_heading = null;
        }

        $new->mailing_body = null;
        $new->mailing_date = $recurrence->getNextMailingDate();

        $this->recurrenceAggregateRoot
            ->update(
                new UpdateRecurrenceSettingsDTO(['old' => $old, 'new' => $new])
            )
            ->persist();
    }
}
