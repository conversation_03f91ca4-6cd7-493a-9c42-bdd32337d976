<?php

namespace Modules\EmailMarketing\Jobs\ProcessRecurrence;

use App\Context\Jobs\AccountAware;
use Modules\EmailMarketing\Domain\LocalEvents\AggregateRoots\RecurrenceAggregateRoot;
use Mo<PERSON>les\EmailMarketing\DTO\RecurrenceSettingsDTO;
use Modules\EmailMarketing\DTO\UpdateRecurrenceSettingsDTO;
use Modules\EmailMarketing\Models\RecurrenceSettings;

class ResetDisabledRecurrence implements AccountAware
{
    /** @var \Modules\EmailMarketing\Domain\LocalEvents\AggregateRoots\RecurrenceAggregateRoot */
    private $recurrenceAggregateRoot;

    public function __construct(RecurrenceAggregateRoot $recurrenceAggregateRoot)
    {
        $this->recurrenceAggregateRoot = $recurrenceAggregateRoot;
    }

    public function __invoke(RecurrenceSettings $recurrence)
    {
        $old = RecurrenceSettingsDTO::fromModel($recurrence);
        $new = clone $old;

        //reset the subject and heading if not persistent, and body
        $new->persist_subject = false;
        $new->mailing_subject = null;

        $new->persist_heading = false;
        $new->mailing_heading = null;

        $new->mailing_body = null;

        //reset the recipient groups
        $new->recipient_group_ids = [];

        //set disabled status to true (just in case as this job is also used to disable a recurrence with no groups)
        $new->is_enabled = false;

        $this->recurrenceAggregateRoot
            ->update(
                new UpdateRecurrenceSettingsDTO(['old' => $old, 'new' => $new])
            )
            ->persist();
    }
}
