<?php

namespace Modules\EmailMarketing\Jobs\ProcessRecurrence;

use App\Models\Plan;
use Domain\LocalEvents\Exceptions\NoEventsFound;
use Domain\Mailings\Enums\LocalEventsBackgroundImages;
use Domain\Mailings\DTO\MailingContentCustomizationsDTO;
use Illuminate\Support\Arr;
use Infrastructure\Repositories\Titan\TitanApiRepository;
use Modules\EmailMarketing\Domain\BlogEmail\Entities\BlogContentSettings;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface;
use Modules\EmailMarketing\DTO\MailingContentDTO;
use Modules\EmailMarketing\Entities\MarketId;
use Modules\EmailMarketing\Models\RecurrenceSettings;
use Modules\EmailMarketing\Services\MailingContentService;
use Modules\Mailing\Contracts\MailingRepository;
use Modules\Mailing\DataTransferObjects\MailingDTO as MailingServiceDTO;
use Ramsey\Uuid\Uuid;

class CreateMailing
{
    /** @var \Modules\Mailing\Contracts\MailingRepository */
    private $mailingRepository;

    /** @var \Modules\EmailMarketing\Services\MailingContentService */
    private $mailingContentService;

    /** @var \Modules\EmailMarketing\Domain\BlogEmail\Entities\BlogContentSettings */
    private $blogContentSettings;

    /** @var TitanApiRepository */
    private $titanApiRepository;

    public function __construct(
        MailingRepository $mailingServiceRepository,
        MailingContentService $mailingContentService,
        BlogContentSettings $blogContentSettings,
        TitanApiRepository $titanApiRepository
    ) {
        $this->mailingRepository = $mailingServiceRepository;
        $this->mailingContentService = $mailingContentService;
        $this->blogContentSettings = $blogContentSettings;
        $this->titanApiRepository = $titanApiRepository;
    }

    public function __invoke(RecurrenceSettings $recurrence)
    {
        //create the MailingContentDTO
        $mailingDTO = $this->makeMailingDTO($recurrence);

        //Get the mailing contents, and update the DTO
        $mailContents = $this->mailingContentService->generate(
            $mailingDTO,
            null,
            true
        );

        //Check if there are events for the mailing
        if ($mailContents['number_of_events'] == 0) {
            throw NoEventsFound::forMarketAndTimeRange($recurrence->account->localContentMarket, $mailingDTO->event_dates_start_at, $mailingDTO->event_dates_end_at);
        }

        //there were events, so we can set the mail contents to the DTO
        $mailingDTO->featured_image_url = $mailContents['featured_image_url'];
        $mailingDTO->mail_content_html = $mailContents['mail_content_html'];
        $mailingDTO->mail_content_text = $mailContents['mail_content_text'];

        //Create the MailingServiceDTO
        $mailingServiceDTO = $this->makeMailingServiceDTO($recurrence, $mailingDTO);

        //Call mailing-service to create the mailing
        $this->mailingRepository->createMailing($mailingServiceDTO);
    }

    private function makeMailingDTO(RecurrenceSettings $recurrence) : MailingContentDTO
    {
        //get the recipient groups from titan
        $titanGroups = $this->titanApiRepository->getRecipientGroups($recurrence->account_id, [
            'productPlanId' => (string)Plan::PLAN_ID_LOCAL_EVENT,
        ]);

        //Map the recipient groups
        $recipientGroupUuids = collect(Arr::get($titanGroups, 'recipientGroups', []))
            ->pluck('id')
            ->values()
            ->toArray();

        $mailingContentDTO = new MailingContentDTO([
            'uuid'                       => Uuid::uuid4(),
            'market_uuid'                => $recurrence->account->localContentMarket->uuid,
            'email_from_address'         => $recurrence->emailFromAddress->getEmail(),
            'mailing_subject'            => $this->getMailingSubject($recurrence),
            'mailing_heading'            => $this->getMailingHeading($recurrence),
            'mailing_body'               => $this->getMailingBody($recurrence),
            'recipient_group_uuids'        => $recipientGroupUuids,
            'event_dates_start_at'       => RecurrenceSettings::getEventsStartDate(
                $recurrence->mailing_date,
                $recurrence->account->localContentMarket->timezone
            ),
            'event_dates_end_at'         => RecurrenceSettings::getEventsEndDate(
                $recurrence->mailing_date,
                $recurrence->account->localContentMarket->timezone,
                $recurrence->frequency
            ),
            'sent_on'                    => $recurrence->mailing_date,
            'featured_image_url'         => "",
            'mail_content_html'          => "",
            'mail_content_text'          => "",
        ]);

        $mailingContentDTO->customizations = MailingContentCustomizationsDTO::fromOptionsArray(
            LocalEventsBackgroundImages::class,
            $recurrence->mailing_customizations
        );

        //return the DTO
        return $mailingContentDTO;
    }

    private function makeMailingServiceDTO(RecurrenceSettings $recurrence, MailingContentDTO $mailingDTO) : MailingServiceDTO
    {
        return new MailingServiceDTO([
            'account_id'          => $recurrence->account->id,
            'product'             => Plan::PLAN_ID_LOCAL_EVENT,
            'uuid'                => $mailingDTO->uuid,
            'book_id'             => null,
            'recipient_group_uuids' => $mailingDTO->recipient_group_uuids,
            'send_at_timestamp'   => (int) $mailingDTO->sent_on->format('U'),
            'subject'             => $mailingDTO->mailing_subject,
            'letter'              => $mailingDTO->mailing_body,
            'from_name'           => $recurrence->account->display_name ?? $recurrence->account->name,
            'reply_email'         => $mailingDTO->email_from_address,
            'featured_image_url'  => $mailingDTO->featured_image_url,
            'html_body'           => $mailingDTO->mail_content_html,
            'text_body'           => $mailingDTO->mail_content_text,
            'properties'          => [
                'market_uuid'              => $mailingDTO->market_uuid,
                'event_dates_start_at'     => $mailingDTO->event_dates_start_at->toDateTimeString(),
                'event_dates_end_at'       => $mailingDTO->event_dates_end_at->toDateTimeString(),
                'recurrence_settings_id'   => $recurrence->id,
                'customizations'           => $recurrence->mailing_customizations ?? []
            ]
        ]);
    }

    private function getMailingSubject(RecurrenceSettings $recurrence) : string
    {
        //use the set subject if it is there
        if ($recurrence->mailing_subject) {
            return $recurrence->mailing_subject;
        }

        //otherwise, check if LC emails are set to use the blog instead of event
        if ($this->blogContentSettings->isEnabled()) {
            return $this->blogContentSettings->getSubject();
        }

        /** @var \Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface $repository */
        $repository = app(RecurrenceSettingsRepositoryInterface::class);

        return $repository->getDefaultMailingSubject(
            MarketId::fromString($recurrence->account->market_uuid),
            $recurrence->frequency,
            $recurrence->mailing_date
        );
    }

    private function getMailingHeading(RecurrenceSettings $recurrence) : string
    {
        //use the set heading if it is there
        if ($recurrence->mailing_heading) {
            return $recurrence->mailing_heading;
        }

        //otherwise, return the market name (default heading)
        return $recurrence->account->localContentMarket->name . " Events";
    }

    private function getMailingBody(RecurrenceSettings $recurrence) : ?string
    {
        //check if LC emails are set to use the blog instead of event
        if ($this->blogContentSettings->isEnabled()) {
            return $this->blogContentSettings->getBody();
        }

        //otherwise, use the body from the recurrence
        return $recurrence->mailing_body;
    }
}
