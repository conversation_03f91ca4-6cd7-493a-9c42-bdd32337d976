<?php

namespace Modules\EmailMarketing\Jobs\ProcessRecurrence;

use Modules\EmailMarketing\Domain\LocalEvents\AggregateRoots\RecurrenceAggregateRoot;
use Modules\EmailMarketing\DTO\RecurrenceSettingsDTO;
use Modules\EmailMarketing\DTO\UpdateRecurrenceSettingsDTO;
use Modules\EmailMarketing\Models\RecurrenceSettings;

class MarkRecurrenceAsFailed
{
    /** @var \Modules\EmailMarketing\Domain\LocalEvents\AggregateRoots\RecurrenceAggregateRoot */
    private $recurrenceAggregateRoot;

    public function __construct(RecurrenceAggregateRoot $recurrenceAggregateRoot)
    {
        $this->recurrenceAggregateRoot = $recurrenceAggregateRoot;
    }

    public function __invoke(RecurrenceSettings $recurrence)
    {
        $old = RecurrenceSettingsDTO::fromModel($recurrence);
        $new = clone $old;

        //mark as failed, and disable the recurrence
        $new->failed = true;
        $new->is_enabled = false;

        $this->recurrenceAggregateRoot
            ->update(
                new UpdateRecurrenceSettingsDTO(['old' => $old, 'new' => $new])
            )
            ->persist();
    }
}
