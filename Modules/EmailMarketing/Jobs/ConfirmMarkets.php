<?php

namespace Modules\EmailMarketing\Jobs;

use Carbon\Carbon;
use Domain\Mailings\Mail\PreviewEmailMarketingMail;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\MarketValidationSettingsRepositoryInterface;
use Modules\EmailMarketing\Models\Market;
use Modules\EmailMarketing\Models\RecurrenceSettings;
use Modules\EmailMarketing\Repositories\EloquentLocationRepository;

class ConfirmMarkets implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 5;

    /**
     * The maximum number of exceptions to allow before failing.
     *
     * @var int
     */
    public $maxExceptions = 2;

    /** @var int */
    public const MARKET_VALIDATION_THRESHOLD = 5;

    /** @var int */
    public const MARKET_VALIDATION_RELEASE_DELAY = 15;

    /** @var Carbon */
    private $startAt;

    /** @var Carbon */
    private $endAt;

    public function __construct(Carbon $startAt, Carbon $endAt)
    {
        $this->startAt = $startAt;
        $this->endAt = $endAt;
    }

    /**
     * @param MarketValidationSettingsRepositoryInterface $settingsRepository
     * @param EloquentLocationRepository $locationRepository
     * @return void
     * @throws \Throwable
     */
    public function handle(MarketValidationSettingsRepositoryInterface $settingsRepository,
                           EloquentLocationRepository $locationRepository
    ) : void
    {
        // Get the list of emails to send
        $marketUpdateEmails = optional($settingsRepository->get())->getMarketUpdateEmailList();

        if (! $marketUpdateEmails) {
            throw new \Exception("Market update email list is empty.");
        }

        // Get all the markets from the eloquent location repository
        $markets = $locationRepository->getAll();

        // If any of the markets were not synchronized today, then release the job back into queue
        $releaseBackIntoQueue = $markets->contains(function (Market $market) {
            return ! Carbon::parse($market->synchronized_at)->isToday();
        });

        // If the flag is true, release the job back into queue
        if ($releaseBackIntoQueue) {
            $this->release(self::MARKET_VALIDATION_RELEASE_DELAY);
            return;
        }

        // Use a collection to store any markets at have fewer events than the threshold
        $marketsBelowThreshold = collect();

        $markets->each(function (Market $market) use ($marketsBelowThreshold) {
                $localStartAt = RecurrenceSettings::getEventsStartDate($this->startAt, $market->timezone);
                $localEndAt = RecurrenceSettings::getLocalEndDate($this->endAt, $market->timezone);

                // Get the filtered events between the start and end dates
                $filteredEvents = $market->filteredEvents()
                    ->startingAfter($localStartAt)
                    ->startingBefore($localEndAt)
                    ->where('is_hidden', '=', false)
                    ->get();

                // Add market to the collection if the event count is less than the threshold
                if ($filteredEvents->count() < self::MARKET_VALIDATION_THRESHOLD) {
                    $marketsBelowThreshold->push([
                        'market'        => $market->name,
                        'event_count'   => $filteredEvents->count(),
                    ]);
                }
                return true;
            });

        // No markets are below threshold, send the validated email to email list
        if ($marketsBelowThreshold->isEmpty()) {
            $this->sendValidatedEmails($marketUpdateEmails);
            return;
        }

        // Some markets are below threshold, send the invalid markets emails
        $this->sendInvalidMarketEmails($marketUpdateEmails, $marketsBelowThreshold);
    }

    /**
     * Send validated markets email
     *
     * @param string $marketUpdateEmails
     * @return void
     * @throws \Throwable
     */
    private function sendValidatedEmails(string $marketUpdateEmails) : void
    {
        //Send the emails
        foreach ($this->splitEmailRecipients($marketUpdateEmails) as $recipientEmailAddress) {
            Mail::send(new PreviewEmailMarketingMail(
                $recipientEmailAddress,
                "Local Event markets sync validated notification",
                view('emails.local-event-market-analysis-valid')->render(),
                view('emails.plain.local-event-market-analysis-valid')->render()
            ));
        }
    }

    /**
     * Send invalid markets email
     *
     * @param string $marketUpdateEmails
     * @param Collection $viewData
     * @return void
     * @throws \Throwable
     */
    private function sendInvalidMarketEmails(string $marketUpdateEmails, Collection $markets) : void
    {
        // Build the view data array for the view
        $viewData = [
            'markets' => $markets
        ];

        //Send the emails
        foreach ($this->splitEmailRecipients($marketUpdateEmails) as $recipientEmailAddress) {
            Mail::send(new PreviewEmailMarketingMail(
                $recipientEmailAddress,
                "Local Event markets sync invalid notification",
                view('emails.local-event-market-analysis-invalid', $viewData)->render(),
                view('emails.plain.local-event-market-analysis-invalid', $viewData)->render()
            ));
        }
    }

    /**
     * Split email string into an array of emails
     *
     * @param string $concatenatedEmails
     * @return string[]
     */
    private function splitEmailRecipients(string $concatenatedEmails) : array
    {
        // Split the emails into an array of recipients
        if (strpos($concatenatedEmails, ',') !== false) {
            $emailArray = explode(',', $concatenatedEmails);
            return array_map('trim', $emailArray);
        }

        return array($concatenatedEmails);
    }
}
