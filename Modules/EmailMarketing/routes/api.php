<?php

use App\Http\Admin\LocalEvents\Controllers\CustomersInMarketRadiusController;
use App\Http\Admin\LocalEvents\Controllers\SearchForMarketsController;
use Illuminate\Support\Facades\Route;
use Modules\EmailMarketing\Http\Controllers\LocalEvents\Api\ChangeMarketController;
use Modules\EmailMarketing\Http\Controllers\LocalEvents\Api\FetchCustomizablePreviewContentController;
use Modules\EmailMarketing\Http\Controllers\LocalEvents\Api\FetchMailingPreviewController;
use Modules\EmailMarketing\Http\Controllers\LocalEvents\Api\FetchNearbyMarketsController;
use Modules\EmailMarketing\Http\Controllers\LocalEvents\Api\FireMarketSyncController;
use Modules\EmailMarketing\Http\Controllers\LocalEvents\Api\SearchZipCodesController;

Route::group(['prefix' => 'local-events', 'middleware' => 'scope:*'], function () {
    Route::get('preview', route_action(FetchMailingPreviewController::class))
        ->name('local-events.preview-mailing.fetch');

    Route::get('customization-preview', route_action(FetchCustomizablePreviewContentController::class))
        ->name('local-events.customizable-preview-content.fetch');

    Route::post('sync-markets', route_action(FireMarketSyncController::class))
        ->name('local-content.sync-markets');

    Route::apiResource('change-market', '\\' . ChangeMarketController::class, [
        'as' => 'local-events'
    ])->only(['index', 'update']);

    Route::get('search-address', route_action(SearchZipCodesController::class))
        ->name('local-events.change-market.search-zip');

    Route::get('fetch-nearby-markets', route_action(FetchNearbyMarketsController::class))
        ->name('local-events.change-market.fetch-nearby-markets');
});

Route::group(['middleware' => ['auth', 'scope:*']], function () {

    // Backend Routes
    Route::group(
        [
            'prefix'     => 'admin/local-events',
            'middleware' => 'can:admin.view',
            'as'         => 'admin.local-events.',
        ],
        function () {
            Route::get('markets/{market}/customers-in-radius', route_action(CustomersInMarketRadiusController::class))
                ->name('markets.customers-in-radius')
                ->middleware('can:local-content.markets.index');

            Route::get('search-markets', route_action(SearchForMarketsController::class))
                ->name('markets.search')
                ->middleware('can:local-content.markets.index');
        }
    );
});
