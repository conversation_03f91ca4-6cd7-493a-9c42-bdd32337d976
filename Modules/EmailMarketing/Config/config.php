<?php

use Illuminate\Support\Str;

return [
    'name' => 'EmailMarketing',

    'local_events' => [
        'transports' => [
            'occasion_genius' => [
                'domain' => env('OCCASION_GENIUS_DOMAIN'),
                'token'  => env('OCCASION_GENIUS_TOKEN'),
                'global_virtual_area_uuid' => env('OCCASION_GENIUS_GLOBAL_VIRTUAL_AREA_UUID'),
                'hide_events_on_create_flags' => env('OCCASION_GENIUS_HIDE_EVENTS_ON_CREATE_FLAGS')
            ],
        ],
        'unsubscribe_url' => '%unsubscribe_url%',

        'sync_markets' => env('LOCAL_EVENTS_SYNC_MARKETS', false),

        'process_recurrences' => env('LOCAL_EVENTS_PROCESS_RECURRENCES', false),

        'share_mailings_url' => env('LOCAL_CONTENT_SHARE_MAILING_BASE_URL')
            ?: Str::replaceFirst('//', '//events.', env('APP_URL')),
    ],
];
