<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Actions;

use Modules\EmailMarketing\Domain\LocalEvents\AggregateRoots\RecurrenceAggregateRoot;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface;
use Modules\EmailMarketing\DTO\UpdateRecurrenceSettingsDTO;
use Modules\EmailMarketing\Models\RecurrenceSettings;

class UpdateRecurrenceSettings
{
    /** @var \Modules\EmailMarketing\Domain\LocalEvents\AggregateRoots\RecurrenceAggregateRoot */
    private $aggregateRoot;

    /** @var \Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface */
    private $repository;

    public function __construct(RecurrenceAggregateRoot $aggregateRoot, RecurrenceSettingsRepositoryInterface $repository)
    {
        $this->aggregateRoot = $aggregateRoot;
        $this->repository = $repository;
    }

    public function execute(UpdateRecurrenceSettingsDTO $recurrenceDto) : RecurrenceSettings
    {
        //update the record

        if (!$recurrenceDto->new->is_enabled) {
            //reset subject and heading if recurrence is disabled
            $recurrenceDto->new->mailing_subject = null;
            $recurrenceDto->new->persist_subject = false;
        }

        $this->aggregateRoot->update($recurrenceDto)->persist();

        //return the record
        return $this->repository->getRecurrenceSettingsForAccount();
    }
}
