<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Actions;

use Modules\EmailMarketing\Domain\LocalEvents\AggregateRoots\RecurrenceAggregateRoot;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface;
use Modules\EmailMarketing\DTO\RecurrenceSettingsDTO;
use Modules\EmailMarketing\Models\RecurrenceSettings;

class AddRecurrenceSettings
{
    /** @var \Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface */
    private $repository;

    public function __construct(RecurrenceSettingsRepositoryInterface $repository)
    {
        $this->repository = $repository;
    }

    public function execute(RecurrenceSettingsDTO $recurrenceDto) : RecurrenceSettings
    {
        //create the new object
        RecurrenceAggregateRoot::create($recurrenceDto)->persist();

        //return the record
        return $this->repository->getRecurrenceSettingsForAccount();
    }
}
