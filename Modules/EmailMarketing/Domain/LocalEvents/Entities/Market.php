<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Entities;

use Spatie\DataTransferObject\DataTransferObject;
use DateTimeZone;

class Market extends DataTransferObject
{
    /** @var \Ramsey\Uuid\UuidInterface */
    public $uuid;

    /** @var string */
    public $name;

    /** @var string */
    public $address;

    /** @var float|int */
    public $latitude;

    /** @var float|int */
    public $longitude;

    /** @var int */
    public $radius = 15;

    /** @var DateTimeZone|null */
    public $timezone;

    public function getGeocode() : Geocode
    {
        return new Geocode($this->latitude, $this->longitude);
    }
}
