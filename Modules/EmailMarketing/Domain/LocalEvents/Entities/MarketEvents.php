<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Entities;

use Modules\EmailMarketing\Models\Event;
use Modules\EmailMarketing\Models\Market;

class MarketEvents
{
    /** @var \Modules\EmailMarketing\Models\Market */
    public $market;

    /** @var \Modules\EmailMarketing\Models\Event */
    public $featuredEvent;

    /** @var \Modules\EmailMarketing\Domain\LocalEvents\Entities\EventCollection */
    public $events;

    public function __construct(Market $market, ?Event $featuredEvent, EventCollection $events)
    {
        $this->market = $market;
        $this->featuredEvent = $featuredEvent;
        $this->events = $events;
    }
}