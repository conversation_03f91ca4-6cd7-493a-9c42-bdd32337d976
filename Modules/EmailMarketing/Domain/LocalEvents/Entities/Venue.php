<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Entities;

use Spatie\DataTransferObject\DataTransferObject;

class Venue extends DataTransferObject
{
    /** @var string */
    public $uuid;

    /** @var string */
    public $name;

    /** @var string|null */
    public $address_1;

    /** @var null|string */
    public $address_2;

    /** @var string */
    public $city;

    /** @var string */
    public $region;

    /** @var string */
    public $postal_code;

    /** @var string|null */
    public $country;

    /** @var string|null */
    public $phone;

    /** @var string|null */
    public $url;

    /** @var float|string|null */
    public $latitude;

    /** @var float|string|null */
    public $longitude;

    /** @var string|null */
    public $space;

    /** @var string|null */
    public $g_identifier;
}
