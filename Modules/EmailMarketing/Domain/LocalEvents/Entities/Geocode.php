<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Entities;

class Geocode
{
    /** @var float */
    private $latitude;

    /** @var float */
    private $longitude;

    public function __construct(float $latitude, float $longitude)
    {
        $this->latitude = $latitude;
        $this->longitude = $longitude;
    }

    public function getLatitude() : float
    {
        return $this->latitude;
    }

    public function getLongitude() : float
    {
        return $this->longitude;
    }

    public function getDistanceFromAnotherGeocode(Geocode $other, string $unit = 'M') : float
    {
        $dist = sin(deg2rad($this->latitude))
                * sin(deg2rad($other->latitude))
                +  cos(deg2rad($this->latitude))
                * cos(deg2rad($other->latitude))
                * cos(deg2rad($this->longitude - $other->longitude));
        $dist = acos($dist);
        $dist = rad2deg($dist);
        $miles = $dist * 60 * 1.1515;

        $unit = strtoupper($unit);

        if ($unit == "K") {
            return ($miles * 1.609344);
        } else if ($unit == "N") {
            return ($miles * 0.8684);
        } else {
            return $miles;
        }
    }
}