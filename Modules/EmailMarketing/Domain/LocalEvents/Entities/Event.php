<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Entities;

use Spatie\DataTransferObject\DataTransferObject;

class Event extends DataTransferObject
{
    /** @var \Ramsey\Uuid\UuidInterface */
    public $uuid;

    /** @var string|null */
    public $recurring_event_uuid;

    /** @var string */
    public $name;

    /** @var string */
    public $description;

    /** @var \Modules\EmailMarketing\Domain\LocalEvents\Entities\EventDate[] */
    public $dates;

    /** @var \Modules\EmailMarketing\Domain\LocalEvents\Entities\Venue */
    public $venue;

    /** @var int (0-6) */
    public $popularity;

    /** @var string */
    public $imageUrl;

    /** @var string|null */
    public $url;

    /** @var null|string[] */
    public $flags;

    /** @var bool */
    public $isVirtual;

    /** @var bool */
    public $isInPerson;

    /** @var array|null */
    public $cancelled;
}
