<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Repositories;

use Carbon\Carbon;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\EventCollection;
use Modules\EmailMarketing\Entities\MarketId;

interface EventRepositoryInterface
{
    public function getForLocation(
        MarketId $location,
        ?int $distance = null,
        ?Carbon $startOn = null,
        ?Carbon $endOn = null
    ) : EventCollection;
}
