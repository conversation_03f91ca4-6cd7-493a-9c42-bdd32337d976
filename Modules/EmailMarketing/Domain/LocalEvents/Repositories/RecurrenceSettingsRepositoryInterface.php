<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Repositories;

use Carbon\Carbon;
use DateTimeZone;
use Domain\Mailings\DTO\AppearanceCustomizationsDTO;
use Illuminate\Support\Collection;
use Modules\EmailMarketing\Entities\MarketId;
use Modules\EmailMarketing\Models\Event;
use Modules\EmailMarketing\Models\RecurrenceSettings;

interface RecurrenceSettingsRepositoryInterface
{
    public function getRecurrenceSettingsForAccount() : ?RecurrenceSettings;

    public function getDefaultRecurrenceSettingsValues() : ?RecurrenceSettings;

    public function getDefaultMailingSubject(
        MarketId $locationId,
        String $frequency,
        Carbon $mailingDate,
        ?int $accountId = null
    ) : string;

    public function addRecurrenceSettings(
        string $frequency,
        int $emailFromId,
        Carbon $mailingDate,
        ?string $mailingSubject,
        bool $persistSubject,
        ?string $mailingHeading,
        bool $persistHeading,
        ?string $mailingBody,
        array $recipientGroupIds,
        bool $isEnabled,
        bool $failed,
        AppearanceCustomizationsDTO $mailingCustomizations
    ) : RecurrenceSettings;

    public function updateRecurrenceSettings(
        int $id,
        string $frequency,
        int $emailFromId,
        Carbon $mailingDate,
        ?string $mailingSubject,
        bool $persistSubject,
        ?string $mailingHeading,
        bool $persistHeading,
        ?string $mailingBody,
        array $recipientGroupIds,
        bool $isEnabled,
        bool $failed,
        AppearanceCustomizationsDTO $mailingCustomizations
    ) : RecurrenceSettings;

    public function getFeaturedEventForMarket(MarketId $locationId, Carbon $startOn, Carbon $endOn) : ?Event;

    public function getNextAvailableMailingDate(DateTimeZone $locationTimezone) : Carbon;

    public function getEnabledRecurrencesSendingBetween(Carbon $start, Carbon $end) : Collection;

    public function getDisabledRecurrencesSendingBetween(Carbon $start, Carbon $end) : Collection;

    public function enableRecurrenceSettings(int $id, Carbon $mailingDate) : bool;

    public function disableRecurrenceSettings(int $id) : bool;
}
