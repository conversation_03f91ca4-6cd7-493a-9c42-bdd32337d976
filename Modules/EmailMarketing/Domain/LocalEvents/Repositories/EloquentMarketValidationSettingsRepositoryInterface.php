<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Repositories;

use Modules\EmailMarketing\Domain\BlogEmail\Entities\MarketValidationSettings;
use Modules\EmailMarketing\Models\Setting;

class EloquentMarketValidationSettingsRepositoryInterface implements MarketValidationSettingsRepositoryInterface
{
    public function get() : ?MarketValidationSettings
    {
        return new MarketValidationSettings(
            Setting::get('local-events.market_update_email_list', null)
        );
    }

    public function set(MarketValidationSettings $settings) : bool
    {
        Setting::add('local-events.market_update_email_list', $settings->getMarketUpdateEmailList());

        return true;
    }
}
