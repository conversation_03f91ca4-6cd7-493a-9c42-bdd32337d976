<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\AggregateRoots;

use Modules\EmailMarketing\Domain\LocalEvents\Events\RecurrenceDisabled;
use Modules\EmailMarketing\Domain\LocalEvents\Events\RecurrenceEnabled;
use Modules\EmailMarketing\Domain\LocalEvents\Events\RecurrenceSettingsAdded;
use Modules\EmailMarketing\Domain\LocalEvents\Events\RecurrenceSettingsUpdated;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface;
use Modules\EmailMarketing\DTO\RecurrenceSettingsDTO;
use Modules\EmailMarketing\DTO\UpdateRecurrenceSettingsDTO;
use Domain\LocalEvents\Exceptions\RecurrenceSettingsDoesNotExist;
use App\EventSourcing\AggregateRoot;

final class RecurrenceAggregateRoot extends AggregateRoot
{
    /** @var \Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface */
    private $repository;

    public function __construct(RecurrenceSettingsRepositoryInterface $repository)
    {
        $this->repository = $repository;
    }

    public static function create(RecurrenceSettingsDTO $recurrenceDto) : RecurrenceAggregateRoot
    {
        //get a new instance
        $ar = new self(app(RecurrenceSettingsRepositoryInterface::class));

        //record the event
        $ar->recordThat(new RecurrenceSettingsAdded(
            $recurrenceDto->frequency,
            $recurrenceDto->email_from_id,
            $recurrenceDto->mailing_date,
            $recurrenceDto->mailing_subject,
            $recurrenceDto->persist_subject,
            $recurrenceDto->mailing_heading,
            $recurrenceDto->persist_heading,
            $recurrenceDto->mailing_body,
            $recurrenceDto->recipient_group_ids,
            $recurrenceDto->is_enabled,
            $recurrenceDto->failed,
            $recurrenceDto->mailing_customizations
        ));

        //return the instance
        return $ar;
    }

    public function update(UpdateRecurrenceSettingsDTO $updateDto) : RecurrenceAggregateRoot
    {
        //get the account recurrence settings
        $recurrenceSettings = $this->repository->getRecurrenceSettingsForAccount();

        //if it doesn't exist, throw an exception
        if (! $recurrenceSettings) {
            throw new RecurrenceSettingsDoesNotExist;
        }

        //check if the recurrence change enabled/disabled to dispatch the correct event
        if($updateDto->old->is_enabled != $updateDto->new->is_enabled) {

            //Check if we need to reset the mailing date
            //Happens when re-enabling a recurrence
            if ($updateDto->old->is_enabled == false && $updateDto->new->is_enabled == true) {
                //remove any failed status when re-enabling
                $updateDto->new->failed = false;

                //get the account from the recurrence settings object
                $account = $recurrenceSettings->account;

                //calculate mailing date
                if ($account->localContentMarket) {
                    $updateDto->new->mailing_date = $this->repository->getNextAvailableMailingDate($account->localContentMarket->timezone);
                }

                // dispatch event to enable the recurrence
                $this->recordThat(new RecurrenceEnabled(
                    $recurrenceSettings->id,
                    $updateDto->new->is_enabled,
                    $updateDto->new->mailing_date));

            } else {
                // dispatch event to disable the recurrence
                $this->recordThat(new RecurrenceDisabled($recurrenceSettings->id, $updateDto->new->is_enabled));
            }
        }

        //fire the update event
        $this->recordThat(new RecurrenceSettingsUpdated(
            $updateDto->new->frequency,
            $updateDto->new->email_from_id,
            $updateDto->new->mailing_date,
            $updateDto->new->mailing_subject,
            $updateDto->new->persist_subject,
            $updateDto->new->mailing_heading,
            $updateDto->new->persist_heading,
            $updateDto->new->mailing_body,
            $updateDto->new->recipient_group_ids,
            $updateDto->new->is_enabled,
            $updateDto->new->failed,
            $updateDto->new->mailing_customizations
        ));

        return $this;
    }
}
