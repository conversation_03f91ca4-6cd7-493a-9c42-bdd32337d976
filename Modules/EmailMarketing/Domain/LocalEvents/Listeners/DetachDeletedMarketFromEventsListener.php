<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Modules\EmailMarketing\Domain\Location\Events\LocalContentMarketDeleted;
use Modules\EmailMarketing\Models\Market;

class DetachDeletedMarketFromEventsListener implements ShouldQueue
{
    public function handle(LocalContentMarketDeleted $event)
    {
        //get trashed market using the uuid in the event
        $deletedMarket = Market::onlyTrashed()->find($event->getMarketUuid());

        //if there's a deleted market
        if ($deletedMarket) {
            //detach all events
            $deletedMarket->events()->detach();
        }
    }
}
