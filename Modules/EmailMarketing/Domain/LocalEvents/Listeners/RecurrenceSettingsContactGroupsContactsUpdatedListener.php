<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Listeners;

use App\Context\Jobs\AccountAware;
use Domain\Contacts\Events\ContactGroupsContactsUpdatedEvent;
use Domain\Mailings\DTO\AppearanceCustomizationsDTO;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;
use Infrastructure\Contacts\Models\ContactGroup;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface;

class RecurrenceSettingsContactGroupsContactsUpdatedListener implements ShouldQueue, AccountAware
{
    use SerializesModels;

    /** @var \Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface */
    private $repository;

    public function __construct(RecurrenceSettingsRepositoryInterface $repository)
    {
        $this->repository = $repository;
    }

    public function handle(ContactGroupsContactsUpdatedEvent $event)
    {
        //get the updated group ids
        $updatedGroupIds = $event->getContactGroupIds();

        //from those group ids, get any that now has 0 contacts
        $contactGroups = ContactGroup::whereIn('id', $updatedGroupIds)->withCount('contacts')->get();

        $emptyGroupIds = $contactGroups->filter(function ($grp) {
            return $grp->contacts_count == 0;
        })
            ->pluck('id')
            ->toArray();

        //if there are any empty groups, proceed
        if (! count($emptyGroupIds)) {
            return;
        }

        //get latest recurrence for the account
        $recurrenceSettings = $this->repository->getRecurrenceSettingsForAccount();

        //check if the deleted group is in the recipient group ids
        if ($recurrenceSettings && count(array_intersect($emptyGroupIds, $recurrenceSettings->recipient_group_ids)) > 0) {
            //remove it from the array
            $newGroupsArray = array_diff($recurrenceSettings->recipient_group_ids, $emptyGroupIds);

            //if the new array is empty, we need to disable the recurrence
            if (0 == count($newGroupsArray)) {
                $recurrenceSettings->is_enabled = false;
            }

            //update the recurrence settings
            $this->repository->updateRecurrenceSettings(
                $recurrenceSettings->id,
                $recurrenceSettings->frequency,
                $recurrenceSettings->email_from_id,
                $recurrenceSettings->mailing_date,
                $recurrenceSettings->mailing_subject,
                $recurrenceSettings->persist_subject,
                $recurrenceSettings->mailing_heading,
                $recurrenceSettings->persist_heading,
                $recurrenceSettings->mailing_body,
                array_values($newGroupsArray), //use array_values to reset the indexes
                (0 == count($newGroupsArray)) ? false : $recurrenceSettings->is_enabled, //if the new array is empty, we need to disable the recurrence
                $recurrenceSettings->failed,
                AppearanceCustomizationsDTO::fromArray($recurrenceSettings->mailing_customizations)
            );
        }
    }
}
