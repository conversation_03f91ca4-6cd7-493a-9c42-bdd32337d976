<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Events;

use Illuminate\Support\Arr;
use Infrastructure\Events\AccountAwareEvent;
use ReminderMedia\Messaging\ShouldBroadcast;

class RecurrenceDisabled extends AccountAwareEvent implements ShouldBroadcast
{
    /** @var string */
    protected $globalName = 'local-events.recurrence-settings-disabled';

    /** @var int */
    public $id;

    /** @var bool */
    public $isEnabled;

    public function __construct(int $id, bool $isEnabled)
    {
        parent::__construct();
        $this->id = $id;
        $this->isEnabled = $isEnabled;
    }

    protected function setData(array $data): void
    {
        $this->id = Arr::get($data, 'id');
        $this->isEnabled = Arr::get($data, 'is_enabled');
    }

    protected function getData(): array
    {
        return [
            'id'         => $this->getId(),
            'is_enabled' => $this->isEnabled(),
        ];
    }

    /** @return int */
    public function getId(): int
    {
        return $this->id;
    }

    /** @return bool */
    public function isEnabled(): bool
    {
        return $this->isEnabled;
    }
}
