<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Events;

use Carbon\Carbon;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Infrastructure\Events\AccountAwareEvent;
use ReminderMedia\Messaging\ShouldBroadcast;

class RecurrenceSettingsUpdated extends AccountAwareEvent implements ShouldBroadcast
{
    use SerializesModels;

    /** @var string */
    protected $globalName = 'local-events.recurrence-settings-updated';

    /** @var string */
    public $newFrequency;

    /** @var int */
    public $newEmailFromId;

    /** @var \Carbon\Carbon */
    public $newMailingDate;

    /** @var string|null */
    public $newMailingSubject;

    /** @var bool */
    public $newpersistSubject;

    /** @var string|null */
    public $newMailingHeading;

    /** @var bool */
    public $newpersistHeading;

    /** @var string|null */
    public $newMailingBody;

    /** @var array */
    public $newRecipientGroupIds;

    /** @var bool */
    public $newIsEnabled;

    /** @var bool */
    public $newFailed;

    /** @var array|null */
    public $newMailingCustomizations;

    public function __construct(
        string $newFrequency,
        int $newEmailFromId,
        Carbon $newMailingDate,
        ?string $newMailingSubject,
        bool $newpersistSubject,
        ?string $newMailingHeading,
        bool $newpersistHeading,
        ?string $newMailingBody,
        array $newRecipientGroupIds,
        bool $newIsEnabled,
        bool $newFailed,
        ?array $newMailingCustomizations
    ) {
        parent::__construct();

        $this->newFrequency = $newFrequency;
        $this->newEmailFromId = $newEmailFromId;
        $this->newMailingSubject = $newMailingSubject;
        $this->newMailingDate = $newMailingDate;
        $this->newpersistSubject = $newpersistSubject;
        $this->newMailingHeading = $newMailingHeading;
        $this->newpersistHeading = $newpersistHeading;
        $this->newMailingBody = $newMailingBody;
        $this->newRecipientGroupIds = $newRecipientGroupIds;
        $this->newIsEnabled = $newIsEnabled;
        $this->newFailed = $newFailed;
        $this->newMailingCustomizations = $newMailingCustomizations;
    }

    public function getNewFrequency() : string
    {
        return $this->newFrequency;
    }

    public function getNewEmailFromId() : int
    {
        return $this->newEmailFromId;
    }

    public function getNewMailingDate() : Carbon
    {
        return $this->newMailingDate;
    }

    public function getNewMailingSubject() : ?string
    {
        return $this->newMailingSubject;
    }

    public function getNewpersistSubject() : bool
    {
        return $this->newpersistSubject;
    }

    public function getNewMailingHeading() : ?string
    {
        return $this->newMailingHeading;
    }

    public function getNewpersistHeading() : bool
    {
        return $this->newpersistHeading;
    }

    public function getNewMailingBody() : ?string
    {
        return $this->newMailingBody;
    }

    public function getNewRecipientGroupIds() : array
    {
        return $this->newRecipientGroupIds;
    }

    public function getNewIsEnabled() : bool
    {
        return $this->newIsEnabled;
    }

    public function getNewFailed() : bool
    {
        return $this->newFailed;
    }

    public function getNewMailingCustomizations() : ?array
    {
        return $this->newMailingCustomizations;
    }

    protected function getData() : array
    {
        return [
            'frequency'           => $this->getNewFrequency(),
            'email_from_id'       => $this->getNewEmailFromId(),
            'mailing_date'        => $this->getNewMailingDate(),
            'mailing_subject'     => $this->getNewMailingSubject(),
            'persist_subject'  => $this->getNewpersistSubject(),
            'mailing_heading'     => $this->getNewMailingHeading(),
            'persist_heading'  => $this->getNewpersistHeading(),
            'mailing_body'        => $this->getNewMailingBody(),
            'recipient_group_ids' => $this->getNewRecipientGroupIds(),
            'is_enabled'          => $this->getNewIsEnabled(),
            'failed'              => $this->getNewFailed(),
            'mailing_customizations' => $this->getNewMailingCustomizations()
        ];
    }

    protected function setData(array $data) : void
    {
        $this->newFrequency = Arr::get($data, 'frequency');
        $this->newEmailFromId = Arr::get($data, 'email_from_id');
        $this->newMailingSubject = Arr::get($data, 'mailing_subject');
        $this->newpersistSubject = Arr::get($data, 'persist_subject');
        $this->newMailingHeading = Arr::get($data, 'mailing_heading');
        $this->newpersistHeading = Arr::get($data, 'persist_heading');
        $this->newMailingDate = Arr::get($data, 'mailing_date');
        $this->newMailingBody = Arr::get($data, 'mailing_body');
        $this->newRecipientGroupIds = Arr::get($data, 'recipient_group_ids');
        $this->newIsEnabled = Arr::get($data, 'is_enabled');
        $this->newFailed = Arr::get($data, 'failed');
        $this->newMailingCustomizations = Arr::get($data, 'mailing_customizations', []);
    }
}
