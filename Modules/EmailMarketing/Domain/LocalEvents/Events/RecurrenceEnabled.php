<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Events;

use Carbon\Carbon;
use Illuminate\Support\Arr;
use Infrastructure\Events\AccountAwareEvent;
use ReminderMedia\Messaging\ShouldBroadcast;

class RecurrenceEnabled extends AccountAwareEvent implements ShouldBroadcast
{
    /** @var string */
    protected $globalName = 'local-events.recurrence-settings-enabled';

    /** @var int */
    public $id;

    /** @var bool */
    public $isEnabled;

    /** @var Carbon */
    public $mailingDate;

    public function __construct(int $id, bool $isEnabled, Carbon $mailingDate)
    {
        parent::__construct();
        $this->id = $id;
        $this->isEnabled = $isEnabled;
        $this->mailingDate = $mailingDate;

    }

    protected function setData(array $data): void
    {
        $this->id = Arr::get($data, 'id');
        $this->isEnabled = Arr::get($data, 'is_enabled');
        $this->mailingDate = Arr::get($data, 'mailing_date');
    }

    protected function getData(): array
    {
        return [
            'id'            => $this->getId(),
            'is_enabled'    => $this->isEnabled(),
            'mailing_date'  => $this->getMailingDate(),
        ];
    }

    /** @return int */
    public function getId(): int
    {
        return $this->id;
    }

    /** @return bool */
    public function isEnabled(): bool
    {
        return $this->isEnabled;
    }

    /** @return Carbon */
    public function getMailingDate(): Carbon
    {
        return $this->mailingDate;
    }
}
