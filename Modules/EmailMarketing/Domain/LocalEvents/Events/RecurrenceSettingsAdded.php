<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Events;

use Carbon\Carbon;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Infrastructure\Events\AccountAwareEvent;
use ReminderMedia\Messaging\ShouldBroadcast;

class RecurrenceSettingsAdded extends AccountAwareEvent implements ShouldBroadcast
{
    use SerializesModels;

    /** @var string */
    protected $globalName = 'local-events.recurrence-settings-added';

    /** @var string */
    public $frequency;

    /** @var int */
    public $emailFromId;

    /** @var \Carbon\Carbon */
    public $mailingDate;

    /** @var string|null */
    public $mailingSubject;

    /** @var bool */
    public $persistSubject;

    /** @var string|null */
    public $mailingHeading;

    /** @var bool */
    public $persistHeading;

    /** @var string|null */
    public $mailingBody;

    /** @var array */
    public $recipientGroupIds;

    /** @var bool */
    public $isEnabled;

    /** @var bool */
    public $failed;

    /** @var array|null */
    public $mailingCustomizations;

    public function __construct(
        string $frequency,
        int $emailFromId,
        Carbon $mailingDate,
        ?string $mailingSubject,
        bool $persistSubject,
        ?string $mailingHeading,
        bool $persistHeading,
        ?string $mailingBody,
        array $recipientGroupIds,
        bool $isEnabled,
        bool $failed,
        ?array $mailingCustomizations
    ) {
        parent::__construct();

        $this->frequency = $frequency;
        $this->emailFromId = $emailFromId;
        $this->mailingSubject = $mailingSubject;
        $this->persistSubject = $persistSubject;
        $this->mailingHeading = $mailingHeading;
        $this->persistHeading = $persistHeading;
        $this->mailingDate = $mailingDate;
        $this->mailingBody = $mailingBody;
        $this->recipientGroupIds = $recipientGroupIds;
        $this->isEnabled = $isEnabled;
        $this->failed = $failed;
        $this->mailingCustomizations = $mailingCustomizations;
    }

    public function getFrequency() : string
    {
        return $this->frequency;
    }

    public function getEmailFromId() : int
    {
        return $this->emailFromId;
    }

    public function getMailingDate() : Carbon
    {
        return $this->mailingDate;
    }

    public function getMailingSubject() : ?string
    {
        return $this->mailingSubject;
    }

    public function getpersistSubject() : bool
    {
        return $this->persistSubject;
    }

    public function getMailingHeading() : ?string
    {
        return $this->mailingHeading;
    }

    public function getpersistHeading() : bool
    {
        return $this->persistHeading;
    }

    public function getMailingBody() : ?string
    {
        return $this->mailingBody;
    }

    public function getRecipientGroupIds() : array
    {
        return $this->recipientGroupIds;
    }

    public function getIsEnabled() : bool
    {
        return $this->isEnabled;
    }

    public function hasFailed() : bool
    {
        return $this->failed;
    }

    public function getMailingCustomizations() : ?array
    {
        return $this->mailingCustomizations;
    }

    protected function getData() : array
    {
        return [
            'frequency'           => $this->getFrequency(),
            'email_from_id'       => $this->getEmailFromId(),
            'mailing_date'        => $this->getMailingDate(),
            'mailing_subject'     => $this->getMailingSubject(),
            'persist_subject'  => $this->getpersistSubject(),
            'mailing_heading'     => $this->getMailingHeading(),
            'persist_heading'  => $this->getpersistHeading(),
            'mailing_body'        => $this->getMailingBody(),
            'recipient_group_ids' => $this->getRecipientGroupIds(),
            'is_enabled'          => $this->getIsEnabled(),
            'failed'              => $this->hasFailed(),
            'mailing_customizations' => $this->getMailingCustomizations()
        ];
    }

    protected function setData(array $data) : void
    {
        $this->frequency = Arr::get($data, 'frequency');
        $this->emailFromId = Arr::get($data, 'email_from_id');
        $this->mailingDate = Arr::get($data, 'mailing_date');
        $this->mailingSubject = Arr::get($data, 'mailing_subject');
        $this->persistSubject = Arr::get($data, 'persist_subject');
        $this->mailingHeading = Arr::get($data, 'mailing_heading');
        $this->persistHeading = Arr::get($data, 'persist_heading');
        $this->mailingBody = Arr::get($data, 'mailing_body');
        $this->recipientGroupIds = Arr::get($data, 'recipient_group_ids');
        $this->isEnabled = Arr::get($data, 'is_enabled');
        $this->failed = Arr::get($data, 'failed');
        $this->mailingCustomizations = Arr::get($data, 'mailing_customizations', []);
    }
}
