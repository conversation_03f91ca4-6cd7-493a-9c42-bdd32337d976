<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Events;

use Modules\EmailMarketing\Entities\MarketId;

class FailedToLoadEventsForMarket
{
    /** @var \Modules\EmailMarketing\Entities\MarketId */
    public $marketId;

    /** @var string */
    public $reason;

    public function __construct(MarketId $marketId, string $reason)
    {
        $this->marketId = $marketId;
        $this->reason = $reason;
    }

    public static function withMarketAndReason(MarketId $marketId, string $string)
    {
        return new self($marketId, $string);
    }
}