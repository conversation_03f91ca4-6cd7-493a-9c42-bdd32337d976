<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Projectors;

use App\Events\AccountEnrolledInLocalContent;
use App\EventSourcing\Projectors\Projector;
use App\EventSourcing\Projectors\ProjectsEvents;
use App\Repositories\EloquentAccountRepository;
use Modules\EmailMarketing\Domain\Location\Events\MarketUpdated;
use Modules\EmailMarketing\Entities\MarketId;

/**
 * Class MarketProjector
 * @package Modules\EmailMarketing\Domain\LocalEvents\Projectors
 */
class MarketProjector implements Projector
{
    use ProjectsEvents;

    private $repository;

    public function __construct(EloquentAccountRepository $repository)
    {
        $this->repository = $repository;
    }

    public function onMarketUpdated(MarketUpdated $event)
    {
        $this->repository->updateMarket(
            $event->getAccountId(),
            MarketId::fromString($event->getMarketUuid())
        );
    }

    public function onAccountEnrolledInLocalContent(AccountEnrolledInLocalContent $event)
    {
        $this->repository->updateMarket(
            $event->getAccountId(),
            $event->getMarketId()
        );
    }
}
