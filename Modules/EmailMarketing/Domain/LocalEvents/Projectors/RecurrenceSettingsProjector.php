<?php

namespace Modules\EmailMarketing\Domain\LocalEvents\Projectors;

use App\Events\AccountRemovedFromLocalEvents;
use Domain\Mailings\DTO\AppearanceCustomizationsDTO;
use Modules\EmailMarketing\Domain\LocalEvents\Events\RecurrenceDisabled;
use Modules\EmailMarketing\Domain\LocalEvents\Events\RecurrenceEnabled;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface;
use Modules\EmailMarketing\Domain\Location\Events\MarketUpdated;
use Modules\EmailMarketing\Domain\LocalEvents\Events\RecurrenceSettingsAdded;
use Modules\EmailMarketing\Domain\LocalEvents\Events\RecurrenceSettingsUpdated;
use App\EventSourcing\Projectors\Projector;
use App\EventSourcing\Projectors\ProjectsEvents;

class RecurrenceSettingsProjector implements Projector
{
    use ProjectsEvents;

    /** @var \Modules\EmailMarketing\Domain\LocalEvents\Repositories\RecurrenceSettingsRepositoryInterface */
    private $repository;

    public function __construct(RecurrenceSettingsRepositoryInterface $repository)
    {
        $this->repository = $repository;
    }

    public function onRecurrenceSettingsAdded(RecurrenceSettingsAdded $event) : void
    {
        $this->repository->addRecurrenceSettings(
            $event->getFrequency(),
            $event->getEmailFromId(),
            $event->getMailingDate(),
            $event->getMailingSubject(),
            $event->getpersistSubject(),
            $event->getMailingHeading(),
            $event->getpersistHeading(),
            $event->getMailingBody(),
            $event->getRecipientGroupIds(),
            $event->getIsEnabled(),
            $event->hasFailed(),
            AppearanceCustomizationsDTO::fromArray($event->getMailingCustomizations())
        );
    }

    public function onRecurrenceSettingsUpdated(RecurrenceSettingsUpdated $event) : void
    {
        $recurrenceSettings = $this->repository->getRecurrenceSettingsForAccount();

        if (! $recurrenceSettings) {
            $this->repository->addRecurrenceSettings(
                $event->getNewFrequency(),
                $event->getNewEmailFromId(),
                $event->getNewMailingDate(),
                $event->getNewMailingSubject(),
                $event->getNewpersistSubject(),
                $event->getNewMailingHeading(),
                $event->getNewpersistHeading(),
                $event->getNewMailingBody(),
                $event->getNewRecipientGroupIds(),
                $event->getNewIsEnabled(),
                $event->getNewFailed(),
                AppearanceCustomizationsDTO::fromArray($event->getNewMailingCustomizations())
            );
        }

        $this->repository->updateRecurrenceSettings(
            $recurrenceSettings->id,
            $event->getNewFrequency(),
            $event->getNewEmailFromId(),
            $event->getNewMailingDate(),
            $event->getNewMailingSubject(),
            $event->getNewpersistSubject(),
            $event->getNewMailingHeading(),
            $event->getNewpersistHeading(),
            $event->getNewMailingBody(),
            $event->getNewRecipientGroupIds(),
            $event->getNewIsEnabled(),
            $event->getNewFailed(),
            AppearanceCustomizationsDTO::fromArray($event->getNewMailingCustomizations())
        );
    }

    public function onRecurrenceSettingsEnabled(RecurrenceEnabled $event): void
    {
        $this->repository->enableRecurrenceSettings($event->getId(), $event->getMailingDate());
    }

    public function onRecurrenceSettingsDisabled(RecurrenceDisabled $event): void
    {
        $this->repository->disableRecurrenceSettings($event->getId());
    }

    public function onAccountRemovedFromLocalEvents(AccountRemovedFromLocalEvents $event) : void
    {
        $recurrenceSettings = $this->repository->getRecurrenceSettingsForAccount();

        if (! $recurrenceSettings) {
            return;
        }

        $this->repository->disableRecurrenceSettings($recurrenceSettings->id);
    }

    public function onMarketUpdated(MarketUpdated $event) : void
    {
        $recurrenceSettings = $this->repository->getRecurrenceSettingsForAccount();

        //if no recurrence setting, don't do anything
        if (! $recurrenceSettings) {
            return;
        }

        //otherwise, update the recurrence settings and clear the subject and body
        $res = $this->repository->updateRecurrenceSettings(
            $recurrenceSettings->id,
            $recurrenceSettings->frequency,
            $recurrenceSettings->email_from_id,
            $recurrenceSettings->mailing_date,
            null, //set to null so the next create mailing will generate the default subject for the market
            false, //set to false so we won't persist an old subject
            null, //set to null so the next create mailing will generate the default heading for the market
            false, //set to false so we won't persist an old heading
            "", //set to empty in case the message body contained market specific info
            $recurrenceSettings->recipient_group_ids,
            $recurrenceSettings->is_enabled,
            $recurrenceSettings->failed,
            AppearanceCustomizationsDTO::fromArray($recurrenceSettings->mailing_customizations)
        );
    }
}
