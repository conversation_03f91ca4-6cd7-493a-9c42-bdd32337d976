<?php

namespace Modules\EmailMarketing\Domain\BlogEmail\Entities;

use Illuminate\Contracts\Support\Arrayable;

class MarketValidationSettings implements Arrayable
{
    /** @var null|string */
    private $marketUpdateEmailList;

    public function __construct( ?string $marketUpdateEmailList = null)
    {
        $this->marketUpdateEmailList = $marketUpdateEmailList;
    }

    public function getMarketUpdateEmailList() : ?string
    {
        return $this->marketUpdateEmailList;
    }

    public function setMarketUpdateEmailList(?string $marketUpdateEmailList) : MarketValidationSettings
    {
        $this->marketUpdateEmailList = $marketUpdateEmailList;

        return $this;
    }

    public function toArray() : array
    {
        return [
            'market_update_email_list'  => $this->getMarketUpdateEmailList(),
        ];
    }
}
