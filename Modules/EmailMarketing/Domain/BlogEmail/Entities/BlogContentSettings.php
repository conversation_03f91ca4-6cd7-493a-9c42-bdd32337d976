<?php

namespace Modules\EmailMarketing\Domain\BlogEmail\Entities;

use Illuminate\Contracts\Support\Arrayable;

class BlogContentSettings implements Arrayable
{
    /** @var boolean */
    private $enabled;

    /** @var null|string */
    private $subject;

    /** @var null|string */
    private $body;

    /** @var null|string */
    private $previewUrl;

    public function __construct(
        bool $enabled = false,
        ?string $subject = null,
        ?string $body = null,
        ?string $previewUrl = null
    ) {
        $this->enabled = $enabled;
        $this->subject = $subject;
        $this->body = $body;
        $this->previewUrl = $previewUrl;
    }

    public function isEnabled() : bool
    {
        return $this->enabled;
    }

    public function setEnabled(bool $enabled) : BlogContentSettings
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function getSubject() : ?string
    {
        return $this->subject;
    }

    public function setSubject(?string $subject) : BlogContentSettings
    {
        $this->subject = $subject;

        return $this;
    }

    public function getBody() : ?string
    {
        return $this->body;
    }

    public function setBody(?string $body) : BlogContentSettings
    {
        $this->body = $body;

        return $this;
    }

    public function getFeaturedImage() : ?string
    {
        return $this->previewUrl;
    }

    public function setFeaturedImage(?string $previewUrl) : BlogContentSettings
    {
        $this->previewUrl = $previewUrl;

        return $this;
    }

    public function toArray() : array
    {
        return [
            'enabled'                   => $this->isEnabled(),
            'subject'                   => $this->getSubject(),
            'body'                      => $this->getBody(),
            'preview_url'               => $this->getFeaturedImage(),
        ];
    }
}
