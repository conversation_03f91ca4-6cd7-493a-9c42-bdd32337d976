<?php

namespace Modules\EmailMarketing\Domain\BlogEmail\Repositories;

use Modules\EmailMarketing\Domain\BlogEmail\Entities\BlogContentSettings;
use Modules\EmailMarketing\Models\Setting;

class EloquentBlogContentSettingsRepositoryInterface implements BlogContentSettingsRepositoryInterface
{
    public function get() : ?BlogContentSettings
    {
        return (new BlogContentSettings(Setting::get('blog.enabled', false)))
            ->setSubject(Setting::get('blog.subject'))
            ->setBody(Setting::get('blog.body'))
            ->setFeaturedImage(Setting::get('blog.featured_image'));
    }

    public function set(BlogContentSettings $settings) : bool
    {
        Setting::add('blog.enabled', $settings->isEnabled(), 'bool');
        Setting::add('blog.featured_image', $settings->getFeaturedImage());
        Setting::add('blog.subject', $settings->getSubject());
        Setting::add('blog.body', $settings->getBody());

        return true;
    }
}
