<?php

namespace Modules\EmailMarketing\Domain\BlogEmail\Actions;

use App\Models\User;
use Modules\EmailMarketing\Domain\BlogEmail\Entities\BlogContentSettings;
use Modules\EmailMarketing\Domain\BlogEmail\Repositories\BlogContentSettingsRepositoryInterface;
use Modules\EmailMarketing\Events\BlogContentUpdated;

class UpdateBlogContentAction
{
    /** @var BlogContentSettingsRepositoryInterface */
    protected $repository;

    public function __construct(BlogContentSettingsRepositoryInterface $repository)
    {
        $this->repository = $repository;
    }

    public function execute(BlogContentSettings $settings, bool $shouldUpdateRecurrences = false, ?User $user = null)
    {
        $this->repository->set($settings);

        event(new BlogContentUpdated(
            $settings->isEnabled(),
            $shouldUpdateRecurrences,
            $settings->getFeaturedImage(),
            $settings->getSubject(),
            $settings->getBody(),
            $user
        ));
    }
}
