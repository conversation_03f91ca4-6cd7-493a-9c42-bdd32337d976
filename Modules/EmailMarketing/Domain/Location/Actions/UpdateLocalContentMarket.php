<?php

namespace Modules\EmailMarketing\Domain\Location\Actions;

use Modules\EmailMarketing\DTO\LocalContentMarketDTO;
use Modules\EmailMarketing\Domain\Location\Events\LocalContentMarketUpdated;
use Modules\EmailMarketing\Models\Market;

class UpdateLocalContentMarket
{
    public function execute(LocalContentMarketDTO $locationDto) : Market
    {
        //build the update params
        $updateParams = [
            'name' => $locationDto->name,
            'address' => $locationDto->address,
            'latitude' => $locationDto->latitude,
            'longitude' => $locationDto->longitude,
            'event_radius' => $locationDto->eventRadius,
            'suggested_radius' => $locationDto->suggestedRadius
        ];

        //with the passed uuid, get the location
        $location = Market::findOrFail($locationDto->uuid);

        //clone the old data
        $oldLocation = clone $location;

        //update the location
        $location->update($updateParams);

        //refresh the object
        $location->refresh();

        //fire off the event
        event(LocalContentMarketUpdated::fromLocationOldAndNew($oldLocation, $location));

        //return the updated location
        return $location;
    }
}
