<?php

namespace Modules\EmailMarketing\Domain\Location\Actions;

use Modules\EmailMarketing\Domain\Location\Events\LocalContentInterestedCustomerAdded;
use Modules\EmailMarketing\DTO\LocalContentInterestedCustomerDTO;

class AddLocalContentInterestedCustomer
{
    public function execute(LocalContentInterestedCustomerDTO $dto) : bool
    {
        event(LocalContentInterestedCustomerAdded::fromInterestedCustomerDto($dto));

        return true;
    }
}
