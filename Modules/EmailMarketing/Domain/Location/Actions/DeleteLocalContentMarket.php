<?php

namespace Modules\EmailMarketing\Domain\Location\Actions;

use Modules\EmailMarketing\Domain\Location\Exceptions\MarketToDeleteHasEnrolledAccounts;
use Modules\EmailMarketing\Models\Market;
use Modules\EmailMarketing\Domain\Location\Events\LocalContentMarketDeleted;

class DeleteLocalContentMarket
{
    public function execute(Market $market) : bool
    {
        if ($market->enrolledAccounts()->count() > 0) {
            throw new MarketToDeleteHasEnrolledAccounts('Market has enrolled accounts');
        }

        //delete the location
        $market->delete();

        //fire off the event
        event(LocalContentMarketDeleted::fromMarket($market));

        return true;
    }
}
