<?php

namespace Modules\EmailMarketing\Domain\Location\Actions;

use Modules\EmailMarketing\DTO\LocalContentMarketDTO;
use Modules\EmailMarketing\Models\Market;
use Modules\EmailMarketing\Domain\Location\Events\LocalContentMarketAdded;

class AddLocalContentMarket
{
    public function execute(LocalContentMarketDTO $marketDTO) : Market
    {
        //build the create params
        $createParams = [
            'name'             => $marketDTO->name,
            'address'          => $marketDTO->address,
            'latitude'         => $marketDTO->latitude,
            'longitude'        => $marketDTO->longitude,
            'event_radius'     => $marketDTO->eventRadius,
            'suggested_radius' => $marketDTO->suggestedRadius,
        ];

        //if the uuid passed is NOT empty, add it to the create params
        if (!empty($marketDTO->uuid)) {
            $createParams['uuid'] = $marketDTO->uuid;
        }

        //create the market
        $market = Market::create($createParams);

        //fire off the event
        event(LocalContentMarketAdded::fromMarket($market));

        //return the market
        return $market;
    }
}
