<?php

namespace Modules\EmailMarketing\Domain\Location\Events;

use Illuminate\Support\Arr;
use Infrastructure\Events\AccountAwareEvent;
use Modules\EmailMarketing\Models\Market;
use ReminderMedia\Messaging\ShouldBroadcast;

class MarketUpdated extends AccountAwareEvent implements ShouldBroadcast
{
    /** @var string */
    protected $globalName = 'account.market-updated';

    /** @var string */
    protected $marketUuid;

    public function __construct(string $marketUuid)
    {
        parent::__construct();

        $this->marketUuid = $marketUuid;
    }

    /**
     * Set the properties of the event
     *
     * @param array $data
     *
     * @throws \Exception
     */
    protected function setData(array $data) : void
    {
        $this->marketUuid = Arr::get($data, 'market_id');
    }

    /**
     * Get the properties of the event as an array
     *
     * @return array
     */
    public function getData() : array
    {
        return [
            'market_id' => $this->marketUuid
        ];
    }

    public static function fromMarket(Market $market) : self
    {
        return new self(
            $market->uuid
        );
    }

    public function getMarketUuid() : string
    {
        return $this->marketUuid;
    }
}
