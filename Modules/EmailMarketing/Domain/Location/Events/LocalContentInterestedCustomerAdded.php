<?php

namespace Modules\EmailMarketing\Domain\Location\Events;

use Illuminate\Support\Arr;
use Infrastructure\Events\AccountAwareEvent;
use Modules\EmailMarketing\DTO\LocalContentInterestedCustomerDTO;
use ReminderMedia\Messaging\ShouldBroadcast;

class LocalContentInterestedCustomerAdded extends AccountAwareEvent implements ShouldBroadcast
{
    /** @var string */
    protected $globalName = 'local-content.interested-customer-added';

    /** @var string|null */
    private $address1;

    /** @var string|null */
    private $address2;

    /** @var string|null */
    private $city;

    /** @var string|null */
    private $state;

    /** @var string|null */
    private $zip;

    /** @var float|null */
    private $latitude;

    /** @var float|null */
    private $longitude;

    public function __construct(
        ?string $address1,
        ?string $address2,
        ?string $city,
        ?string $state,
        ?string $zip,
        ?float $latitude,
        ?float $longitude
    ) {
        parent::__construct();

        $this->address1 = $address1;
        $this->address2 = $address2;
        $this->city = $city;
        $this->state = $state;
        $this->zip = $zip;
        $this->latitude = $latitude;
        $this->longitude = $longitude;
    }

    public function getAddress1() : ?string
    {
        return $this->address1;
    }

    public function getAddress2() : ?string
    {
        return $this->address2;
    }

    public function getCity() : ?string
    {
        return $this->city;
    }

    public function getState() : ?string
    {
        return $this->state;
    }

    public function getZip() : ?string
    {
        return $this->zip;
    }

    public function getLatitude() : ?float
    {
        return $this->latitude;
    }

    public function getLongitude() : ?float
    {
        return $this->longitude;
    }

    protected function setData(array $data) : void
    {
        $this->address1 = Arr::get($data, 'address1');
        $this->address2 = Arr::get($data, 'address2');
        $this->city = Arr::get($data, 'city');
        $this->state = Arr::get($data, 'state');
        $this->zip = Arr::get($data, 'zip');
        $this->latitude = Arr::get($data, 'latitude');
        $this->longitude = Arr::get($data, 'longitude');
    }

    protected function getData() : array
    {
        return [
            'address1'  => $this->getAddress1(),
            'address2'  => $this->getAddress2(),
            'city'      => $this->getCity(),
            'state'     => $this->getState(),
            'zip'       => $this->getZip(),
            'latitude'  => $this->getLatitude(),
            'longitude' => $this->getLongitude(),
        ];
    }

    public static function fromInterestedCustomerDto(LocalContentInterestedCustomerDTO $dto) : self
    {
        return new self(
            $dto->address1,
            $dto->address2,
            $dto->city,
            $dto->state,
            $dto->zip,
            $dto->latitude,
            $dto->longitude
        );
    }
}
