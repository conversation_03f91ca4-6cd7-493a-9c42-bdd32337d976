<?php

namespace Modules\EmailMarketing\Domain\Location\Events;

use Illuminate\Support\Arr;
use Infrastructure\Events\AccountAwareEvent;
use ReminderMedia\Messaging\ShouldBroadcast;
use Modules\EmailMarketing\Models\Market;

class LocalContentMarketAdded extends AccountAwareEvent implements ShouldBroadcast
{
    /** @var string  */
    protected $globalName = 'local-content.market-added';

    /** @var string */
    private $locationUuid;

    /** @var string */
    private $name;

    /** @var string */
    private $address;

    /** @var float */
    private $latitude;

    /** @var float */
    private $longitude;

    /** @var int */
    private $eventRadius;

    /** @var int */
    private $suggestedRadius;

    public function __construct(string $locationUuid, string $name, string $address, float $latitude, float $longitude, int $eventRadius, int $suggestedRadius)
    {
        parent::__construct();

        $this->locationUuid = $locationUuid;
        $this->name = $name;
        $this->address = $address;
        $this->latitude = $latitude;
        $this->longitude = $longitude;
        $this->eventRadius = $eventRadius;
        $this->suggestedRadius = $suggestedRadius;
    }

    public function getMarketUuid() : string
    {
        return $this->locationUuid;
    }

    public function getName() : string
    {
        return $this->name;
    }

    public function getAddress() : string
    {
        return $this->address;
    }

    public function getLatitude() : float
    {
        return $this->latitude;
    }

    public function getLongitude() : float
    {
        return $this->longitude;
    }

    public function getEventRadius() : int
    {
        return $this->eventRadius;
    }

    public function getSuggestedRadius() : int
    {
        return $this->suggestedRadius;
    }

    /**
     * Set the properties of the event
     *
     * @param array $data
     */
    protected function setData(array $data) : void
    {
        $this->locationUuid = Arr::get($data, 'uuid');
        $this->name = Arr::get($data, 'name');
        $this->address = Arr::get($data, 'address');
        $this->latitude = Arr::get($data, 'latitude');
        $this->longitude = Arr::get($data, 'longitude');
        $this->eventRadius = Arr::get($data, 'eventRadius');
        $this->suggestedRadius = Arr::get($data, 'suggestedRadius');
    }

    /**
     * Get the properties of the event as an array
     *
     * @return array
     */
    protected function getData() : array
    {
        return [
            'uuid' => $this->locationUuid,
            'name' => $this->name,
            'address' => $this->address,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'eventRadius' => $this->eventRadius,
            'suggestedRadius' => $this->suggestedRadius
        ];
    }

    public static function fromMarket(Market $location) : self
    {
        return new self(
            $location->uuid,
            $location->name,
            $location->address,
            $location->latitude,
            $location->longitude,
            $location->event_radius,
            $location->suggested_radius
        );
    }

}
