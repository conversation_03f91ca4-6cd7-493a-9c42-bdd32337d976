<?php

namespace Modules\EmailMarketing\Domain\Location\Events;

use Illuminate\Support\Arr;
use Infrastructure\Events\AccountAwareEvent;
use ReminderMedia\Messaging\ShouldBroadcast;
use Modules\EmailMarketing\Models\Market;

class LocalContentMarketUpdated extends AccountAwareEvent implements ShouldBroadcast
{
    /** @var string */
    protected $globalName = 'local-content.market-updated';

    /** @var string */
    private $oldLocationUuid;

    /** @var string */
    private $oldName;

    /** @var string */
    private $oldAddress;

    /** @var float */
    private $oldLatitude;

    /** @var float */
    private $oldLongitude;

    /** @var int */
    private $oldEventRadius;

    /** @var int */
    private $oldSuggestedRadius;

    /** @var string */
    private $newLocationUuid;

    /** @var string */
    private $newName;

    /** @var string */
    private $newAddress;

    /** @var float */
    private $newLatitude;

    /** @var float */
    private $newLongitude;

    /** @var int */
    private $newEventRadius;

    /** @var int */
    private $newSuggestedRadius;

    public function __construct(
        string $oldLocationUuid,
        string $oldName,
        string $oldAddress,
        float $oldLatitude,
        float $oldLongitude,
        int $oldEventRadius,
        int $oldSuggestedRadius,
        string $newLocationUuid,
        string $newName,
        string $newAddress,
        float $newLatitude,
        float $newLongitude,
        int $newEventdRadius,
        int $newSuggestedRadius
    ) {
        parent::__construct();

        $this->oldLocationUuid = $oldLocationUuid;
        $this->oldName = $oldName;
        $this->oldAddress = $oldAddress;
        $this->oldLatitude = $oldLatitude;
        $this->oldLongitude = $oldLongitude;
        $this->oldEventRadius = $oldEventRadius;
        $this->oldSuggestedRadius = $oldSuggestedRadius;
        $this->newLocationUuid = $newLocationUuid;
        $this->newName = $newName;
        $this->newAddress = $newAddress;
        $this->newLatitude = $newLatitude;
        $this->newLongitude = $newLongitude;
        $this->newEventRadius = $newEventdRadius;
        $this->newSuggestedRadius = $newSuggestedRadius;
    }


    /**
     * Set the properties of the event
     *
     * @param array $data
     *
     * @throws \Exception
     */
    protected function setData(array $data) : void
    {
        $this->oldLocationUuid = Arr::get($data, 'old.uuid');
        $this->oldName = Arr::get($data, 'old.name');
        $this->oldAddress = Arr::get($data, 'old.address');
        $this->oldLatitude = Arr::get($data, 'old.latitude');
        $this->oldLongitude = Arr::get($data, 'old.longitude');
        $this->oldEventRadius = Arr::get($data, 'old.oldEventRadius');
        $this->oldSuggestedRadius = Arr::get($data, 'old.suggestedRadius');
        $this->newLocationUuid = Arr::get($data, 'new.uuid');
        $this->newName = Arr::get($data, 'new.name');
        $this->newAddress = Arr::get($data, 'new.address');
        $this->newLatitude = Arr::get($data, 'new.latitude');
        $this->newLongitude = Arr::get($data, 'new.longitude');
        $this->newEventRadius  = Arr::get($data, 'new.newEventRadius');
        $this->newSuggestedRadius = Arr::get($data, 'new.suggestedRadius');
    }

    /**
     * Get the properties of the event as an array
     *
     * @return array
     */
    public function getData() : array
    {
        return [
            'old' => [
                'uuid' => $this->oldLocationUuid,
                'name' => $this->oldName,
                'address' => $this->oldAddress,
                'latitude' => $this->oldLatitude,
                'longitude' => $this->oldLongitude,
                'eventRadius' => $this->oldEventRadius,
                'suggestedRadius' => $this->oldSuggestedRadius,
            ],
            'new' => [
                'uuid' => $this->newLocationUuid,
                'name' => $this->newName,
                'address' => $this->newAddress,
                'latitude' => $this->newLatitude,
                'longitude' => $this->newLongitude,
                'eventRadius' => $this->newEventRadius,
                'suggestedRadius' => $this->newSuggestedRadius,
            ],
        ];
    }

    public static function fromLocationOldAndNew(Market $oldLocationData, Market $newLocationData) : self
    {
        return new self(
            $oldLocationData->uuid,
            $oldLocationData->name,
            $oldLocationData->address,
            $oldLocationData->latitude,
            $oldLocationData->longitude,
            $oldLocationData->event_radius,
            $oldLocationData->suggested_radius,
            $newLocationData->uuid,
            $newLocationData->name,
            $newLocationData->address,
            $newLocationData->latitude,
            $newLocationData->longitude,
            $newLocationData->event_radius,
            $newLocationData->suggested_radius
        );
    }

    public function getOldUuid() : string
    {
        return $this->oldLocationUuid;
    }

    public function getOldName() : string
    {
        return $this->oldName;
    }

    public function getOldAddress() : string
    {
        return $this->oldAddress;
    }

    public function getOldLatitude() : float
    {
        return $this->oldLatitude;
    }

    public function getOldLongitude() : float
    {
        return $this->oldLongitude;
    }

    public function getOldEventRadius() : int
    {
        return $this->oldEventRadius;
    }

    public function getOldSuggestedRadius() : int
    {
        return $this->oldSuggestedRadius;
    }

    public function getNewUuid() : string
    {
        return $this->newLocationUuid;
    }

    public function getNewName() : string
    {
        return $this->newName;
    }

    public function getNewAddress() : string
    {
        return $this->newAddress;
    }

    public function getNewLatitude() : float
    {
        return $this->newLatitude;
    }

    public function getNewLongitude() : float
    {
        return $this->newLongitude;
    }

    public function getNewEventRadius() : int
    {
        return $this->newEventRadius;
    }

    public function getNewSuggestedRadius() : int
    {
        return $this->newSuggestedRadius;
    }
}
