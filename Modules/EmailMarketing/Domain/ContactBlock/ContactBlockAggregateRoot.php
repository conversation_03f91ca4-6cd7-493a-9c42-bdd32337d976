<?php

namespace Modules\EmailMarketing\Domain\ContactBlock;

use App\Models\ContactBlock;
use App\Models\ContactBlockItem;
use Domain\ContactBlock\ContactBlockAggregateRoot as BaseAggregateRoot;
use Modules\EmailMarketing\Domain\ContactBlock\Enums\Sections;
use Modules\EmailMarketing\Domain\ContactBlock\Events\ContactBlockCreated;
use Modules\EmailMarketing\Domain\ContactBlock\Events\ContactBlockUpdated;


final class ContactBlockAggregateRoot extends BaseAggregateRoot
{
    /** @var string */
    protected static $product = ContactBlock::PRODUCT_LOCAL_EVENTS;

    protected static $updateEvent = ContactBlockUpdated::class;

    public static function createInitialContactBlock() : ContactBlockAggregateRoot
    {
        /** @var \Modules\EmailMarketing\Domain\ContactBlock\ContactBlockAggregateRoot $ar */
        $ar = app(self::class);

        $lines = [];

        // Business Rule - Default contact block for local events contains:
        // - Primary photo
        // - Office logo selected
        // - Display name
        // - Title
        // - Primary phone number
        // - Primary email address
        // - Primary location

        //Images
        //Photo
        $primaryPhoto = self::getPrimaryPhotoForContactBlock();

        if ($primaryPhoto) {
            $lines[] = $ar->makeItem(
                ContactBlockItem::ITEM_TYPE_HEADSHOT,
                null,
                json_encode([
                    'id' => $primaryPhoto->getMagazineFileId(),
                    'title' => $primaryPhoto->getAltName(),
                    'urls' => [
                        'preview_url' => $primaryPhoto->getLargeUrl(),
                        'product_url' => $primaryPhoto->getLargeUrl(),
                        'thumbnail_url' => $primaryPhoto->getThumbnailUrl()
                    ]
                ], JSON_UNESCAPED_SLASHES), //so we don't escape the forward slashes in the image paths
                Sections::HEADSHOT
            );
        }

        //get the office logo
        $officeLogoPath = self::getOfficeLogoPathForContactBlock();
        if ($officeLogoPath) {
            $lines[] = $ar->makeItem(
                ContactBlockItem::ITEM_TYPE_OFFICE_LOGO,
                null,
                json_encode([
                    'title' => "Your Logo",
                    'urls' => [
                        'preview_url' => $officeLogoPath,
                        'product_url' => $officeLogoPath,
                        'thumbnail_url' => $officeLogoPath,
                    ],
                ], JSON_UNESCAPED_SLASHES), //so we don't escape the forward slashes in the image paths
                Sections::OFFICE_LOGO
            );
        }

        //Profile Info

        if ($ar->account->display_name) {
            $lines[] = $ar->makeItem(ContactBlockItem::ITEM_TYPE_DISPLAY_NAME, $ar->account->id, null, Sections::NAME);
        }

        /** @var \App\Models\ProfessionalTitle|null $title */
        $title = $ar->account->titles()->first();

        if ($title) {
            $lines[] = $ar->makeItem(ContactBlockItem::ITEM_TYPE_PROFESSIONAL_TITLE, $title->id, null, Sections::SUBTITLE);
        }

        /** @var \App\Models\PhoneNumber|null $phoneNumber */
        $phoneNumber = $ar->account->phoneNumbers()->orderBy('is_primary', 'DESC')->first();

        if ($phoneNumber) {
            $lines[] = $ar->makeItem(ContactBlockItem::ITEM_TYPE_PHONE_NUMBER, $phoneNumber->id, null, Sections::CONTACT_INFOS);
        }

        /** @var \App\Models\EmailAddress|null $phoneNumber */
        $emailAddress = $ar->account->emailAddresses()->orderBy('is_primary', 'DESC')->first();

        if ($emailAddress) {
            $lines[] = $ar->makeItem(ContactBlockItem::ITEM_TYPE_EMAIL_ADDRESS, $emailAddress->id, null, Sections::CONTACT_INFOS);
        }

        /** @var \App\Models\Location|null $location */
        $location = $ar->account->locations()->orderBy('is_primary', 'DESC')->first();

        if ($location) {
            $lines[] = $ar->makeItem(ContactBlockItem::ITEM_TYPE_LOCATION, $location->id, null, Sections::CONTACT_INFOS);
        }

        $ar->recordThat(new ContactBlockCreated($lines));

        return $ar;
    }

    public function update(array $items) : ContactBlockAggregateRoot
    {
        $this->recordThat(new ContactBlockUpdated($items));

        return $this;
    }
}
