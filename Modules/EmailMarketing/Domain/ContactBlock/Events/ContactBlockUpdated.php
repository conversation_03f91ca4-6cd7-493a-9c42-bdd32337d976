<?php

namespace Modules\EmailMarketing\Domain\ContactBlock\Events;

use Illuminate\Support\Arr;
use Infrastructure\Events\AccountAwareEvent;
use ReminderMedia\Messaging\ShouldBroadcast;

class ContactBlockUpdated extends AccountAwareEvent implements ShouldBroadcast
{
    /** @var string */
    protected $globalName = 'profile.local-events-contact-block-updated';

    /** @var array */
    private $items;

    public function __construct(array $items)
    {
        parent::__construct();

        $this->items = $items;
    }

    public function getItems() : array
    {
        return $this->items;
    }

    protected function setData(array $data) : void
    {
        $this->items = Arr::get($data, 'items');
    }

    protected function getData() : array
    {
        return [
            'items' => $this->getItems(),
        ];
    }
}
