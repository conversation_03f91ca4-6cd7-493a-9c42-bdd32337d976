<?php

namespace Modules\EmailMarketing\Domain\ContactBlock\Reactors;

use Domain\ContactBlock\Contracts\RecalculatesContactBlock;
use Domain\Profile\Events\Disclosure\DisclosureDeleted;
use Domain\Profile\Events\EmailAddressDeleted;
use Domain\Profile\Events\LicenseNumberDeleted;
use Domain\Profile\Events\LocationDeleted;
use Domain\Profile\Events\PhoneNumberDeleted;
use Domain\Profile\Events\ProfessionalDesignationDeleted;
use Domain\Profile\Events\ProfessionalTitleDeleted;
use Domain\Profile\Events\SocialMediaDeleted;
use Domain\Profile\Events\TaglineDeleted;
use Domain\Profile\Events\TeamDeleted;
use Domain\Profile\Events\TeamMemberDeleted;
use Domain\Profile\Events\WebsiteDeleted;
use App\EventSourcing\EventHandlers\EventHandler;
use App\EventSourcing\EventHandlers\HandlesEvents;
use App\EventSourcing\EventHandlers\RequiresActiveAccount;
use Modules\EmailMarketing\Domain\ContactBlock\ContactBlockAggregateRoot;

class RecalculateContactBlockReactor implements EventHandler, RequiresActiveAccount
{
    use HandlesEvents;

    /** @var array */
    private $handlesEvents = [
        EmailAddressDeleted::class            => self::class,
        LicenseNumberDeleted::class           => self::class,
        LocationDeleted::class                => self::class,
        PhoneNumberDeleted::class             => self::class,
        ProfessionalDesignationDeleted::class => self::class,
        ProfessionalTitleDeleted::class       => self::class,
        SocialMediaDeleted::class             => self::class,
        TaglineDeleted::class                 => self::class,
        TeamDeleted::class                    => self::class,
        TeamMemberDeleted::class              => self::class,
        WebsiteDeleted::class                 => self::class,
        DisclosureDeleted::class              => self::class,
    ];

    public function __invoke(RecalculatesContactBlock $event): void
    {
        /** @var \Modules\EmailMarketing\Domain\ContactBlock\ContactBlockAggregateRoot $ar */
        $ar = resolve(ContactBlockAggregateRoot::class);

        $ar->recalculate()->persist();
    }
}
