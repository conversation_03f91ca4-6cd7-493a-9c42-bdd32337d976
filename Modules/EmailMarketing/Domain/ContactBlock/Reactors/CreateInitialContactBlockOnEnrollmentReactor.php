<?php

namespace Modules\EmailMarketing\Domain\ContactBlock\Reactors;

use App\Events\AccountEnrolledInLocalContent;
use App\EventSourcing\EventHandlers\EventHandler;
use App\EventSourcing\EventHandlers\HandlesEvents;
use App\Models\ContactBlock;
use Modules\EmailMarketing\Domain\ContactBlock\ContactBlockAggregateRoot;

class CreateInitialContactBlockOnEnrollmentReactor implements EventHandler
{
    use HandlesEvents;

    public function onAccountEnrolledInLocalEvents(AccountEnrolledInLocalContent $event) : void
    {
        if ($this->contactBlockExists()) {
            return;
        }

        /** @var \Modules\EmailMarketing\Domain\ContactBlock\ContactBlockAggregateRoot $ar */
        $ar = ContactBlockAggregateRoot::createInitialContactBlock();

        $ar->persist();
    }

    private function contactBlockExists()
    {
        return ContactBlock::query()
            ->where('product', ContactBlock::PRODUCT_LOCAL_EVENTS)
            ->exists();
    }
}
