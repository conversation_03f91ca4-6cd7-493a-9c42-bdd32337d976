<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use Carbon\Carbon;
use Faker\Generator as Faker;
use Modules\EmailMarketing\Models\Event;
use Modules\EmailMarketing\Models\EventDate;
use Modules\EmailMarketing\Models\Market;
use Modules\EmailMarketing\Models\RecurrenceSettings;
use App\Models\EmailAddress;

/*
|--------------------------------------------------------------------------
| Model Factories
|--------------------------------------------------------------------------
|
| This directory should contain each of the model factory definitions for
| your application. Factories provide a convenient way to generate new
| model instances for testing / seeding your application's database.
|
*/

$factory->define(Market::class, function (Faker $faker) {

    return [
        'uuid'             => $faker->uuid,
        'external_uuid'    => $faker->uuid,
        'name'             => $faker->city,
        'address'          => str_replace("\n", ', ', $faker->address),
        'latitude'         => $faker->latitude,
        'longitude'        => $faker->longitude,
        'timezone'         => new DateTimeZone($faker->timezone),
        'event_radius'     => rand(10, 25),
        'suggested_radius' => rand(10, 25),
        'enabled'          => true
    ];
});

$factory->state(Market::class, 'no_external_uuid', function () {
    return ['external_uuid' => null];
});

$factory->define(Event::class, function (Faker $faker) {
    return [
        'uuid'        => $faker->uuid,
        'recurring_event_uuid' => $faker->uuid,
        'name'        => $faker->name,
        'description' => $faker->sentence,
        'image_url'   => $faker->imageUrl(),
        'source_url'  => $faker->url,
        'popularity'  => rand(0, 6),
        'venue'       => [
            "uuid"          => $faker->uuid,
            "name"          => $faker->name,
            "address_1"     => $faker->streetAddress,
            "address_2"     => null,
            "city"          => $faker->city,
            "region"        => $faker->stateAbbr,
            "postal_code"   => $faker->postcode,
            "country"       => "US",
            "phone"         => $faker->phoneNumber,
            "url"           => $faker->url,
            "latitude"      => $faker->latitude,
            "longitude"     => $faker->longitude,
            "space"         => $faker->word
        ],
        'flags'       => [],
    ];
});

$factory->define(EventDate::class, function (Faker $faker) {
    return [
        'uuid'       => $faker->uuid,
        'event_uuid' => function () {
            return factory(Event::class)->create()->uuid;
        },
        'starts_at'  => function () use ($faker) {
            $startsAt = $faker->dateTimeBetween('now', '+1 month')->format('Y-m-d H:i:s');

            $startsAt = Carbon::parse($startsAt);

            $startsAt->startOfHour();

            return $startsAt;
        },
        'ends_at'    => function (array $data) use ($faker) {
            $startsAt = $data['starts_at'];

            if ($startsAt instanceof DateTime) {
                $startsAt = new Carbon($startsAt->format('Y-m-d H:i:s'));
            }

            if (is_string($startsAt)) {
                $startsAt = Carbon::parse($startsAt);
            }

            return $startsAt->copy()->addHour();
        },
        'ticket_url' => $faker->url,
    ];
});

$factory->define(RecurrenceSettings::class, function (Faker $faker, $overrides) {
    return [
        'frequency' => 'bi-weekly',
        'email_from_id' => function (array $data) {
            return factory(EmailAddress::class)->create()->id;
        },
        'mailing_date' => Carbon::parse('next thursday'),
        'mailing_subject' => $faker->sentence,
        'persist_subject' => false,
        'mailing_heading' => null,
        'persist_heading' => false,
        'mailing_body' => $faker->paragraph,
        'recipient_group_ids' => [],
        'is_enabled' => true,
        'failed' => false
    ];
});
