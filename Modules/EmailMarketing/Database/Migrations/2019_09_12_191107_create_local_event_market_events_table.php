<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateLocalEventMarketEventsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('local_event_market_events', function (Blueprint $table) {
            $table->uuid('market_uuid');
            $table->uuid('event_uuid');
            $table->timestamps();

            $table->primary(['market_uuid', 'event_uuid'], 'PK_market_events');

            $table->foreign('market_uuid', 'FK_local_events-market')
                ->references('uuid')
                ->on('local_event_markets')
                ->onDelete('cascade');

            $table->foreign('event_uuid', 'FK_local_events-event')
                ->references('uuid')
                ->on('local_event_events')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('local_event_market_events');
    }
}
