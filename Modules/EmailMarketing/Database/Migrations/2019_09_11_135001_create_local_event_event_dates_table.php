<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateLocalEventEventDatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('local_event_event_dates', function (Blueprint $table) {
            $table->uuid('uuid');
            $table->uuid('event_uuid');
            $table->dateTime('starts_at')->index('IX_event_dates-starts_at');
            $table->dateTime('ends_at')->index('IX_event_dates-ends_at');
            $table->string('ticket_url')->nullable();

            $table->timestamps();

            $table->primary('uuid', 'PK_local_event_event_dates');
            $table->foreign('event_uuid', 'FK_local_event_events-dates')
                ->references('uuid')
                ->on('local_event_events');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('local_event_event_dates');
    }
}
