<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddCustomizationFieldsForRecurrences extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('local_events_recurrence_settings', function (Blueprint $table) {
            $table->json("mailing_customizations")->nullable()->default(null)->after('mailing_body');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('local_events_recurrence_settings', function (Blueprint $table) {
            $table->dropColumn("mailing_customizations");
        });
    }
}
