<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddEnabledFieldForLocalEventMarkets extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('local_event_markets', function (Blueprint $table) {
            $table->boolean('enabled')->default(false)->after('suggested_radius');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('local_event_markets', function (Blueprint $table) {
            $table->dropColumn('enabled');
        });
    }
}
