<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRecurringEventUuidToEvents extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('local_event_events', function (Blueprint $table) {
            $table->uuid('recurring_event_uuid')->nullable()->after('uuid');;
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('local_event_events', function (Blueprint $table) {
            $table->dropColumn("recurring_event_uuid");
        });
    }
}
