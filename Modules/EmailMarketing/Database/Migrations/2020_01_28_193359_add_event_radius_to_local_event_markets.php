<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class AddEventRadiusToLocalEventMarkets extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //add the column
        Schema::table('local_event_markets', function (Blueprint $table) {
            $table->integer('event_radius')->comment('Range in miles from this point to include events')->after('timezone');
            $table->integer('suggested_radius')->comment('Range in miles from this point to include agents')->change();
        });

        //populate the data
        DB::table('local_event_markets')->update([
            "event_radius" => DB::RAW("`suggested_radius`")
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //revert the data
        DB::table('local_event_markets')->update([
            "suggested_radius" => DB::RAW("`event_radius`")
        ]);

        //drop the column
        Schema::table('local_event_markets', function (Blueprint $table) {
            $table->dropColumn('event_radius');
            $table->integer('suggested_radius')->comment('Range in miles from this point to include events and agents')->change();

        });
    }
}
