<?php

use App\Models\AccountGroup;
use App\Models\Group;
use App\Models\Permission;
use App\Models\Plan;
use Illuminate\Database\Migrations\Migration;

class AddLocalContentGroupAndPermission extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //make sure the permissions are up to date (should have been done in UpdatePermissionsAndPlansForLocalContent)
        //app(DatabaseSeeder::class)->call(PermissionsPlansAndRolesSeeder::class);

        //Add the group
        $group = Group::firstOrCreate(['slug' => Group::LOCAL_CONTENT_SLUG], [
            'type' => 'account',
            'name' => 'Local Content',
        ]);

        //Add the permission to the group and plan
        /* Permissions will be assigned through the PermissionsPlansAndRolesSeeder
        $permission = Permission::where(['name' => 'local-content.view-sidebar'])->first();
        if ($permission) {
            $group->givePermissionTo($permission);

            //Get the existing plan
            $plan = Plan::find(Plan::PLAN_ID_LOCAL_EVENT);
            if ($plan) {
                $plan->givePermissionTo($permission);
            }
        }
        */
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $group = Group::where('slug', Group::LOCAL_CONTENT_SLUG)->first();
        AccountGroup::where('group_id', $group->id)->delete();
        $group->delete();
    }
}
