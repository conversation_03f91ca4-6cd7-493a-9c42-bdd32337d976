<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddOnDeleteCascadeForEventDateForeignKey extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('local_event_event_dates', function (Blueprint $table) {
            //drop the current foreign key
            $table->dropForeign('FK_local_event_events-dates');

            //readd it with on delete cascade
            $table->foreign('event_uuid', 'FK_local_event_events-dates')
                ->references('uuid')
                ->on('local_event_events')
                ->onDelete('cascade');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('local_event_event_dates', function (Blueprint $table) {
            //drop the current foreign key
            $table->dropForeign('FK_local_event_events-dates');

            //readd it without on delete cascade
            $table->foreign('event_uuid', 'FK_local_event_events-dates')
                ->references('uuid')
                ->on('local_event_events');

        });
    }
}
