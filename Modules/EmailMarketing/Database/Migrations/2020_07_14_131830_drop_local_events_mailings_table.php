<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class DropLocalEventsMailingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('local_events_mailings');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::create('local_events_mailings', function (Blueprint $table) {
            $table->uuid('uuid');
            $table->unsignedInteger('account_id');
            $table->uuid('market_uuid');
            $table->unsignedInteger("email_from_id");
            $table->string("email_from_address");
            $table->string("mailing_subject")->nullable()->default(null);
            $table->text("mailing_body")->nullable()->default(null);
            $table->json("recipient_group_ids");
            $table->dateTime('event_dates_start_at')->index('IX_local_events_mailings-event_dates_start_at');
            $table->dateTime('event_dates_end_at')->index('IX_local_events_mailings-event_dates_end_at');
            $table->dateTime('sent_on')->index('IX_local_events_mailings-sent_on');
            $table->string('featured_image_url')->nullable()->default(null);
            $table->longText('mail_content_html')->nullable()->default(null);
            $table->longText('mail_content_text')->nullable()->default(null);
            $table->unsignedInteger('mailing_service_mailing_id')->nullable()->default(null);
            $table->timestamps();

            $table->primary('uuid');
            $table->foreign('account_id')->references('id')->on('accounts');
        });
    }
}
