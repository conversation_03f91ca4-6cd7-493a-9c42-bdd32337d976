<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateLocalEventsRecurrenceSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('local_events_recurrence_settings', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('account_id')->unique('UQ_account-id');
            $table->string("frequency");
            $table->unsignedInteger("email_from_id");
            $table->datetime("mailing_date");
            $table->string("mailing_subject")->nullable()->default(null);
            $table->text("mailing_body")->nullable()->default(null);
            $table->json("recipient_group_ids");
            $table->boolean("is_enabled")->default(false);
            $table->boolean("failed")->default(false);
            $table->timestamps();

            $table->foreign('account_id')->references('id')->on('accounts');
            $table->foreign('email_from_id')->references('id')->on('email_addresses');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('local_events_recurrence_settings');
    }
}
