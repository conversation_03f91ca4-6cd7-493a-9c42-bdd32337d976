<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateLocalEventEventsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('local_event_events', function (Blueprint $table) {
            $table->uuid('uuid');
            $table->string('name');
            $table->string('description', 256);
            $table->string('image_url')->nullable();
            $table->string('source_url')->nullable();
            $table->json('venue');
            $table->json('flags')->nullable();
            $table->tinyInteger('popularity');

            $table->timestamps();
            $table->primary('uuid', 'PK_local_event_events');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('local_event_events');
    }
}
