<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddCustomerHeaderFieldsForRecurrences extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('local_events_recurrence_settings', function (Blueprint $table) {
            $table->boolean("persist_subject")->default(false)->after('mailing_subject');
            $table->string("mailing_heading")->nullable()->default(null)->after('persist_subject');
            $table->boolean("persist_heading")->default(false)->after('mailing_heading');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('local_events_recurrence_settings', function (Blueprint $table) {
            $table->dropColumn("persist_heading");
            $table->dropColumn("mailing_heading");
            $table->dropColumn("persist_subject");
        });
    }
}
