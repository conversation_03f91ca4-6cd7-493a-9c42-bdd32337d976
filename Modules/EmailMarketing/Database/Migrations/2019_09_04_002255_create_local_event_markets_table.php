<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateLocalEventMarketsTable extends Migration
{
    private $tableName = 'local_event_markets';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create($this->tableName, function (Blueprint $table) {
            $table->uuid('uuid')->comment('Internal UUID');
            $table->uuid('external_uuid')->nullable()->comment('External System ID');
            $table->string('name')->comment('Colloquial Name for this market');
            $table->string('address')->comment('Address this market is centered on');
            $table->decimal('latitude', 10, 8)->comment('Latitude of this market');
            $table->decimal('longitude', 11, 8)->comment('Longitude of this market');
            $table->string('timezone', 30)->comment('Timezone of this market');
            $table->integer('suggested_radius')->comment('Range in miles from this point to include events and agents');
            $table->timestamps();

            $table->primary('uuid');
        });

        $markets = collect(json_decode(file_get_contents(__DIR__ . '/../markets.json'), true))
            ->map(function ($market) {
                $market['updated_at'] = now();
                $market['created_at'] = $market['updated_at'];
                return $market;
            });

        \DB::table($this->tableName)
            ->insert($markets->toArray());
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists($this->tableName);
    }
}
