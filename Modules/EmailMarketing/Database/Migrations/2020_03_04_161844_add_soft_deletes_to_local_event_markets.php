<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddSoftDeletesToLocalEventMarkets extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //add the soft delete
        Schema::table('local_event_markets', function (Blueprint $table) {
            //add the soft delete column
            $table->softDeletes();
        });


        //run the permission seeder
        //app(DatabaseSeeder::class)->call(PermissionsPlansAndRolesSeeder::class);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('local_event_markets', function (Blueprint $table) {
            //Drop the deleted_at column
            $table->dropSoftDeletes();
        });
    }
}
