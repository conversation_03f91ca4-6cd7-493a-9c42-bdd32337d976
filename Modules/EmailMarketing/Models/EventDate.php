<?php

namespace Modules\EmailMarketing\Models;

use App\Traits\HasUuidKey;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class EventDate
 * @package Modules\EmailMarketing\Models
 * @property \Carbon\Carbon $starts_at
 * @property \Carbon\Carbon $ends_at
 */
class EventDate extends Model
{
    use HasUuidKey;

    protected $table = 'local_event_event_dates';

    protected $primaryKey = 'uuid';

    protected $guarded = [];

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at'   => 'datetime'
    ];

    public function event() : BelongsTo
    {
        return $this->belongsTo(Event::class, 'event_uuid');
    }
}
