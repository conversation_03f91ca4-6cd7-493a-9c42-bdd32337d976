<?php

namespace Modules\EmailMarketing\Models;

use App\Context\AccountId;
use App\Traits\HasUuidKey;
use Carbon\Carbon;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\EventCollection;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\MarketEvents;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\Venue;
use Modules\EmailMarketing\Entities\GlobalMarketId;
use Modules\Mailing\Models\Mailing as MailingModel;

/**
 * Class Event
 * @package Modules\EmailMarketing\Models
 * @property bool                                                                                $is_hidden
 * @property array                                                                               $flags
 * @property int                                                                                 $popularity
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\EmailMarketing\Models\EventDate[] $times
 * @method static Event|Builder market($id)
 * @method static Event|Builder startingAfter(Carbon $date)
 * @method static Event|Builder startingBefore(Carbon $date)
 * @method static Event|Builder withAnyFlags(array $flags)
 * @method static Event|Builder inMarketOrGlobal(String $marketUuid)
 * @method static Event|Builder forEnabledMarkets(iterable $marketIds)
 */
class Event extends Model
{
    use HasUuidKey;

    //TODO - change all the other flags to constants as well
    //Flags based on the 'cancelled' status of imported events
    const FLAG_STATUS_CANCELLED          = "status_cancelled";
    const FLAG_STATUS_POSTPONED_TBD      = "status_postponed_tbd";
    const FLAG_STATUS_POSTPONED_UPDATED  = "status_postponed_date_updated";
    const FLAG_STATUS_CHANGED_TO_VIRTUAL = "status_changed_to_virtual";
    const FLAG_STATUS_SOLD_OUT           = "status_sold_out";
    const FLAG_STATUS_UNKNOWN            = "status_unknown";

    //Flags for events sent from OG
    const EVENT_FLAG_DONT_MISS          = 'dont_miss_event';
    const EVENT_FLAG_LOCAL_GEM          = 'local_gem';
    const EVENT_FLAG_TOURING            = 'touring';
    const EVENT_FLAG_ANNUAL             = 'annual';
    const EVENT_FLAG_VIRTUAL            = 'virtual';
    const EVENT_FLAG_IN_PERSON          = 'in_person';
    const EVENT_FLAG_LOCAL              = 'local';
    const EVENT_FLAG_LOCAL_VIRTUAL      = 'local_virtual';
    const EVENT_FLAG_GLOBAL_VIRTUAL     = 'global_virtual';
    const EVENT_FLAG_COVID_PRECAUTIONS  = 'stated_covid_precautions';

    const ACCEPTABLE_FLAGS = [
        self::EVENT_FLAG_DONT_MISS,
        self::EVENT_FLAG_LOCAL_GEM,
        self::EVENT_FLAG_TOURING,
        self::EVENT_FLAG_ANNUAL,
        self::EVENT_FLAG_VIRTUAL,
        self::EVENT_FLAG_IN_PERSON,
        self::EVENT_FLAG_LOCAL_VIRTUAL,
        self::EVENT_FLAG_GLOBAL_VIRTUAL
    ];

    const STATUS_FLAGS = [
        self::FLAG_STATUS_CANCELLED,
        self::FLAG_STATUS_POSTPONED_TBD,
        self::FLAG_STATUS_POSTPONED_UPDATED,
        self::FLAG_STATUS_CHANGED_TO_VIRTUAL,
        self::FLAG_STATUS_SOLD_OUT,
        self::FLAG_STATUS_UNKNOWN
    ];

    const LONG_DESCRIPTION_SUFFIX = ' ...';

    /** @var int MINIMUM_NUMBER_OF_EVENTS -> the minimum number of events that should be included for a mailing */
    const MINIMUM_NUMBER_OF_EVENTS = 8;

    protected $table = 'local_event_events';

    protected $primaryKey = 'uuid';

    protected $guarded = [];

    protected $casts = [
        'venue'     => 'json',
        'flags'     => 'json',
        'is_hidden' => 'boolean',
    ];

    public function times() : HasMany
    {
        return $this->hasMany(EventDate::class)
            ->orderBy('starts_at');
    }

    protected function getVenueAttribute()
    {
        return new Venue(json_decode($this->attributes['venue'], true));
    }

    public function markets() : BelongsToMany
    {
        return $this->belongsToMany(
            Market::class,
            'local_event_market_events',
            'event_uuid',
            'market_uuid',
            'uuid',
            'uuid'
        )->withTimestamps();
    }

    public function hasAllFlags(array $flags) : bool
    {
        return collect($flags)->diff(collect($this->flags))->count() == 0;
    }

    public function hasAnyFlags(array $flags) : bool
    {
        return collect($flags)->intersect(collect($this->flags))->count() > 0;
    }

    /**
     * Cleans up the description before saving it to the DB
     *
     * @param string|null $description
     */
    protected function setDescriptionAttribute(?string $description) : void
    {
        if (! $description) {
            $this->attributes['description'] = null;
            return;
        }

        // Remove any double spaces or new line characters.
        $description = trim(preg_replace("/\s+/", " ", $description));

        // Add ellipsis if description is too long
        // This used to be done by OG but they removed their `summary` field, so we now have to do it.
        if (strlen($description) > 256) {
            // truncates the text
            $description = mb_substr(
                $description,
                0,
                (256 - strlen(self::LONG_DESCRIPTION_SUFFIX))
            );

            // prevents last word truncation
            $description = trim(mb_substr($description, 0, mb_strrpos($description, " ")))
                . self::LONG_DESCRIPTION_SUFFIX;
        }

        $this->attributes['description'] = $description;
    }

    public function scopeFuzzySearch(Builder $query, $value)
    {
        $query->where(function (Builder $query) use ($value) {
            $likeValue = "%$value%";

            $query->where('local_event_events.uuid', 'like', $likeValue)
                ->orWhere('local_event_events.name', 'like', $likeValue)
                ->orWhereJsonContains('local_event_events.flags', $value);
        });
    }

    public function scopeMarket(Builder $query, $value) : void
    {
        if (request()->is('graphql') && is_array($value)) {
            $value = Arr::get($value, 'market');
        }

        if (! $value) {
            return;
        }

        $query->whereHas('markets', function ($query) use ($value) {
            $query->where('uuid', $value);
        });
    }

    public function scopeForEnabledMarkets(Builder $query, iterable $marketIds) : void
    {
        $query->whereHas('markets', function (Builder $query) use ($marketIds) {
            $query->whereIn('uuid', $marketIds);
            $query->where('enabled', true);
        });
    }

    public function scopeInMarketOrGlobal(Builder $query, $value) : void
    {
        if (request()->is('graphql') && is_array($value)) {
            $value = Arr::get($value, 'market');
        }

        if (! $value) {
            return;
        }

        $query->forEnabledMarkets([
            app(GlobalMarketId::class)->getValue()->toString(),
            $value
        ]);
    }

    public function scopeStartingAfter(Builder $query, $value) : void
    {
        $query->whereHas('times', function ($query) use ($value) {
            $query->where('starts_at', '>=', $value);
        });
    }

    public function scopeStartingBefore(Builder $query, $value) : void
    {
        $query->whereHas('times', function ($query) use ($value) {
            $query->where('starts_at', '<=', $value);
        });
    }

    public function scopeEndingNoLaterThan(Builder $query, DateTimeInterface $value) : void
    {
        $query->whereHas('times', function ($query) use ($value) {
            $query->havingRaw('MAX(ends_at) < ?', [$value])->groupBy('event_uuid');
        });
    }

    public function scopeWithAnyFlags(Builder $query, array $flags) : void
    {
        $query->where(function (Builder $query) use ($flags) {
            $query->whereJsonContains('flags', array_shift($flags));
            foreach ($flags as $flag) {
                $query->orWhereJsonContains('flags', $flag);
            }
        });
    }

    public static function getForMailing(Market $market, Carbon $start, Carbon $end, int $accountId = null) : MarketEvents
    {
        // Get the events for the given date ranges and location, avoid duplicated events
        $events = self::getForMarketAndDateRange($market, $start, $end);

        if ($events->isEmpty()) {
            //No events found, so return an empty MarketEventsCollection
            return new MarketEvents(
                $market,
                null,
                EventCollection::make(collect())
            );
        }

        $previousFeaturedEvents = self::getFeaturedEventsFromPreviousMailings($accountId);

        // Get the featured event
        $featuredEvent = $events->filter(function (Event $event) {
            return strpos($event->name, 'Brad G') !== false || in_array(self::EVENT_FLAG_DONT_MISS, $event->flags)
                && in_array(self::EVENT_FLAG_LOCAL_GEM, $event->flags)
                && in_array(self::EVENT_FLAG_IN_PERSON, $event->flags);
        })
            ->when($previousFeaturedEvents->isNotEmpty(), function ($events) use ($previousFeaturedEvents) {
                return self::filterPreviousFeaturedEvent($events, $previousFeaturedEvents);
            })
            ->sortByDesc('popularity')
            ->first();

        //the number of events the first set should contain
        $firstSetLimit = 6;

        //the minimum number of events the result should contain
        $minimumNumberOfEvents = self::MINIMUM_NUMBER_OF_EVENTS;

        // Remove featured event if found
        // If we don't find it, we will remove it from the sets below after we obtain them.
        if ($featuredEvent) {
            $events = $events->reject(function (Event $event) use ($featuredEvent) {
                return $event->uuid == $featuredEvent->uuid;
            })
                ->values();

            // Deduct one since we found a featured event.
            $firstSetLimit--;
            $minimumNumberOfEvents--;
        }

        // Each set is sorted based on flag and popularity.

        // First set consists of in person events occurring between the thursday of send to the following sunday
        $firstSet = $events->filter(function (Event $event) use ($start) {
            return $event->times->filter(function (EventDate $eventDate) use ($start) {
                return $eventDate->starts_at->isBetween(
                    $start,
                    $start->copy()->modify('next sunday')->endOfDay()
                );
            });
        })
            ->when($market->local_uuid->getValue() != app(GlobalMarketId::class)->getValue(), function ($events) {
                //when not in the Global Virtual market, filter out events that are not in person
                return $events->filter(function (Event $event) {
                    return in_array(self::EVENT_FLAG_IN_PERSON, $event->flags);
                });
            })
            ->sort(static::sortEventByFlagsAndPopularity())
            ->take($firstSetLimit);

        $events = $events->reject(function (Event $event) use ($firstSet) {
            return $firstSet->pluck('uuid')->contains($event->uuid);
        });

        // Second set consists of in person events from the Monday following send, thru the following thursday
        $secondSet = $events->filter(function (Event $event) use ($start) {
            return $event->times->filter(function (EventDate $eventDate) use ($start) {
                return $eventDate->starts_at->isBetween(
                    $start->copy()->modify('next Monday'),
                    $start->copy()->modify('next Thursday')->endOfDay()
                );
            });
        })
            ->when($market->local_uuid->getValue() != app(GlobalMarketId::class)->getValue(), function ($events) {
                //when not in the Global Virtual market, filter out events that are not in person
                return $events->filter(function (Event $event) {
                    return in_array(self::EVENT_FLAG_IN_PERSON, $event->flags);
                });
            })
            ->sort(static::sortEventByFlagsAndPopularity())
            ->take(3);

        $events = $events->reject(function (Event $event) use ($secondSet) {
            return $secondSet->pluck('uuid')->contains($event->uuid);
        });

        // Third set consists of in person events from the second Friday following send, thru the following Sunday
        $thirdSet = $events->filter(function (Event $event) use ($start) {
            return $event->times->filter(function (EventDate $eventDate) use ($start) {
                return $eventDate->starts_at->isBetween(
                    $start->copy()->modify('second Friday'),
                    $start->copy()->modify('second Sunday')->endOfDay()
                );
            });
        })
            ->when($market->local_uuid->getValue() != app(GlobalMarketId::class)->getValue(), function ($events) {
                //when not in the Global Virtual market, filter out events that are not in person
                return $events->filter(function (Event $event) {
                    return in_array(self::EVENT_FLAG_IN_PERSON, $event->flags);
                });
            })
            ->sort(static::sortEventByFlagsAndPopularity())
            ->take(3);

        $events = $events->reject(function (Event $event) use ($thirdSet) {
            return $thirdSet->pluck('uuid')->contains($event->uuid);
        });

        // Fourth set consists of in person events from the second Monday following send, thru the following Wednesday
        $fourthSet = $events->filter(function (Event $event) use ($start) {
            return $event->times->filter(function (EventDate $eventDate) use ($start) {
                return $eventDate->starts_at->isBetween(
                    $start->copy()->modify('second Monday'),
                    $start->copy()->modify('second Wednesday')->endOfDay()
                );
            });
        })
            ->when($market->local_uuid->getValue() != app(GlobalMarketId::class)->getValue(), function ($events) {
                //when not in the Global Virtual market, filter out events that are not in person
                return $events->filter(function (Event $event) {
                    return in_array(self::EVENT_FLAG_IN_PERSON, $event->flags);
                });
            })
            ->sort(static::sortEventByFlagsAndPopularity())
            ->take(1);

        $events = $events->reject(function (Event $event) use ($fourthSet) {
            return $fourthSet->pluck('uuid')->contains($event->uuid);
        });

        // Merge all the events sets
        $finalEvents = $firstSet->merge($secondSet)
            ->merge($thirdSet)
            ->merge($fourthSet);

        // If we do not have a featured event yet, sort by popularity, and take the first
        if (! $featuredEvent) {
            $finalEvents = $finalEvents
                ->when($previousFeaturedEvents->isNotEmpty(), function ($events) use ($previousFeaturedEvents) {
                    return self::filterPreviousFeaturedEvent($events, $previousFeaturedEvents);
                })
                ->sort(static::sortEventByFlagsAndPopularity());

            $featuredEvent = $finalEvents->shift();

            //reduce the minimum number of events by 1
            $minimumNumberOfEvents--;
        }

        //Fill the remaining slots up to 8 events total (including the featured event)
        //If we are here, it means at least 1 event was returned in the original query,
        // so we are guaranteed to have a featured event.
        //Therefore, we need to check how many other events ($finalEvents count) there are
        // If less than 7/8 ($minimumNumberOfEvents), we need to pad the event with:
        //      A - leftovers from the date range checks / filters
        //      B - events from the next mailing period

        //Leftover events in date range
        if ($finalEvents->count() < $minimumNumberOfEvents && $events->count() > 0) {
            //take from the leftovers
            $numMissing = $minimumNumberOfEvents - $finalEvents->count();
            $leftOverEvents = $events->sort(static::sortEventByFlagsAndPopularity())->take($numMissing);

            //merge the leftovers into the final events
            $finalEvents = $finalEvents->merge($leftOverEvents);
        }

        //Still below  the minimum, get other events outside of date range
        if ($finalEvents->count() < $minimumNumberOfEvents) {
            //we're still short the total number of events. Try to get from the next period
            $nextPeriodStart = $start->copy()->addWeeks(2)->startOfDay();
            $nextPeriodEnd = $end->copy()->addWeeks(2)->endOfDay();
            $nextPeriodEvents = self::getForMarketAndDateRange($market, $nextPeriodStart, $nextPeriodEnd);

            //if there's event, sort, take, and merge
            if ($nextPeriodEvents->isNotEmpty()) {
                //take from the other period - rejecting any that were returned for the current period,
                // or is the current featured event, sorting by flags and popularity
                $numMissing = $minimumNumberOfEvents - $finalEvents->count();
                $otherEvents = $nextPeriodEvents->reject(function (Event $event) use ($finalEvents, $featuredEvent) {
                    return $finalEvents->pluck('uuid')->contains($event->uuid) ||
                        ($featuredEvent && $featuredEvent->uuid == $event->uuid);
                })
                    ->sort(static::sortEventByFlagsAndPopularity())
                    ->take($numMissing);

                //merge the next period events into the final events
                $finalEvents = $finalEvents->merge($otherEvents);
            }
        }

        //Final check for featured event - If we still do not have a featured event, sort by popularity, and take the first
        if (! $featuredEvent) {
            $finalEvents = $finalEvents
                ->when($previousFeaturedEvents->isNotEmpty(), function ($events) use ($previousFeaturedEvents) {
                    return self::filterPreviousFeaturedEvent($events, $previousFeaturedEvents);
                })
                ->sort(static::sortEventByFlagsAndPopularity());
            $featuredEvent = $finalEvents->shift();
        }

        //sort the remaining events by date
        $finalEvents = $finalEvents->sortBy(function (Event $event) {
            /** @var \Modules\EmailMarketing\Models\EventDate $startTime */
            $startTime = $event->times->first();

            return $startTime->starts_at
                ->timestamp;
        });

        //return the market events collection
        return new MarketEvents(
            $market,
            $featuredEvent,
            EventCollection::make($finalEvents)
        );
    }

    public static function getForMarketAndDateRange(Market $market, Carbon $start, Carbon $end) : Collection
    {
        // Get the events for the given date ranges and location
        $events = static::inMarketOrGlobal($market->local_uuid->getId()->toString())
            ->startingAfter($start)
            ->startingBefore($end)
            ->withAnyFlags(self::ACCEPTABLE_FLAGS)
            ->where(function (Builder $builder) {
                $builder->where('flags', '!=', '["local"]');
            })
            ->where('is_hidden', false)
            ->with('times')
            ->get();

        //filter the events by recurring event
        $events = self::filterUniqueEventsAndTimesByRecurringEvent($events, $start, $end);

        //filter based on venue
        $events = self::filterUniqueEventsAndTimesByVenue($events, $start, $end);

        //return the filtered events
        return $events;
    }

    private static function filterUniqueEventsAndTimesByRecurringEvent(Collection $events, Carbon $start, Carbon $end): Collection
    {
        $eventsGroupedByRecurringEvent = $events->groupBy(function(Event $event) {
            //if the recurring event uuid is not null, use that, otherwise, use the event uuid
            return ($event->recurring_event_uuid && "" != $event->recurring_event_uuid)
                ? $event->recurring_event_uuid
                : $event->uuid;
        });

        return self::filterUniqueEventsByTimes($eventsGroupedByRecurringEvent, $start, $end);
    }

    private static function filterUniqueEventsAndTimesByVenue(Collection $events, Carbon $start, Carbon $end): Collection
    {
        $eventsGroupedByVenue = $events->groupBy(function(Event $event) {
            //group them by a unique key of name and some venue details
            $venueDetails = Arr::only($event->venue->toArray(), ['name', 'latitude', 'longitude']);

            //since that might be a long string, we can encode the key so it's a little shorter
            return $event->name . '|' . json_encode($venueDetails);
        });

        return self::filterUniqueEventsByTimes($eventsGroupedByVenue, $start, $end);
    }

    private static function filterUniqueEventsByTimes(Collection $events, Carbon $start, Carbon $end): Collection
    {
        return $events->map(function ($group) use ($start, $end) {
                //each item in the group a collection of events with the same event/venue
                //so we need to sort the group by event date, and grab the first event only
                return $group->sortBy(function (Event $event) use ($start, $end) {
                    //filter out any event dates that occur outside the date range
                    return $event->times->filter(function (EventDate $eventDate) use ($start, $end) {
                        return $eventDate->starts_at->isBetween(
                            $start,
                            $end
                        );
                    })
                        //sort the remaining dates by starts_at
                        ->sortBy('starts_at')
                        //take the first (earliest) one
                        ->first()
                        //and return the starts_at values as the value to sort the event with
                        ->starts_at;
                })
                    //take only the values of the sorted collection
                    ->values()
                    //return the first one as the only event to use from the duplicates
                    ->first();
            })
            ->flatten()
            ->values();
    }

    /**
     * Determine the value of the virtual or local flag combination in order to sort correctly.
     * All values should be in the 100's place or higher (10's place is for flags, ones place is used for popularity)
     * @return int
     */
    private function getLocalOrVirtualValue() : int
    {
        if ($this->hasAllFlags([self::EVENT_FLAG_IN_PERSON])) {
            return 300;
        } elseif ($this->hasAllFlags([self::EVENT_FLAG_LOCAL_VIRTUAL])) {
            return 200;
        } elseif ($this->hasAllFlags([self::EVENT_FLAG_GLOBAL_VIRTUAL])) {
            return 100;
        } else {
            return 0;
        }
    }

    /**
     * Determine the value of the flag combination in order to sort correctly.
     * All values should be in the 10's place or higher (ones place is used for popularity)
     * @return int
     */
    private function getFlagValue() : int
    {
        if ($this->hasAllFlags([self::EVENT_FLAG_LOCAL, self::EVENT_FLAG_DONT_MISS])) {
            return 40;
        } elseif ($this->hasAllFlags([self::EVENT_FLAG_LOCAL_GEM, self::EVENT_FLAG_LOCAL])) {
            return 30;
        } elseif ($this->hasAllFlags([self::EVENT_FLAG_DONT_MISS])) {
            return 20;
        } elseif ($this->hasAllFlags([self::EVENT_FLAG_LOCAL_GEM])) {
            return 10;
        } else {
            return 0;
        }
    }

    private static function sortEventByFlagsAndPopularity()
    {
        return function (Event $current, Event $next) {
            $currentFlagValue = $current->getLocalOrVirtualValue() + $current->getFlagValue() + $current->popularity;
            $nextFlagValue = $next->getLocalOrVirtualValue() + $next->getFlagValue() + $next->popularity;

            if ($currentFlagValue == $nextFlagValue) {
                return 0;
            }

            return ($currentFlagValue < $nextFlagValue)
                ? 1
                : -1;
        };
    }

    private static function getFeaturedEventsFromPreviousMailings(int $accountId = null) : Collection
    {
        //If no account id is provided, use the account id in the context
        if (null == $accountId) {
            $accountId = optional(AccountId::current())->id();
        }

        //If we still don't have an account id, return an empty collection
        if (null == $accountId) {
            return collect();
        }

        //initialize the collection to hold the featured events
        $featuredEvents = collect();

        //Get the LE mailings for the account
        $mailings = MailingModel::allAccounts()
            ->where('account_id', $accountId)
            ->forLocalEvents()
            ->where('state', 'sent')
            ->get();

        //get the featured events from the mailing properties
        $mailings->each(function (MailingModel $mailing) use ($featuredEvents) {
            $featuredEvent = Arr::get($mailing->getProperties(), 'featured_event', null);

            if ($featuredEvent) {
                $featuredEvents->push($featuredEvent);
            }
        });

        //return the collection
        return $featuredEvents;
    }

    private static function filterPreviousFeaturedEvent(Collection $currentEvents, Collection $previousFeaturedEvents): Collection
    {
        return $currentEvents->filter(function (Event $event) use ($previousFeaturedEvents) {
            //check if there's any matches to previous events
            $previousMatches = $previousFeaturedEvents->filter(function (array $previousEvent) use ($event) {
                //build the data to compare
                $previousVenueData = [
                    'name' => $previousEvent['venue_name'],
                    'latitude' => $previousEvent['venue_latitude'],
                    'longitude' => $previousEvent['venue_longitude']
                ];

                $previousString = $previousEvent['event_name'] . '|' . json_encode($previousVenueData);

                //group them by a unique key of name and some venue details
                $eventVenueDetails = Arr::only($event->venue->toArray(), ['name', 'latitude', 'longitude']);

                //since that might be a long string, we can encode the key so it's a little shorter
                $eventString = $event->name . '|' . json_encode($eventVenueDetails);

                //filter out any previous events that match the current ones
                return $previousEvent['event_uuid'] == $event->uuid
                    || $previousEvent['event_recurring_uuid'] == $event->recurring_event_uuid
                    || $previousString == $eventString;
            });

            //if there are no previous matches, we want to keep the event
            return $previousMatches->isEmpty();
        });
    }

    public function toggleVisibility() : self
    {
        $this->is_hidden = ! $this->is_hidden;

        return $this;
    }
}
