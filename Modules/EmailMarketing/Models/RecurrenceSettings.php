<?php

namespace Modules\EmailMarketing\Models;

use App\Models\EmailAddress;
use App\Traits\BelongsToAccount;
use Carbon\Carbon;
use DateTimeZone;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Infrastructure\Audit\Traits\RmcAuditable;
use OwenIt\Auditing\Contracts\Auditable;

/**
 * Class RecurrenceSettings
 * @package Modules\EmailMarketing\Models
 * @property \App\Models\Account $account
 * @property \Carbon\Carbon      $mailing_date
 * @method static Builder|self enabled()
 * @property int                 $id
 * @property int                 $frequency
 * @property int                 $email_from_id
 * @property bool                $is_enabled
 * @property bool                $persist_heading
 * @property string              $mailing_subject
 * @property string|null         $mailing_heading
 * @property string|null         $mailing_body
 * @property bool                $persist_subject
 * @property int[]|string[]|array         $recipient_group_ids
 * @property array               $mailing_customizations
 */
class RecurrenceSettings extends Model implements Auditable
{
    use BelongsToAccount, RmcAuditable;

    protected $table = 'local_events_recurrence_settings';

    protected $guarded = [];

    protected $casts = [
        'email_from_id'          => 'int',
        'recipient_group_ids'    => 'json',
        'is_enabled'             => 'boolean',
        'mailing_date'           => 'datetime',
        'failed'                 => 'boolean',
        'persist_subject'        => 'boolean',
        'persist_heading'        => 'boolean',
        'mailing_customizations' => 'array',
    ];

    protected $attributes = [
        'persist_subject'        => false,
        'mailing_heading'        => null,
        'persist_heading'        => false,
        'is_enabled'             => false,
        'failed'                 => false,
    ];

    public function emailFromAddress() : HasOne
    {
        return $this->hasOne(EmailAddress::class, 'id', 'email_from_id');
    }

    public function getNextMailingDate() : Carbon
    {
        // todo: use $this->frequency to calculate next mailing date
        return $this->mailing_date->copy()->addWeeks(2);
    }

    protected function getMailingCustomizationsAttribute($value) : array
    {
        //decode the value
        $value = json_decode($value, true);

        return ! is_array($value)
            ? []
            : $value;
    }

    protected function getRecipientGroupIdsAttribute($value) : array
    {
        //decode the value
        $value = json_decode($value, true);

        return ! is_array($value)
            ? []
            : $value;
    }

    public function scopeEnabled(Builder $builder, bool $isEnabled = true) : void
    {
        $builder->where('is_enabled', $isEnabled);
    }

    public static function scopeFailed(Builder $builder, bool $failed = true) : Builder
    {
        return $builder->where('failed', $failed);
    }

    public static function scopeSendingBetween(Builder $builder, Carbon $start, Carbon $end) : Builder
    {
        return $builder->whereBetween('mailing_date', [$start, $end]);
    }

    /**
     * Calculates the events date range start for a mailing
     * @param Carbon $mailingDate - for now, we base it on the mailing date
     * @param DateTimeZone $locationTimezone - the time zone for the location the events are coming from
     * @return Carbon
     */
    public static function getEventsStartDate(Carbon $mailingDate, DateTimeZone $locationTimezone) : Carbon
    {
        //make a copy of the mailing date
        $startDate = $mailingDate->copy();

        //convert to the local time zone
        $startDate->setTimezone($locationTimezone);

        //set the time to start of date
        $startDate->startOfDay();

        //revert to the app timezone
        $startDate->setTimezone(config('app.timezone'));

        //return the result
        return $startDate;
    }

    /**
     * Calculates the events date range end for a mailing.
     * Uses the mailing date as a start.
     * Frequency is fixed to bi-weekly, so we don't check it
     * @param Carbon $mailingDate - the mailing date
     * @param DateTimeZone $locationTimezone - the time zone for the location the events are coming from
     * @param String $frequency - the frequency of emails
     * @return Carbon
     */
    public static function getEventsEndDate(Carbon $mailingDate, DateTimeZone $locationTimezone, String $frequency) : Carbon
    {
        //start by getting the start date
        $endDate = self::getEventsStartDate($mailingDate, $locationTimezone);

        //convert to the local time zone
        $endDate->setTimezone($locationTimezone);

        //add 2 weeks
        // todo: eventually use $frequency to calculate end date
        $endDate->addWeeks(2);

        //remove 1 day, and set the time to 23:59:59
        $endDate->subDays(1)->endOfDay();

        //revert to the app timezone
        $endDate->setTimezone(config('app.timezone'));

        //return the result
        return $endDate;
    }

    public static function getLocalEndDate(Carbon $mailingDate, DateTimeZone $locationTimezone): Carbon
    {
        //make a copy of the mailing date
        $endDate = $mailingDate->copy();

        //convert to the local time zone
        $endDate->setTimezone($locationTimezone);

        //remove 1 day, and set the time to 23:59:59
        $endDate->subDay()->endOfDay();

        //revert to the app timezone
        $endDate->setTimezone(config('app.timezone'));

        //return the result
        return $endDate;
    }
}
