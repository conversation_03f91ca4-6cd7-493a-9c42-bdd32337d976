<?php

namespace Modules\EmailMarketing\Models;

use App\Models\Account;
use App\Models\Location;
use App\Models\Plan;
use App\Scopes\BelongsToAccountScope;
use App\Traits\HasUuidKey;
use DateTimeZone;
use Elasticsearch\Client;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Infrastructure\Audit\Traits\RmcAuditable;
use Lara<PERSON>\Scout\Builder as ScoutBuilder;
use Lara<PERSON>\Scout\Searchable;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\Geocode;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\Market as EntityMarket;
use Modules\EmailMarketing\Entities\MarketId;
use OwenIt\Auditing\Contracts\Auditable;

/**
 * Class Location
 * @package Modules\EmailMarketing\Models
 * @property null|MarketId $external_uuid
 * @property null|MarketId $local_uuid
 * @property int $suggested_radius
 * @property string $name
 */
class Market extends Model implements Auditable
{
    use HasUuidKey, Searchable, SoftDeletes, RmcAuditable;

    const FACTOR_KILOMETERS = 6371;
    const FACTOR_MILES = 3961;

    protected $primaryKey = 'uuid';

    protected $table = 'local_event_markets';

    protected $guarded = [];

    protected $casts = [
        'latitude' => 'float',
        'longitude' => 'float',
        'event_radius' => 'int',
        'suggested_radius' => 'int',
        'enabled' => 'boolean'
    ];

    protected $dates = ['synchronized_at'];

    protected function getLocalUuidAttribute() : MarketId
    {
        return MarketId::fromString($this->attributes['uuid']);
    }

    protected function getExternalUuidAttribute() : ?MarketId
    {
        $externalUuid = $this->attributes['external_uuid'];

        if (! $externalUuid) {
            return null;
        }

        return MarketId::fromString($externalUuid);
    }

    protected function getTimezoneAttribute(): ?DateTimeZone
    {
        return $this->attributes['timezone']
            ? new DateTimeZone($this->attributes['timezone'])
            : null;
    }

    protected function setTimezoneAttribute(?DateTimeZone $timeZone)
    {
        $this->attributes['timezone'] = $timeZone
            ? $timeZone->getName()
            : $timeZone;
    }

    public static function createFromEntity(EntityMarket $market) : Market
    {
        //Build the array of update or create params
        $updateOrCreateParams = [
            'latitude'         => $market->latitude,
            'longitude'        => $market->longitude,
            'address'          => $market->address,
        ];

        //if timezone is set in the market, add the param
        if ($market->timezone) {
            $updateOrCreateParams['timezone'] = $market->timezone;
        }

        /** @var \Modules\EmailMarketing\Models\Market $marketModel */
        $marketModel = self::updateOrCreate(
            [
                'external_uuid'    => $market->uuid,
            ],
            $updateOrCreateParams
        );

        // Only update the name, timezone, and radii if it's new
        if ($marketModel->wasRecentlyCreated) {
            $marketModel->update([
                'name'             => $market->name,
                'event_radius' => $market->radius,
                'suggested_radius' => $market->radius,

            ]);
        }

        return $marketModel;
    }

    public function toSearchableArray()
    {
        $record = $this->toArray();
        $record['market'] = [
            'type' => 'circle',
            'coordinates' => [$this->longitude, $this->latitude],
            'radius' => "{$this->suggested_radius}mi"
        ];

        return Arr::except($record, ['created_at', 'updated_at', 'latitude', 'longitude']);
    }

    public function getGeocode() : Geocode
    {
        return new Geocode($this->latitude, $this->longitude);
    }

    public function accounts() : HasMany
    {
        return $this->hasMany(Account::class, 'market_uuid');
    }

    public function enrolledAccounts() : HasMany
    {
        return $this->accounts()
            ->whereHas('plans', function (Builder $query) {
                $query->where('plan_id', Plan::PLAN_ID_LOCAL_EVENT);
            });
    }

    public function scopeFuzzySearch(Builder $query, $value)
    {
        $value = "%$value%";

        $query->where(function ($query) use ($value) {
            $query->where('uuid', 'like', $value)
                ->orWhere('name', 'like', $value)
                ->orWhere('address', 'like', $value);
        });
    }

    public function scopeEnabled(Builder $builder, bool $isEnabled = true) : void
    {
        $builder->where('enabled', $isEnabled);
    }

    public function containedLocations() : Collection
    {
        $latitude  = $this->latitude;
        $longitude = $this->longitude;
        $distance  = $this->suggested_radius;
        $radius    = self::FACTOR_MILES;

        $deltaLatitude = ($distance / $radius);
        $deltaLongitude = ($distance / (cos(deg2rad($latitude)) * $radius));

        $firstCut = Location::query()
            ->withoutGlobalScopes([BelongsToAccountScope::class])
            ->primaryOnly()
            ->whereBetween('latitude', [
                $latitude - rad2deg($deltaLatitude),
                $latitude + rad2deg($deltaLatitude),
            ])
            ->whereBetween('longitude', [
                $longitude - rad2deg($deltaLongitude),
                $longitude + rad2deg($deltaLongitude)
            ])
            ->get();

        return $firstCut->filter(function (Location $location) use ($radius) {
            return $this->suggested_radius >= $this->getGeocode()
                    ->getDistanceFromAnotherGeocode($location->getGeocode());
        });
    }

    public static function forGeocode(Geocode $geocode, ?string $extraQuery = '') : ScoutBuilder
    {
        return static::search($extraQuery, function (Client $es, $query, $options) use ($geocode) {
            $options = array_merge_recursive($options, [
                'body' => [
                    'query' => [
                        "bool" => [
                            "must" => [
                                [
                                    "geo_shape" => [
                                        "market" => [
                                            "shape"    => [
                                                "type"        => "point",
                                                "coordinates" => [
                                                    $geocode->getLongitude(),
                                                    $geocode->getLatitude(),
                                                ],
                                            ],
                                            "relation" => "contains",
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ]);

            return $es->search($options);
        });
    }

    public function events() : BelongsToMany
    {
        return $this->belongsToMany(
            Event::class,
            'local_event_market_events',
            'market_uuid',
            'event_uuid',
            'uuid',
            'uuid'
        )->withTimestamps();
    }

    public function filteredEvents() : BelongsToMany
    {
        return $this->events()->where(function (Builder $builder) {
            $flags = Event::ACCEPTABLE_FLAGS;
            $firstFlag = array_shift($flags);

            $builder->whereJsonContains('flags', $firstFlag);

            foreach ($flags as $flag) {
                $builder->orWhereJsonContains('flags', $flag);
            }
        })
            ->where(function (Builder $builder) {
                $builder->where('flags', '!=', '["local"]');
            });
    }
}
