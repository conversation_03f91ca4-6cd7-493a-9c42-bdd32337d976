<?php

namespace Modules\EmailMarketing\Notifications;

use App\TeamsNotification\Section;
use App\TeamsNotification\TeamsChannel;
use App\TeamsNotification\TeamsMessage;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Modules\EmailMarketing\OccasionGenius\Entities\Event;
use Throwable;

class EventFailedToParseNotification extends Notification
{
    use Queueable;

    /** @var \Modules\EmailMarketing\OccasionGenius\Entities\Event */
    private $event;

    /** @var \Throwable */
    private $exception;

    public function __construct(Event $event, Throwable $exception)
    {
        //
        $this->event = $event;
        $this->exception = $exception;
    }

    public function via()
    {
        return [TeamsChannel::class];
    }

    public function toMicrosoftTeamsMessage()
    {
        return TeamsMessage::create('Event Failed to Parse')
            ->newSection(
                (new Section)
                    ->title(get_class($this->exception))
                    ->subtitle($this->exception->getMessage())
                    ->addFact('Event UUID', $this->event->uuid)
            );
    }
}
