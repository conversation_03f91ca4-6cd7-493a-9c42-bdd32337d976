<?php

namespace Modules\EmailMarketing\Tests\Feature;

use Carbon\Carbon;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Event as EventFacade;
use Mockery;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\Event;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\EventCollection;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\EventDate;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\MarketCollection;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\Venue;
use Modules\EmailMarketing\Domain\LocalEvents\Events\FailedToLoadEventsForMarket;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\EventRepositoryInterface;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\LocationRepositoryInterface;
use Modules\EmailMarketing\Entities\GlobalMarketId;
use Modules\EmailMarketing\Entities\MarketId;
use Modules\EmailMarketing\Jobs\SyncEventsForMarket;
use Modules\EmailMarketing\Jobs\SyncMarkets;
use Modules\EmailMarketing\Models\Event as EloquentEvent;
use Modules\EmailMarketing\Models\EventDate as EloquentEventDate;
use Modules\EmailMarketing\Models\Market as EloquentMarket;
use Modules\EmailMarketing\OccasionGenius\Enums\VirtualRule;
use Ramsey\Uuid\Uuid;
use Tests\RefreshDatabase;
use Tests\TestCase;

class EventSyncJobTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /** @var \Mockery\LegacyMockInterface|\Mockery\MockInterface|\Modules\EmailMarketing\Domain\LocalEvents\Repositories\EventRepositoryInterface */
    private $eventsRepo;

    /** @var \Mockery\LegacyMockInterface|\Mockery\MockInterface|\Modules\EmailMarketing\Domain\LocalEvents\Repositories\LocationRepositoryInterface */
    private $locationsRepo;

    /** @var \Modules\EmailMarketing\Entities\GlobalMarketId */
    private $globalMarketId;

    protected function setUp(): void
    {
        parent::setUp();

        $this->eventsRepo = Mockery::mock(EventRepositoryInterface::class);
        $this->locationsRepo = Mockery::mock(LocationRepositoryInterface::class);
        $this->globalMarketId = $this->app->make(GlobalMarketId::class);
    }

    /** @test */
    public function it_successfully_saves_events_from_external_source_and_associates_them_to_the_location()
    {
        /** @var \Modules\EmailMarketing\Models\Market $market */
        $market = factory(EloquentMarket::class)->create();
        $marketId = MarketId::fromString($market->uuid);

        $startAt = Carbon::now();
        $endAt = $startAt->copy()->addDays(7);

        $this->prepareEventsMock($market->external_uuid, $market->event_radius, $startAt, $endAt, 'occasion-genius-events-page-1.json');

        $job = new SyncEventsForMarket($marketId, $startAt, $endAt);

        $job->handle($this->eventsRepo, $this->globalMarketId);

        $this->assertEquals(10, $market->events()->count());
    }

    /** @test */
    public function it_does_nothing_when_location_does_not_exist_locally()
    {
        $locationId = MarketId::fromString($this->faker->uuid);

        $startAt = Carbon::now();
        $endAt = $startAt->copy()->addDays(7);

        $job = new SyncEventsForMarket($locationId, $startAt, $endAt);

        EventFacade::fake([FailedToLoadEventsForMarket::class]);

        $job->handle($this->eventsRepo, $this->globalMarketId);

        EventFacade::assertDispatched(FailedToLoadEventsForMarket::class);
    }

    /** @test */
    public function it_does_nothing_when_location_has_no_external_id()
    {
        $location = factory(EloquentMarket::class)->state('no_external_uuid')->create();
        $locationId = MarketId::fromString($location->uuid);

        $startAt = Carbon::now();
        $endAt = $startAt->copy()->addDays(7);

        $job = new SyncEventsForMarket($locationId, $startAt, $endAt);

        EventFacade::fake([FailedToLoadEventsForMarket::class]);

        $job->handle($this->eventsRepo, $this->globalMarketId);

        EventFacade::assertDispatched(FailedToLoadEventsForMarket::class);
    }

    /** @test */
    public function it_dispatches_jobs_for_locations_found_in_og()
    {
        Bus::fake();

        //get the current count of locations
        $currentLocationCount = EloquentMarket::count();

        $startAt = Carbon::now();
        $endAt = $startAt->copy()->addDays(7);

        //note: location mock payload should have a single location in it
        $this->prepareLocationsMock();

        $job = new SyncMarkets($startAt, $endAt);

        $job->handle($this->locationsRepo);

        $this->assertEquals($currentLocationCount, EloquentMarket::count());

        Bus::assertDispatched(SyncEventsForMarket::class);
    }

    /** @test */
    public function it_detaches_events_in_the_import_date_range_which_are_not_from_the_external_source ()
    {
        //works the same as the test to import events from an external source, except we also add a new event,
        // within the date range for the market.
        //
        /** @var \Modules\EmailMarketing\Models\Market $market */
        $market = factory(EloquentMarket::class)->create();
        $marketId = MarketId::fromString($market->uuid);

        $startAt = Carbon::now();
        $endAt = $startAt->copy()->addDays(7);

        //Add an event withing the date range
        //Create an event
        $extraEvent = factory(EloquentEvent::class)->create();

        //attach the event to the location
        $extraEvent->markets()->attach($market);

        //add an event date for the event
        //set the start time to any time between the day after the mailing date, up to 5 days after that
        factory(EloquentEventDate::class)->create([
            'event_uuid' => $extraEvent->uuid,
            'starts_at' => $startAt->copy()
        ]);

        //Import events from the external source
        $this->prepareEventsMock($market->external_uuid, $market->event_radius, $startAt, $endAt, 'occasion-genius-events-page-1.json');

        $job = new SyncEventsForMarket($marketId, $startAt, $endAt);

        $job->handle($this->eventsRepo, $this->globalMarketId);

        //We should only have the events imported from the market
        $this->assertEquals(10, $market->events()->count());

        //Assert the extra event is still in the database
        $this->assertDatabaseHas('local_event_events', ['uuid' => $extraEvent->uuid]);

        //Assert that the market is not attached to the event
        $this->assertDatabaseMissing('local_event_market_events', [
            'market_uuid' => $market->uuid,
            'event_uuid' => $extraEvent->uuid
        ]);
    }

    /** @test */
    public function it_does_not_import_events_that_are_cancelled_or_postponed_with_date_tbd()
    {
        /** @var \Modules\EmailMarketing\Models\Market $market */
        $market = factory(EloquentMarket::class)->create();
        $marketId = MarketId::fromString($market->uuid);

        $startAt = Carbon::now();
        $endAt = $startAt->copy()->addDays(7);

        $this->prepareEventsMock($market->external_uuid, $market->event_radius, $startAt, $endAt, 'occasion-genius-events-with-cancelled.json');

        $job = new SyncEventsForMarket($marketId, $startAt, $endAt);

        $job->handle($this->eventsRepo, $this->globalMarketId);

        $this->assertEquals(1, $market->events()->count());
    }

    /** @test */
    public function it_adds_status_flags_for_events_with_cancelled_data()
    {
        /** @var \Modules\EmailMarketing\Models\Market $market */
        $market = factory(EloquentMarket::class)->create();
        $marketId = MarketId::fromString($market->uuid);

        $startAt = Carbon::now();
        $endAt = $startAt->copy()->addDays(7);

        $this->prepareEventsMock($market->external_uuid, $market->event_radius, $startAt, $endAt, 'occasion-genius-events-with-cancelled.json');

        $job = new SyncEventsForMarket($marketId, $startAt, $endAt);

        $job->handle($this->eventsRepo, $this->globalMarketId);

        //the previous test asserts that only 1 event will be saved, so grab the first event, and test the flags
        $event = $market->events->first();

        //check the flags
        $hasStatusFlags = $event->hasAllFlags([
            EloquentEvent::FLAG_STATUS_CHANGED_TO_VIRTUAL,
            EloquentEvent::FLAG_STATUS_UNKNOWN
        ]);

        $this->assertTrue($hasStatusFlags);
    }

    /** @test */
    public function it_sets_is_hidden_for_new_events_with_related_flags()
    {
        /** @var \Modules\EmailMarketing\Models\Market $market */
        $market = factory(EloquentMarket::class)->create();
        $marketId = MarketId::fromString($market->uuid);

        $startAt = Carbon::now();
        $endAt = $startAt->copy()->addDays(7);

        $this->prepareEventsMock($market->external_uuid, $market->event_radius, $startAt, $endAt, 'occasion-genius-events-with-flags-for-hidden.json');

        $job = new SyncEventsForMarket($marketId, $startAt, $endAt);

        $job->handle($this->eventsRepo, $this->globalMarketId);

        //the event sync should have only imported 1 event, so get the first one
        $event = $market->events->first();

        $this->assertTrue($event->is_hidden);
    }

    /** @test */
    public function it_does_not_reset_is_hidden_for_existing_events_with_related_flags()
    {
        /** @var \Modules\EmailMarketing\Models\Market $market */
        $market = factory(EloquentMarket::class)->create();
        $marketId = MarketId::fromString($market->uuid);

        $startAt = Carbon::now();
        $endAt = $startAt->copy()->addDays(7);

        $this->prepareEventsMock($market->external_uuid, $market->event_radius, $startAt, $endAt, 'occasion-genius-events-with-flags-for-hidden.json');

        //run the job the first time to create the event
        $job = new SyncEventsForMarket($marketId, $startAt, $endAt);
        $job->handle($this->eventsRepo, $this->globalMarketId);

        //the event sync should have only imported 1 event, so get the first one, and update is_hidden to false
        $newEvent = $market->events->first();
        $eventName = $newEvent->name;
        $newEvent->update([
            'name'      => $newEvent->name . " UPDATED",
            'is_hidden' => false
        ]);

        //run the job a second time to update events
        $job = new SyncEventsForMarket($marketId, $startAt, $endAt);
        $job->handle($this->eventsRepo, $this->globalMarketId);

        //get the updated event
        $market->load('events');
        $updatedEvent = $market->events->first();

        //verify the name was reset (showing the event was updated), but is_hidden remains false
        $this->assertEquals($eventName, $updatedEvent->name);
        $this->assertFalse($updatedEvent->is_hidden);
    }

    /** @test */
    public function it_successfully_saves_events_from_external_source_except_certain_flags_on_cancelled()
    {
        /** @var \Modules\EmailMarketing\Models\Market $market */
        $market = factory(EloquentMarket::class)->create();
        $marketId = MarketId::fromString($market->uuid);

        $startAt = Carbon::now();
        $endAt = $startAt->copy()->addDays(7);

        $this->prepareEventsMock(
            $market->external_uuid,
            $market->event_radius,
            $startAt,
            $endAt,
            'occasion-genius-events-with-cancelled.json'
        );

        $job = new SyncEventsForMarket($marketId, $startAt, $endAt);

        $job->handle($this->eventsRepo, $this->globalMarketId);

        $this->assertEquals(1, $market->events()->count());
    }

    //*************************************************************

    private function prepareEventsMock(MarketId $marketId, int $distance, Carbon $startAt, Carbon $endAt, string $dataFileName)
    {
        $payload = json_decode(file_get_contents(__DIR__ . '/../stubs/' . $dataFileName), true);

        $events = collect($payload['results'])->map(function (array $data) {
            return new Event([
                'uuid'        => Uuid::fromString($this->faker->uuid),
                'name'        => $data['name'],
                'description' => $data['description'],
                'dates'       => $this->convertDates($data),
                'venue'       => $this->convertVenue($data['venue']),
                'popularity'  => $data['popularity_score'],
                'imageUrl'    => $data['image_url'],
                'url'         => $data['source_url'],
                'flags'       => $data['flags'],
                'isInPerson'  => VirtualRule::isInPerson($data['virtual_rule']),
                'isVirtual'   => VirtualRule::isVirtual($data['virtual_rule']),
                'cancelled'   => $data['cancelled'] ?? []
            ]);
        });

        $this->eventsRepo->shouldReceive('getForLocation')
            ->with(
                Mockery::on(function ($argument) use ($marketId) {
                    return $marketId->sameValueAs($argument);
                }),
                Mockery::on(function ($argument) use ($distance) {
                    return $distance == $argument;
                }),
                Mockery::on(function ($argument) use ($startAt) {
                    return $startAt->equalTo($argument);
                }),
                Mockery::on(function ($argument) use ($endAt) {
                    return $endAt->equalTo($argument);
                })
            )
            ->andReturn(new EventCollection($events));
    }

    private function prepareLocationsMock()
    {
        $payload = json_decode(file_get_contents(__DIR__ . '/../stubs/occasion-genius-locations.json'), true);

        $events = collect($payload['results'])->map(function (array $data) {
            return (\Modules\EmailMarketing\OccasionGenius\Entities\Location::fromApiResponse($data))->toEntity();
        });

        $this->locationsRepo->shouldReceive('getAll')
            ->withNoArgs()
            ->andReturn(new MarketCollection($events));
    }

    private function buildUrl(string $image) : string
    {
        return sprintf(
            "https://image.api.occasiongenius.com/image/%s",
            $image
        );
    }

    private function convertDates(array $event) : array
    {
        //simple create the event date
        $eventDate = new EventDate([
            'startsAt' => Carbon::parse($event['start_date']),
            'endsAt'   => Carbon::parse($event['end_date']),
            'url'      => $event['ticket_url'],
        ]);

        return [$eventDate];
    }

    private function convertVenue(array $venue) : Venue
    {
        return new Venue($venue);
    }
}
