<?php

namespace Modules\EmailMarketing\Tests\Feature;

use Carbon\Carbon;
use Domain\Mailings\Mail\PreviewEmailMarketingMail;
use Exception;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;
use Mockery;
use Modules\EmailMarketing\Domain\BlogEmail\Entities\MarketValidationSettings;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\MarketCollection;
use Modules\EmailMarketing\Domain\LocalEvents\Repositories\MarketValidationSettingsRepositoryInterface;
use Modules\EmailMarketing\Jobs\ConfirmMarkets;
use Modules\EmailMarketing\Models\Event;
use Modules\EmailMarketing\Models\EventDate;
use Modules\EmailMarketing\Models\Market;
use Modules\EmailMarketing\Repositories\EloquentLocationRepository;
use Tests\RefreshDatabase;
use Tests\TestCase;

class MarketConfirmJobTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /** @var Mockery\LegacyMockInterface|Mockery\MockInterface|MarketValidationSettingsRepositoryInterface */
    private $settingsRepo;

    /** @var Mockery\LegacyMockInterface|Mockery\MockInterface|EloquentLocationRepository */
    private $locationsRepo;

    protected function setUp(): void
    {
        parent::setUp();

        $this->settingsRepo = Mockery::mock(MarketValidationSettingsRepositoryInterface::class);
        $this->locationsRepo = Mockery::mock(EloquentLocationRepository::class);
    }

    /** @test */
    public function it_sends_market_valid_email_when_all_markets_are_above_threshold() : void
    {
        // Set the Mailer to fake sending
        Mail::fake();

        /** @var Collection $markets */
        $markets = factory(Market::class, 2)->create([
            'synchronized_at' => Carbon::now(),
        ]);

        // Prepare a set of events for each market
        $markets->each(function (Market $market) {
            $this->prepareEvents($market);
        });

        // Mock the settings for a single email address
        $this->settingsRepo->shouldReceive('get')
            ->withNoArgs()
            ->andReturn(new MarketValidationSettings('<EMAIL>'));

        // Mock the locations to return the faked markets
        $this->locationsRepo->shouldReceive('getAll')
            ->withNoArgs()
            ->andReturn(new MarketCollection($markets));

        // Run the Confirm Markets
        $startAt = Carbon::now()->startOfDay();
        $endAt = $startAt->copy()->addDays(7);
        $job = new ConfirmMarkets($startAt, $endAt);
        $job->handle($this->settingsRepo, $this->locationsRepo);

        // Assert that the correct email was sent
        Mail::assertSent(PreviewEmailMarketingMail::class, function ($mail) {
            $this->assertEquals("Local Event markets sync validated notification", $mail->subjectText);

            return true;
        });
    }

    /** @test */
    public function it_sends_market_invalid_email_when_some_markets_are_below_threshold() : void
    {
        // Set the Mailer to fake sending
        Mail::fake();

        /** @var Collection $markets */
        $markets = factory(Market::class, 2)->create([
            'synchronized_at' => Carbon::now(),
        ]);

        // Prepare a set of events for each market
        $markets->each(function (Market $market) {
            $this->prepareEvents($market, 2);
        });

        // Mock the settings for a single email address
        $this->settingsRepo->shouldReceive('get')
            ->withNoArgs()
            ->andReturn(new MarketValidationSettings('<EMAIL>'));

        // Mock the locations to return the faked markets
        $this->locationsRepo->shouldReceive('getAll')
            ->withNoArgs()
            ->andReturn(new MarketCollection($markets));

        // Run the Confirm Markets
        $startAt = Carbon::now()->startOfDay();
        $endAt = $startAt->copy()->addDays(7);
        $job = new ConfirmMarkets($startAt, $endAt);
        $job->handle($this->settingsRepo, $this->locationsRepo);

        // Assert that the correct email was sent
        Mail::assertSent(PreviewEmailMarketingMail::class, function ($mail) {
            $this->assertEquals("Local Event markets sync invalid notification", $mail->subjectText);

            return true;
        });
    }

    /** @test */
    public function it_throws_exception_when_no_emails_are_set_in_settings() : void
    {
        /** @var Collection $markets */
        $markets = factory(Market::class, 2)->create([
            'synchronized_at' => Carbon::now(),
        ]);

        // Mock the settings for a single email address
        $this->settingsRepo->shouldReceive('get')
            ->withNoArgs()
            ->andReturn(new MarketValidationSettings());

        // Mock the locations to return the faked markets
        $this->locationsRepo->shouldReceive('getAll')
            ->withNoArgs()
            ->andReturn(new MarketCollection($markets));

        // Run the Confirm Markets
        $startAt = Carbon::now()->startOfDay();
        $endAt = $startAt->copy()->addDays(7);
        $job = new ConfirmMarkets($startAt, $endAt);

        try {
            $job->handle($this->settingsRepo, $this->locationsRepo);
        } catch (Exception $exception) {
            $this->assertEquals("Market update email list is empty.", $exception->getMessage());
        }
    }

    /** @test */
    public function it_releases_the_job_back_into_queue_when_markets_are_not_synced_today() : void
    {
        /** @var Collection $markets */
        $markets = factory(Market::class, 2)->create([
            'synchronized_at' => Carbon::yesterday()->startOfDay(),
        ]);

        // Mock the settings for a single email address
        $this->settingsRepo->shouldReceive('get')
            ->withNoArgs()
            ->andReturn(new MarketValidationSettings('<EMAIL>'));

        // Mock the locations to return the faked markets
        $this->locationsRepo->shouldReceive('getAll')
            ->withNoArgs()
            ->andReturn(new MarketCollection($markets));

        // Set up the start and end times
        $startAt = Carbon::now()->startOfDay();
        $endAt = $startAt->copy()->addDays(7);

        // Partially mock the job so that we can assert that release is handled
        $mockedJob = Mockery::mock(ConfirmMarkets::class, [$startAt, $endAt])->makePartial();

        // Assert that release is going to happen after the delay
        $mockedJob->shouldReceive('release')
            ->once()
            ->withArgs(function ($arg) {
                return $arg === ConfirmMarkets::MARKET_VALIDATION_RELEASE_DELAY;
            })
            ->andReturnNull();

        // Run the Confirm Markets job
        $mockedJob->handle($this->settingsRepo, $this->locationsRepo);
    }

    private function prepareEvents(Market $market, int $eventCount = 5) : void
    {
        // Create an amount of events based on $eventCount
        $events = factory(Event::class, $eventCount)
            ->create([
                'flags' => [
                    Event::EVENT_FLAG_IN_PERSON,
                ],
                'is_hidden' => false,
            ]);

        // Create a new EventDate and save it to the event model
        $events->each(function (Event $event) {
            $eventTime = factory(EventDate::class)
                ->create([
                    'event_uuid'    => $event->uuid,
                    'starts_at'     => Carbon::now(),
                    'ends_at'       => Carbon::tomorrow()->endOfDay(),
                ]);
            $event->times()->save($eventTime);
        });

        // Save the events to the market
        $market->events()->saveMany($events);
    }
}
