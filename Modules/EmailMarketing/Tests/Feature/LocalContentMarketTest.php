<?php

namespace Modules\EmailMarketing\Tests\Feature;

use Modules\EmailMarketing\Domain\Location\Actions\AddLocalContentMarket;
use Modules\EmailMarketing\Domain\Location\Actions\DeleteLocalContentMarket;
use Modules\EmailMarketing\Domain\Location\Actions\UpdateLocalContentMarket;
use Modules\EmailMarketing\DTO\LocalContentMarketDTO;
use Modules\EmailMarketing\Models\Market;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\RefreshDatabase;
use Tests\TestCase;
use Tests\TestsAuthorization;
use Tests\TestsGraphQL;

class LocalContentMarketTest extends TestCase
{
    use RefreshDatabase, WithFaker, TestsAuthorization, TestsGraphQL;

    /** @test */
    public function it_adds_a_new_market()
    {
        $action = app(AddLocalContentMarket::class);

        $marketVars = factory(Market::class)->make();

        $newMarket = $action->execute(new LocalContentMarketDTO([
            "uuid" => $marketVars->uuid,
            "name" => $marketVars->name,
            "address" => $marketVars->address,
            "latitude" => $marketVars->latitude,
            "longitude" => $marketVars->longitude,
            "eventRadius" => $marketVars->event_radius,
            "suggestedRadius" => $marketVars->suggested_radius
        ]));

        $this->assertEquals($newMarket->uuid, $marketVars->uuid);
        $this->assertEquals($newMarket->name, $marketVars->name);
        $this->assertEquals($newMarket->address, $marketVars->address);
        $this->assertEquals($newMarket->latitude, $marketVars->latitude);
        $this->assertEquals($newMarket->longitude, $marketVars->longitude);
        $this->assertEquals($newMarket->event_radius, $marketVars->event_radius);
        $this->assertEquals($newMarket->suggested_radius, $marketVars->suggested_radius);
    }

    /** @test */
    public function it_updates_a_market()
    {

        $action = app(UpdateLocalContentMarket::class);

        //create an existing model, and get teh vars for update
        $existingMarket = factory(Market::class)->create();
        $updatedMarketVars = factory(Market::class)->make();

        $updatedMarket = $action->execute(new LocalContentMarketDTO([
            "uuid" => $existingMarket->uuid,
            "name" => $updatedMarketVars->name,
            "address" => $updatedMarketVars->address,
            "latitude" => $updatedMarketVars->latitude,
            "longitude" => $updatedMarketVars->longitude,
            "eventRadius" => $updatedMarketVars->event_radius,
            "suggestedRadius" => $updatedMarketVars->suggested_radius
        ]));

        $this->assertEquals($updatedMarket->uuid, $existingMarket->uuid);
        $this->assertEquals($updatedMarket->name, $updatedMarketVars->name);
        $this->assertEquals($updatedMarket->address, $updatedMarketVars->address);
        $this->assertEquals($updatedMarket->latitude, $updatedMarketVars->latitude);
        $this->assertEquals($updatedMarket->longitude, $updatedMarketVars->longitude);
        $this->assertEquals($updatedMarket->event_radius, $updatedMarketVars->event_radius);
        $this->assertEquals($updatedMarket->suggested_radius, $updatedMarketVars->suggested_radius);
    }

    /** @test */
    public function it_soft_deletes_a_market()
    {
        $existing = factory(Market::class)->create();

        /** @var \Domain\Profile\Actions\DeleteTeamMember $action */
        $action = app(DeleteLocalContentMarket::class);

        $result = $action->execute($existing);

        $this->assertTrue($result);

        $this->assertSoftDeleted('local_event_markets', [
            'uuid' => $existing->uuid
        ]);
    }
}
