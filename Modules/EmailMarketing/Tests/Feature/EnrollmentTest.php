<?php

namespace Modules\EmailMarketing\Tests\Feature;

use App\Events\AccountEnrolledInLocalContent;
use App\Events\AccountRemovedFromLocalEvents;
use App\Models\ContactBlock;
use App\Models\ContactBlockItem;
use App\Models\EmailAddress;
use App\Models\Location;
use App\Models\PhoneNumber;
use App\Models\Plan;
use App\Models\ProfessionalTitle;
use Illuminate\Foundation\Testing\WithFaker;
use Modules\EmailMarketing\Models\Market;
use Tests\MocksXrmsApiCallsForImages;
use Tests\RefreshDatabase;
use Tests\TestCase;

/** @group AccountEnrollAndRemove */
class EnrollmentTest extends TestCase
{
    use RefreshDatabase, WithFaker, MocksXrmsApiCallsForImages;

    /** @var \Illuminate\Support\Collection */
    private $markets;

    protected function setUp(): void
    {
        parent::setUp();

        $this->markets = factory(Market::class, 10)->create();

        $this->mockXrmsApiCallsForImages();
    }

    /** @test */
    public function it_adds_local_event_to_the_account()
    {
        $marketId = $this->markets->random()->uuid;

        // Handle the event for adding the plan
        $this->actOnInterserviceEvent($this->makeAccountEnrolledInLocalEvents($marketId));

        $this->account->refresh();

        // Assert account's location was assigned
        $this->assertEquals(
            $marketId,
            $this->account->localContentMarket->uuid
        );

        // Assert that the plan is assigned
        $this->assertTrue(
            $this->account->hasPlan(Plan::PLAN_ID_LOCAL_EVENT),
            'Plan was not assigned'
        );
    }

    /** @test */
    public function it_creates_a_contact_block_upon_enrollment()
    {
        // With profile Items
        factory(PhoneNumber::class)->create(['is_primary' => true]);
        factory(EmailAddress::class)->create(['is_primary' => true]);
        factory(Location::class)->create(['is_primary' => true]);
        factory(ProfessionalTitle::class)->create();

        // Handle the event for adding the plan
        $this->actOnInterserviceEvent($this->makeAccountEnrolledInLocalEvents());

        // Check if the account has a contact block
        $accountCb = ContactBlock::where('product', ContactBlock::PRODUCT_LOCAL_EVENTS)->first();

        $this->assertNotNull($accountCb);
        $this->assertCount(
            7,
            $accountCb->items()->get()->toArray()
        ); //Display name, 5 profile items, and 2 mocked images
        $this->assertEquals(
            $this->account->id,
            $accountCb->items()->where('item_type', ContactBlockItem::ITEM_TYPE_DISPLAY_NAME)->first()->toArray(
            )['item_id']
        );
        $this->assertContains(ContactBlockItem::ITEM_TYPE_HEADSHOT, $accountCb->items()->pluck('item_type'));
        $this->assertContains(ContactBlockItem::ITEM_TYPE_OFFICE_LOGO, $accountCb->items()->pluck('item_type'));
        $this->assertContains(ContactBlockItem::ITEM_TYPE_DISPLAY_NAME, $accountCb->items()->pluck('item_type'));
        $this->assertContains(ContactBlockItem::ITEM_TYPE_PROFESSIONAL_TITLE, $accountCb->items()->pluck('item_type'));
        $this->assertContains(ContactBlockItem::ITEM_TYPE_PHONE_NUMBER, $accountCb->items()->pluck('item_type'));
        $this->assertContains(ContactBlockItem::ITEM_TYPE_EMAIL_ADDRESS, $accountCb->items()->pluck('item_type'));
        $this->assertContains(ContactBlockItem::ITEM_TYPE_LOCATION, $accountCb->items()->pluck('item_type'));
    }

    /** @test */
    public function it_doesnt_create_new_cb_for_local_events_when_exists()
    {
        factory(ContactBlock::class)->states('local-events')->create();

        // Handle the event for adding the plan
        $this->actOnInterserviceEvent($this->makeAccountEnrolledInLocalEvents());

        $accountCb = ContactBlock::where('account_id', $this->account->id)
            ->where('product', ContactBlock::PRODUCT_LOCAL_EVENTS)->get();
        $this->assertCount(1, $accountCb);
    }

    /** @test */
    public function it_removes_only_local_events_and_keeps_others()
    {
        $this->account->plans()->sync([Plan::PLAN_ID_AMERICAN_LIFESTYLE_MAGAZINE, Plan::PLAN_ID_LOCAL_EVENT]);

        // Check that the account has 2 plans, ALM and LE
        $this->assertEquals(2, $this->account->plans()->count());

        // Handle the event for removing the plan
        $this->actOnInterserviceEvent($this->makeAccountRemovedFromLocalEvents());

        $this->account->refresh();

        // Check that the account has 1 plan
        $this->assertEquals(1, $this->account->plans()->count());

        // Check it doesnt have LE plan
        $this->assertFalse($this->account->hasPlan(Plan::PLAN_ID_LOCAL_EVENT));
        // Check it still has ALM plan
        $this->assertTrue($this->account->hasPlan(Plan::PLAN_ID_AMERICAN_LIFESTYLE_MAGAZINE));
    }

    private function makeAccountEnrolledInLocalEvents(?string $locationUuid = null)
    {
        return AccountEnrolledInLocalContent::fromArray([
            'uuid' => $this->faker->uuid,
            'occurred_at' => now()->toDateTimeString(),
            'global_name' => 'account.enrolled-in-local-content',
            'data' => [
                'plan_id' => Plan::PLAN_ID_LOCAL_EVENT,
                'market_id' => $locationUuid ?? $this->markets->random()->uuid,
            ],
            'metadata' => [
                'source' => 'crm',
                'context' => ['account_id' => $this->account->id],
            ],
        ]);
    }

    private function makeAccountRemovedFromLocalEvents()
    {
        return AccountRemovedFromLocalEvents::fromArray([
            'uuid' => $this->faker->uuid,
            'occurred_at' => now()->toDateTimeString(),
            'global_name' => 'account.removed-from-local-content',
            'data' => ['plan_id' => Plan::PLAN_ID_LOCAL_EVENT],
            'metadata' => [
                'source' => 'crm',
                'context' => ['account_id' => $this->account->id],
            ],
        ]);
    }
}
