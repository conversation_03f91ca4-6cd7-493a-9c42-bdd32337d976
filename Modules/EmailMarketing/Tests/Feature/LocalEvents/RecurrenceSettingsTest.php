<?php

namespace Modules\EmailMarketing\Tests\Feature\LocalEvents;

use App\Models\EmailAddress;
use Carbon\Carbon;
use Domain\Account\AccountAggregateRoot;
use Illuminate\Foundation\Testing\WithFaker;
use Infrastructure\Repositories\Titan\TitanApiRepository;
use Mockery;
use Modules\EmailMarketing\Domain\LocalEvents\Actions\AddRecurrenceSettings;
use Modules\EmailMarketing\Domain\LocalEvents\Actions\UpdateRecurrenceSettings;
use Modules\EmailMarketing\DTO\RecurrenceSettingsDTO;
use Modules\EmailMarketing\DTO\UpdateRecurrenceSettingsDTO;
use Modules\EmailMarketing\Entities\MarketId;
use Modules\EmailMarketing\Models\Market;
use Modules\EmailMarketing\Models\RecurrenceSettings;
use Tests\RefreshDatabase;
use Tests\TestCase;
use Tests\TestsAuthorization;

class RecurrenceSettingsTest extends TestCase
{
    use RefreshDatabase, WithFaker, TestsAuthorization;

    /** @var \App\Models\EmailAddress */
    private $emailAddress;

    protected function setUp(): void
    {
        parent::setUp();

        $this->emailAddress = factory(EmailAddress::class)->create();
    }

    /** @test */
    public function it_adds_a_new_recurrence_settings()
    {
        // set expectations for titan repository calls
        $titanRepo = Mockery::mock(TitanApiRepository::class);
        $titanRepo->shouldReceive('updateDigitalProductAssociations')->once()->andReturn([]);
        $this->app->instance(TitanApiRepository::class, $titanRepo);

        $action = app(AddRecurrenceSettings::class);

        $recurrenceSettings = factory(RecurrenceSettings::class)->make([
            'email_from_id' => $this->emailAddress->id
        ]);

        //execute the action
        $newRecurrenceSettings = $action->execute(RecurrenceSettingsDTO::fromModel($recurrenceSettings));

        $this->assertEquals($recurrenceSettings->frequency, $newRecurrenceSettings->frequency);
        $this->assertEquals($recurrenceSettings->email_from_id, $newRecurrenceSettings->email_from_id);
        $this->assertEquals($recurrenceSettings->mailing_date, $newRecurrenceSettings->mailing_date);
        $this->assertEquals($recurrenceSettings->mailing_subject, $newRecurrenceSettings->mailing_subject);
        $this->assertEquals($recurrenceSettings->mailing_body, $newRecurrenceSettings->mailing_body);
        $this->assertEquals($recurrenceSettings->is_enabled, $newRecurrenceSettings->is_enabled);
    }

    /** @test */
    public function it_updates_a_recurrence_settings()
    {
        // set expectations for titan repository calls
        $titanRepo = Mockery::mock(TitanApiRepository::class);
        $titanRepo->shouldReceive('updateDigitalProductAssociations')->once()->andReturn([]);
        $this->app->instance(TitanApiRepository::class, $titanRepo);

        $action = app(UpdateRecurrenceSettings::class);

        //create an existing model, and get the vars for update
        $existingRecurrenceSettings = factory(RecurrenceSettings::class)->create([
            'email_from_id' => $this->emailAddress->id
        ]);

        $newEmail = factory(EmailAddress::class)->create();

        $updateDto = new UpdateRecurrenceSettingsDTO([
            'old' => RecurrenceSettingsDTO::fromModel($existingRecurrenceSettings),
            'new' => new RecurrenceSettingsDTO([
                'frequency'              => "bi-weekly",
                'email_from_id'          => $newEmail->id,
                'mailing_date'           => new Carbon('next thursday'),
                'mailing_subject'        => $this->faker->sentence . "UPDATED",
                'persist_subject'        => true,
                'mailing_heading'        => $this->faker->sentence . "UPDATED",
                'persist_heading'        => true,
                'mailing_body'           => $this->faker->paragraph . "UPDATED",
                'recipient_group_ids'    => [1, 2, 3],
                'is_enabled'             => true,
                'failed'                 => false,
                'mailing_customizations' => [],
            ])
        ]);

        $updatedRecurrenceSettings = $action->execute($updateDto);

        $this->assertEquals($updateDto->new->frequency, $updatedRecurrenceSettings->frequency);
        $this->assertEquals($updateDto->new->email_from_id, $updatedRecurrenceSettings->email_from_id);
        $this->assertEquals($updateDto->new->mailing_date, $updatedRecurrenceSettings->mailing_date);
        $this->assertEquals($updateDto->new->mailing_heading, $updatedRecurrenceSettings->mailing_heading);
        $this->assertEquals($updateDto->new->persist_heading, $updatedRecurrenceSettings->persist_heading);
        $this->assertEquals($updateDto->new->mailing_body, $updatedRecurrenceSettings->mailing_body);
        $this->assertEquals($updateDto->new->is_enabled, $updatedRecurrenceSettings->is_enabled);
    }

    /** @test */
    public function it_disables_a_recurrence_settings()
    {
        // set expectations for titan repository calls
        $titanRepo = Mockery::mock(TitanApiRepository::class);
        $titanRepo->shouldReceive('updateDigitalProductAssociations')->once()->andReturn([]);
        $this->app->instance(TitanApiRepository::class, $titanRepo);

        $action = app(UpdateRecurrenceSettings::class);

        //create an existing model, and get the vars for update
        $existingRecurrenceSettings = factory(RecurrenceSettings::class)->create([
            'email_from_id' => $this->emailAddress->id,
        ]);

        $newDto = RecurrenceSettingsDTO::fromModel($existingRecurrenceSettings);
        $newDto->is_enabled = false;

        $updateDto = new UpdateRecurrenceSettingsDTO([
            'old' => RecurrenceSettingsDTO::fromModel($existingRecurrenceSettings),
            'new' => $newDto,
        ]);

        $updatedRecurrenceSettings = $action->execute($updateDto);

        $this->assertEquals($updateDto->new->is_enabled, $updatedRecurrenceSettings->is_enabled);
    }

    /** @test */
    public function it_resets_mailing_date_when_a_recurrence_is_enabled()
    {
        $this->markTestIncomplete('Need to fix this as it fails if run after 7PM on a Wednesday');
        return;
        // set expectations for titan repository calls
        $titanRepo = Mockery::mock(TitanApiRepository::class);
        $titanRepo->shouldReceive('updateDigitalProductAssociations')->once()->andReturn([]);
        $this->app->instance(TitanApiRepository::class, $titanRepo);

        $action = app(UpdateRecurrenceSettings::class);

        //create a local event market, and attach it to the account
        $market = factory(Market::class)->create();
        $this->account->market_uuid = $market->uuid;
        $this->account->save();

        //create an existing model, and get the vars for update
        $existingRecurrenceSettings = factory(RecurrenceSettings::class)->create([
            'email_from_id' => $this->emailAddress->id,
            'mailing_date' => new Carbon('last thursday 9AM'),
            'is_enabled' => false
        ]);

        //calculate what the next mailing date SHOULD be
        //get the current date and time in the correct timezone for the market
        $today = Carbon::now($market->timezone);

        //if today is thursday, we need to check if it is before 9AM
        $nextMailingDate = null;
        if ($today->isThursday()) {
            $limit = $today->copy()->setTime(9, 0, 0);

            //if today is before the limit, then the limit is the next sending date
            if ($today->isBefore($limit)) {
                $nextMailingDate = $limit->copy();
            }
        }

        //mailing date has not yet been set
        if (!$nextMailingDate) {
            //set it to next thursday local time
            $nextMailingDate =  Carbon::createFromFormat(
                'Y-m-d H:i:s',
                Carbon::parse('next thursday 9AM')->toDateTimeString(), //get the date for Thursday and hard code the time
                $market->timezone //set it to the right timezone
            );
        }

        //reset to the app time zone
        $nextMailingDate->setTimezone(config('app.timezone'));

        //create a DTO for the new settings and reset is_enabled
        $newDto = RecurrenceSettingsDTO::fromModel($existingRecurrenceSettings);
        $newDto->is_enabled = true;

        $updateDto = new UpdateRecurrenceSettingsDTO([
            'old' => RecurrenceSettingsDTO::fromModel($existingRecurrenceSettings),
            'new' => $newDto
        ]);

        //updatge the recurrence settings
        $updatedRecurrenceSettings = $action->execute($updateDto);

        //assert that recurrences are enabled, and the mailing date changed
        $this->assertEquals($nextMailingDate, $updatedRecurrenceSettings->mailing_date);
        $this->assertTrue($updatedRecurrenceSettings->is_enabled);
    }

    /** @test */
    public function it_does_not_reset_mailing_date_if_recurrence_is_already_enabled()
    {
        // set expectations for titan repository calls
        $titanRepo = Mockery::mock(TitanApiRepository::class);
        $titanRepo->shouldReceive('updateDigitalProductAssociations')->once()->andReturn([]);
        $this->app->instance(TitanApiRepository::class, $titanRepo);

        $action = app(UpdateRecurrenceSettings::class);

        //create a local event market, and attach it to the account
        $market = factory(Market::class)->create();
        $this->account->market_uuid = $market->uuid;
        $this->account->save();

        //set the starting value for mailing date
        $originalMailingDate = new Carbon('last thursday 9am');

        //create an existing model, and get the vars for update
        $existingRecurrenceSettings = factory(RecurrenceSettings::class)->create([
            'email_from_id' => $this->emailAddress->id,
            'mailing_date' => new Carbon('last thursday 9AM'),
            'is_enabled' => true
        ]);

        //calculate what the next mailing date WOULD be if it was reset
        //get the current date and time in the correct timezone for the market
        $today = Carbon::now($market->timezone);

        //if today is thursday, we need to check if it is before 9AM
        $nextMailingDate = null;
        if ($today->isThursday()) {
            $limit = $today->copy()->setTime(9, 0, 0);

            //if today is before the limit, then the limit is the next sending date
            if ($today->isBefore($limit)) {
                $nextMailingDate = $limit->copy();
            }
        }

        //mailing date has not yet been set
        if (!$nextMailingDate) {
            //set it to next thursday local time
            $nextMailingDate =  Carbon::createFromFormat(
                'Y-m-d H:i:s',
                Carbon::parse('next thursday 9AM')->toDateTimeString(), //get the date for Thursday and hard code the time
                $market->timezone //set it to the right timezone
            );
        }

        //reset to the app time zone
        $nextMailingDate->setTimezone(config('app.timezone'));

        //create a DTO for the new settings and reset another field, and make sure is_enabled is still true
        $newDto = RecurrenceSettingsDTO::fromModel($existingRecurrenceSettings);
        $newDto->nextMailingSubject = $this->faker->sentence . "UPDATED";
        $newDto->isEnabled = true;

        $updateDto = new UpdateRecurrenceSettingsDTO([
            'old' => RecurrenceSettingsDTO::fromModel($existingRecurrenceSettings),
            'new' => $newDto
        ]);

        //updatge the recurrence settings
        $updatedRecurrenceSettings = $action->execute($updateDto);

        //assert that recurrences are enabled, and the mailing date changed
        $this->assertEquals($originalMailingDate, $updatedRecurrenceSettings->mailing_date);
        $this->assertNotEquals($nextMailingDate, $updatedRecurrenceSettings->mailing_date);
        $this->assertTrue($updatedRecurrenceSettings->is_enabled);
    }

    /** @test */
    public function it_resets_mailing_subject_and_body_when_account_market_is_updated()
    {
        // set expectations for titan repository calls
        $titanRepo = Mockery::mock(TitanApiRepository::class);
        $titanRepo->shouldReceive('updateDigitalProductAssociations')->once()->andReturn([]);
        $this->app->instance(TitanApiRepository::class, $titanRepo);

        //First, create a market, and assign it to the account
        $market = factory(Market::class)->create();
        $this->account->market_uuid = $market->uuid;
        $this->account->save();

        //now, create a second market that we'll use when we update
        $updatedMarket = factory(Market::class)->create();

        //create an existing model for recurrence settings, make sure the subject and body are populated
        $existingRecurrenceSettings = factory(RecurrenceSettings::class)->create([
            'email_from_id' => $this->emailAddress->id,
            'mailing_subject' => $this->faker->sentence(),
            'mailing_body' => $this->faker->paragraph()
        ]);

        //update the account market through the account aggregate root
        $ar = new AccountAggregateRoot($this->account);

        //call the update function
        $ar->updateMarket(MarketId::fromString($updatedMarket->uuid))->persist();

        //check the database
        $this->assertDatabaseHas('local_events_recurrence_settings', [
            'id' => $existingRecurrenceSettings->id,
            'mailing_subject' => null,
            'mailing_body' => ""
        ]);
    }

    /** @test */
    public function it_expects_start_date_time_adjusted_by_timezone()
    {
        $startAt = Carbon::now()->startOfDay();

        $easternStartDate = RecurrenceSettings::getEventsStartDate($startAt, new \DateTimeZone('america/new_york'));
        $centralStartDate = RecurrenceSettings::getEventsStartDate($startAt, new \DateTimeZone('america/chicago'));
        $mountainStartDate = RecurrenceSettings::getEventsStartDate($startAt, new \DateTimeZone('america/denver'));
        $pacificStartDate = RecurrenceSettings::getEventsStartDate($startAt, new \DateTimeZone('america/los_angeles'));

        // start date will be less one day after timezone conversions
        $startDate = $startAt->subDay()->toDateString();

        // check if the start date is observing daylight savings time
        // for instance, eastern standard time is UTC-05:00 and eastern daylight time is UTC-04:00
        if ((bool) $startAt->copy()->setTimezone(new \DateTimeZone('america/new_york'))->format('I')) {
            $this->assertEquals("$startDate 04:00:00", $easternStartDate->toDateTimeString());
            $this->assertEquals("$startDate 05:00:00", $centralStartDate->toDateTimeString());
            $this->assertEquals("$startDate 06:00:00", $mountainStartDate->toDateTimeString());
            $this->assertEquals("$startDate 07:00:00", $pacificStartDate->toDateTimeString());
        } else {
            $this->assertEquals("$startDate 05:00:00", $easternStartDate->toDateTimeString());
            $this->assertEquals("$startDate 06:00:00", $centralStartDate->toDateTimeString());
            $this->assertEquals("$startDate 07:00:00", $mountainStartDate->toDateTimeString());
            $this->assertEquals("$startDate 08:00:00", $pacificStartDate->toDateTimeString());
        }
    }

    /** @test */
    public function it_expects_end_date_time_adjusted_by_timezone()
    {
        $startAt = Carbon::now()->startOfDay();
        $endAt = $startAt->copy()->addDays(7);

        $easternEndDate = RecurrenceSettings::getLocalEndDate($endAt, new \DateTimeZone('america/new_york'));
        $centralEndDate = RecurrenceSettings::getLocalEndDate($endAt, new \DateTimeZone('america/chicago'));
        $mountainEndDate = RecurrenceSettings::getLocalEndDate($endAt, new \DateTimeZone('america/denver'));
        $pacificEndDate = RecurrenceSettings::getLocalEndDate($endAt, new \DateTimeZone('america/los_angeles'));

        // end date will be less one day after timezone conversions
        $endDate = $endAt->subDay()->toDateString();

        // check if the end date is observing daylight savings time in the easter time zone
        // for instance, eastern standard time is UTC-05:00 and eastern daylight time is UTC-04:00
        if ((bool) $endAt->copy()->setTimezone(new \DateTimeZone('america/new_york'))->format('I')) {
            $this->assertEquals("$endDate 03:59:59", $easternEndDate->toDateTimeString());
            $this->assertEquals("$endDate 04:59:59", $centralEndDate->toDateTimeString());
            $this->assertEquals("$endDate 05:59:59", $mountainEndDate->toDateTimeString());
            $this->assertEquals("$endDate 06:59:59", $pacificEndDate->toDateTimeString());
        } else {
            $this->assertEquals("$endDate 04:59:59", $easternEndDate->toDateTimeString());
            $this->assertEquals("$endDate 05:59:59", $centralEndDate->toDateTimeString());
            $this->assertEquals("$endDate 06:59:59", $mountainEndDate->toDateTimeString());
            $this->assertEquals("$endDate 07:59:59", $pacificEndDate->toDateTimeString());
        }
    }
}
