<?php

namespace Modules\EmailMarketing\Tests\Feature\LocalEvents;

use App\Models\Plan;
use Carbon\Carbon;
use DateTimeZone;
use Modules\EmailMarketing\Entities\GlobalMarketId;
use Modules\EmailMarketing\Models\Event;
use Modules\EmailMarketing\Models\EventDate;
use Modules\EmailMarketing\Models\Market;
use Modules\EmailMarketing\OccasionGenius\Entities\Venue;
use Modules\Mailing\Models\Mailing;
use Tests\DoesNotCreateAccount;
use Tests\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Collection;
use Tests\TestCase;

class GetEventsForMarketAndDateRangeTest extends TestCase implements DoesNotCreateAccount
{
    use RefreshDatabase, WithFaker;

    /** @var \Carbon\Carbon */
    private $eventsDateRangeStart;

    /** @var \Carbon\Carbon */
    private $eventsDateRangeEnd;

    /** @var \Modules\EmailMarketing\Models\Market */
    private $localMarket;

    /** @var \Modules\EmailMarketing\Models\Market */
    private $globalMarket;

    protected function setUp(): void
    {
        parent::setUp();

        //Create a local market
        $this->localMarket = factory(Market::class)->create([
            'timezone' => new DateTimeZone('UTC')
        ]);

        //create a global virtual market
        $this->globalMarket = factory(Market::class)->create([
            'uuid' => app(GlobalMarketId::class)->getValue()->toString(),
            'timezone' => new DateTimeZone('UTC')
        ]);

        //set the next mailing date
        // needs to be next thursday 9AM to properly tests the event gathering logic
        $nextMailingDate = Carbon::parse('next thursday 9AM');

        //get the date ranges used in the event gathering
        //it's the start date to the end of day of the second wednesday
        $this->eventsDateRangeStart = $nextMailingDate->copy()->startOfDay();
        $this->eventsDateRangeEnd = $nextMailingDate->copy()->modify('second Wednesday')->endOfDay();
    }

    /** @test */
    public function it_retrieves_a_featured_event_with_all_matching_flags()
    {
        //create the expected featured event with all the required flags
        $expectedFeaturedEvent = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ]
        )->first();

        //create other events within the mailing period
        $otherEvents = $this->createEventsForMarketAndDateRange(
            5,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL_GEM
            ]
        );

        //get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        //assert the returned featured event is the expected
        $this->assertEquals($expectedFeaturedEvent->uuid, $eventsColl->featuredEvent->uuid);
    }

    /** @test */
    public function it_retrieves_a_featured_event_determined_by_local_or_virtual_value()
    {
        //create the expected featured event that will be in person
        $expectedFeaturedEvent = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_IN_PERSON
            ]
        )->first();

        //create other events within the mailing period that are not in person
        $otherEvents = $this->createEventsForMarketAndDateRange(
            5,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_VIRTUAL
            ]
        );

        //get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        //assert the returned featured event is the expected
        $this->assertEquals($expectedFeaturedEvent->uuid, $eventsColl->featuredEvent->uuid);
    }

    /** @test */
    public function it_retrieves_a_featured_event_determined_by_flag_values()
    {
        //create the expected featured event with a high rated flag
        $expectedFeaturedEvent = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_DONT_MISS
            ]
        )->first();

        //create other events within the mailing period with a lower rated flag
        $otherEvents = $this->createEventsForMarketAndDateRange(
            5,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL_GEM
            ]
        );

        //get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        //assert the returned featured event is the expected
        $this->assertEquals($expectedFeaturedEvent->uuid, $eventsColl->featuredEvent->uuid);
    }

    /** @test */
    public function it_retrieves_a_featured_event_determined_by_popularity()
    {
        //create the expected featured event with a high popularity
        $expectedFeaturedEvent = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_DONT_MISS
            ],
            [
                'popularity' => 10
            ]
        )->first();

        //create other events within the mailing period with no predetermined popularity
        $otherEvents = $this->createEventsForMarketAndDateRange(
            5,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeStart->copy()->addDays(2),
            [
                Event::EVENT_FLAG_LOCAL_GEM
            ]
        );

        //get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        //assert the returned featured event is the expected
        $this->assertEquals($expectedFeaturedEvent->uuid, $eventsColl->featuredEvent->uuid);
    }

    /** @test */
    public function it_retrieves_a_featured_event_that_has_not_been_included_in_previous_mailings_based_on_event_uuid()
    {
        //set an account id
        $accountId = $this->faker->randomNumber();

        //based_on_recurring_event_uuid
        //based_on_event_name_and_venue_details
        //create a featured event to be used in a mailing
        $usedFeaturedEvent = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ],
            [
                'popularity' => 10,
            ]
        )->first();

        //Create a mailing that includes the featured event
        $mailing = factory(Mailing::class)->states(['sent'])->create([
            'account_id' => $accountId,
            'product' => Plan::PLAN_ID_LOCAL_EVENT,
            'send_at' => Carbon::now()->subHours(4),
            'properties' => [
                'featured_event' => [
                    "event_name"        => $this->faker->sentence,
                    "event_uuid"        => $usedFeaturedEvent->uuid, //match the used featured event uuid
                    "event_recurring_uuid" => $this->faker->uuid,
                    "venue_name"        => $this->faker->sentence,
                    "venue_latitude"     => $this->faker->latitude,
                    "venue_longitude"      => $this->faker->longitude,
                ]
            ]
        ]);

        //create the expected featured event with all the required flags
        $expectedFeaturedEvent = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ],
            [
                'popularity' => 5,
            ]
        )->first();

        //create other events within the mailing period
        $otherEvents = $this->createEventsForMarketAndDateRange(
            5,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL_GEM
            ]
        );

        //get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            $accountId
        );

        //assert the returned featured event is the expected
        $this->assertEquals($expectedFeaturedEvent->uuid, $eventsColl->featuredEvent->uuid);
    }

    /** @test */
    public function it_retrieves_a_featured_event_that_has_not_been_included_in_previous_mailings_based_on_recurring_event_uuid()
    {
        //set an account id
        $accountId = $this->faker->randomNumber();

        //based_on_event_name_and_venue_details
        //create a featured event to be used in a mailing
        $usedFeaturedEvent = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ],
            [
                'popularity' => 10,
            ]
        )->first();

        //Create a mailing that includes the featured event
        $mailing = factory(Mailing::class)->states(['sent'])->create([
            'account_id' => $accountId,
            'product' => Plan::PLAN_ID_LOCAL_EVENT,
            'send_at' => Carbon::now()->subHours(4),
            'properties' => [
                'featured_event' => [
                    "event_name"        => $this->faker->sentence,
                    "event_uuid"        => $this->faker->uuid,
                    "event_recurring_uuid" => $usedFeaturedEvent->recurring_event_uuid, //match the used featured recurring event uuid
                    "venue_name"        => $this->faker->sentence,
                    "venue_latitude"     => $this->faker->latitude,
                    "venue_longitude"      => $this->faker->longitude,
                ]
            ]
        ]);

        //create the expected featured event with all the required flags
        $expectedFeaturedEvent = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ],
            [
                'popularity' => 5,
            ]
        )->first();

        //create other events within the mailing period
        $otherEvents = $this->createEventsForMarketAndDateRange(
            5,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL_GEM
            ]
        );

        //get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            $accountId
        );

        //assert the returned featured event is the expected
        $this->assertEquals($expectedFeaturedEvent->uuid, $eventsColl->featuredEvent->uuid);
    }

    /** @test */
    public function it_retrieves_a_featured_event_that_has_not_been_included_in_previous_mailings_based_on_event_name_and_venue_details()
    {
        //set an account id
        $accountId = $this->faker->randomNumber();

        //create a featured event to be used in a mailing
        $usedFeaturedEvent = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ],
            [
                'popularity' => 10,
            ]
        )->first();

        //Create a mailing that includes the featured event
        $mailing = factory(Mailing::class)->states(['sent'])->create([
            'account_id' => $accountId,
            'product' => Plan::PLAN_ID_LOCAL_EVENT,
            'send_at' => Carbon::now()->subHours(4),
            'properties' => [
                'featured_event' => [
                    "event_name"        => $usedFeaturedEvent->name, //match the used featured event name
                    "event_uuid"        => $this->faker->uuid,
                    "event_recurring_uuid" => $this->faker->uuid,
                    "venue_name"        => $usedFeaturedEvent->venue->name, //match the used featured event venue name
                    "venue_latitude"     => $usedFeaturedEvent->venue->latitude,  //match the used featured event venue latitude
                    "venue_longitude"      => $usedFeaturedEvent->venue->longitude,  //match the used featured event venue longitude
                ]
            ]
        ]);

        //create the expected featured event with all the required flags
        $expectedFeaturedEvent = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ],
            [
                'popularity' => 5,
            ]
        )->first();

        //create other events within the mailing period
        $otherEvents = $this->createEventsForMarketAndDateRange(
            5,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL_GEM
            ]
        );

        //get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            $accountId
        );

        //assert the returned featured event is the expected
        $this->assertEquals($expectedFeaturedEvent->uuid, $eventsColl->featuredEvent->uuid);
    }

    /** @test */
    public function it_does_not_exclude_a_featured_event_that_has_been_included_in_previous_mailings_for_other_accounts()
    {
        //set an account id
        $accountId = $this->faker->randomNumber();
        $otherAccountId = $accountId * 2;

        //create a featured event to be used in another account's mailing
        $expectedFeaturedEvent = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ],
            [
                'popularity' => 10,
            ]
        )->first();

        //Create a mailing that includes the featured event
        $mailing = factory(Mailing::class)->states(['sent'])->create([
            'account_id' => $otherAccountId,
            'product' => Plan::PLAN_ID_LOCAL_EVENT,
            'send_at' => Carbon::now()->subHours(4),
            'properties' => [
                'featured_event' => [
                    "event_name"        => $expectedFeaturedEvent->name,
                    "event_uuid"        => $expectedFeaturedEvent->uuid,
                    "event_recurring_uuid" => $expectedFeaturedEvent->recurring_event_uuid,
                    "venue_name"        => $expectedFeaturedEvent->venue->name,
                    "venue_latitude"     => $expectedFeaturedEvent->venue->latitude,
                    "venue_longitude"      => $expectedFeaturedEvent->venue->longitude,
                ]
            ]
        ]);

        //create other events within the mailing period
        $otherEvents = $this->createEventsForMarketAndDateRange(
            5,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL_GEM
            ]
        );

        //get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            $accountId
        );

        //assert the returned featured event is the expected
        $this->assertEquals($expectedFeaturedEvent->uuid, $eventsColl->featuredEvent->uuid);
    }

    /** @test */
    public function it_retrieves_events_from_local_and_global_events_for_a_local_market()
    {
        //Create the local event - it will also be the featured event
        $expectedLocal = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ]
        )->first();

        //create the expected global market event
        $expectedGlobal = $this->createEventsForMarketAndDateRange(
            1,
            $this->globalMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_GLOBAL_VIRTUAL
            ]
        )->first();

        //get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        //verify the featured event is the local event
        $this->assertEquals($expectedLocal->uuid, $eventsColl->featuredEvent->uuid);

        //verify the number of events in the event collection
        $this->assertEquals(1, $eventsColl->events->count());

        //verify the first event in the collection is the global event
        $this->assertEquals($expectedGlobal->uuid, $eventsColl->events->first()->uuid);
    }

    /** @test */
    public function it_retrieves_events_only_from_global_events_for_the_global_market()
    {
        //Create the local event - it will also be the featured event
        $expectedLocal = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ]
        )->first();

        //create the expected global market event
        $expectedGlobal = $this->createEventsForMarketAndDateRange(
            1,
            $this->globalMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_GLOBAL_VIRTUAL
            ]
        )->first();

        //get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->globalMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        //verify the featured event is the global event
        $this->assertEquals($expectedGlobal->uuid, $eventsColl->featuredEvent->uuid);

        //verify there are no other events in the event collection
        $this->assertEquals(0, $eventsColl->events->count());
    }

    /** @test */
    public function it_does_not_retrieve_events_from_disabled_markets()
    {
        //disable the local market
        $this->localMarket->update(['enabled' => false]);

        //Create the local event - it will also be the featured event
        $expectedLocal = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ]
        )->first();

        //create the expected global market event
        $expectedGlobal = $this->createEventsForMarketAndDateRange(
            1,
            $this->globalMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_GLOBAL_VIRTUAL
            ]
        )->first();

        //get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        //verify the number of events in the event collection
        $this->assertEquals(0, $eventsColl->events->count());

        //verify the featured event is the global event
        $this->assertEquals($expectedGlobal->uuid, $eventsColl->featuredEvent->uuid);
    }

    /** @test */
    public function it_does_not_retrieve_events_from_other_markets_except_the_global_market()
    {
        //Create the local event - it will also be the featured event
        $expectedLocal = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ]
        )->first();

        //create the expected global market event
        $expectedGlobal = $this->createEventsForMarketAndDateRange(
            1,
            $this->globalMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_GLOBAL_VIRTUAL
            ]
        )->first();

        //Create a 3rd market
        $otherMarket = factory(Market::class)->create([
            'timezone' => new DateTimeZone('UTC')
        ]);

        //create an event for that market
        $otherEvent = $this->createEventsForMarketAndDateRange(
            1,
            $otherMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ]
        )->first();

        //get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        //verify the featured event is the local event
        $this->assertEquals($expectedLocal->uuid, $eventsColl->featuredEvent->uuid);

        //verify the number of events in the event collection
        $this->assertEquals(1, $eventsColl->events->count());

        //verify the first event in the collection is the global event
        $this->assertEquals($expectedGlobal->uuid, $eventsColl->events->first()->uuid);

        //verify the other event is not in the collection
        $this->assertFalse($eventsColl->events->pluck('uuid')->contains($otherEvent->uuid));
    }

    /** @test */
    public function it_does_not_retrieve_global_events_if_enough_local_events_exist()
    {
        //create local event
        $localEvents = $this->createEventsForMarketAndDateRange(
            Event::MINIMUM_NUMBER_OF_EVENTS,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_IN_PERSON
            ]
        );

        //create the global market events
        $globalEvents = $this->createEventsForMarketAndDateRange(
            Event::MINIMUM_NUMBER_OF_EVENTS,
            $this->globalMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_GLOBAL_VIRTUAL
            ]
        );

        //get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        //verify the number of events in the event collection
        $this->assertEquals(Event::MINIMUM_NUMBER_OF_EVENTS - 1, $eventsColl->events->count());

        //verify the featured event is from the local events
        $this->assertTrue($localEvents->pluck('uuid')->contains($eventsColl->featuredEvent->uuid));

        //verify all the other events are from local events
        //we do this by seeing how many of the uuids in the returned events do not exist in the local events
        $localUuidDiff = $eventsColl->events->pluck('uuid')->diff($localEvents->pluck('uuid'))->count();
        $this->assertEquals(0, $localUuidDiff);

        //verify none of the global events are in the events collection
        //we do this by seeing how many of the uuids in the returned events exist in the global events
        $globalUuidIntersect = $eventsColl->events->pluck('uuid')->intersect($globalEvents->pluck('uuid'))->count();
        $this->assertEquals(0, $globalUuidIntersect);
    }

    /** @test */
    public function it_retrieves_events_from_the_next_period_if_not_enough_exist_for_the_current_period()
    {
        //create in range local events
        $currentPeriodEvents = $this->createEventsForMarketAndDateRange(
            2,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_IN_PERSON
            ]
        );

        //get the local events for the next date range
        $nextPeriodEvents = $this->createEventsForMarketAndDateRange(
            2,
            $this->localMarket,
            $this->eventsDateRangeStart->copy()->addWeeks(2),
            $this->eventsDateRangeEnd->copy()->addWeeks(2),
            [
                Event::EVENT_FLAG_IN_PERSON
            ]
        );

        //get the local events for the next date range
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        //verify the featured event is in range
        $this->assertTrue($currentPeriodEvents->pluck('uuid')->contains($eventsColl->featuredEvent->uuid));

        //verify next period events are included in the returned events collection
        //we do this by seeing that all the next period event uuids are in the returned event uuids
        $nextPeriodDiff = $nextPeriodEvents->pluck('uuid')->diff($eventsColl->events->pluck('uuid'))->count();
        $this->assertEquals(0, $nextPeriodDiff);
    }

    /** @test */
    public function it_does_not_retrieve_event_outside_the_current_and_next_date_range()
    {
        //create in range local events
        $currentPeriodEvents = $this->createEventsForMarketAndDateRange(
            2,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_IN_PERSON
            ]
        );

        //get the local events for the next date range
        $nextPeriodEvents = $this->createEventsForMarketAndDateRange(
            2,
            $this->localMarket,
            $this->eventsDateRangeStart->copy()->addWeeks(2),
            $this->eventsDateRangeEnd->copy()->addWeeks(2),
            [
                Event::EVENT_FLAG_IN_PERSON
            ]
        );

        //merge the 2
        $inRangeEvents = $currentPeriodEvents->merge($nextPeriodEvents);

        //get the local events for the next date range
        $outOfRangeEvents = $this->createEventsForMarketAndDateRange(
            2,
            $this->localMarket,
            $this->eventsDateRangeStart->copy()->addWeeks(4),
            $this->eventsDateRangeEnd->copy()->addWeeks(4),
            [
                Event::EVENT_FLAG_IN_PERSON
            ]
        );

        //get the local events for the next date range
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        //verify the featured event is in range
        $this->assertTrue($currentPeriodEvents->pluck('uuid')->contains($eventsColl->featuredEvent->uuid));

        //verify none of the out of range events are in the events collection
        //we do this by seeing that all the uuids returned are in the in range event
        $inRangeDiff = $eventsColl->events->pluck('uuid')->diff($inRangeEvents->pluck('uuid'))->count();
        $this->assertEquals(0, $inRangeDiff);

        //and how many of the uuids in the returned events exist in the in the out range events
        $outOfRangeIntersect = $eventsColl->events->pluck('uuid')->intersect($outOfRangeEvents->pluck('uuid'))->count();
        $this->assertEquals(0, $outOfRangeIntersect);
    }

    /** @test */
    public function it_prioritizes_in_person_events_when_getting_events_for_each_date_subset()
    {
        //create the expected featured event with all the required flags
        $expectedFeaturedEvent = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ],
            [
                'popularity' => 10
            ]
        )->first();

        //create events for each date subset with the required flags for in person,
        // and an extra event for each subset not in person
        $firstSubsetInPersonEvents = $this->createEventsForMarketAndDateRange(
            5,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeStart->copy()->modify('next sunday')->endOfDay(),
            [
                Event::EVENT_FLAG_IN_PERSON
            ],
            [
                'popularity' => 1
            ]
        );

        $firstSubsetOtherEvents = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeStart->copy()->modify('next sunday')->endOfDay(),
            [
                Event::EVENT_FLAG_LOCAL_VIRTUAL
            ],
            [
                'popularity' => 10
            ]
        );

        $secondSubsetInPersonEvents = $this->createEventsForMarketAndDateRange(
            3,
            $this->localMarket,
            $this->eventsDateRangeStart->copy()->modify('next Monday'),
            $this->eventsDateRangeStart->copy()->modify('next Thursday')->endOfDay(),
            [
                Event::EVENT_FLAG_IN_PERSON
            ],
            [
                'popularity' => 1
            ]
        );

        $secondSubsetOtherEvents = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart->copy()->modify('next Monday'),
            $this->eventsDateRangeStart->copy()->modify('next Thursday')->endOfDay(),
            [
                Event::EVENT_FLAG_LOCAL_VIRTUAL
            ],
            [
                'popularity' => 10
            ]
        );

        $thirdSubsetInPersonEvents = $this->createEventsForMarketAndDateRange(
            3,
            $this->localMarket,
            $this->eventsDateRangeStart->copy()->modify('second Friday'),
            $this->eventsDateRangeStart->copy()->modify('second Sunday')->endOfDay(),
            [
                Event::EVENT_FLAG_IN_PERSON
            ],
            [
                'popularity' => 1
            ]
        );

        $thirdSubsetOtherEvents = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart->copy()->modify('second Friday'),
            $this->eventsDateRangeStart->copy()->modify('second Sunday')->endOfDay(),
            [
                Event::EVENT_FLAG_LOCAL_VIRTUAL
            ],
            [
                'popularity' => 10
            ]
        );

        $fourthSubsetInPersonEvents = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart->copy()->modify('second Monday'),
            $this->eventsDateRangeStart->copy()->modify('second Wednesday')->endOfDay(),
            [
                Event::EVENT_FLAG_IN_PERSON
            ],
            [
                'popularity' => 1
            ]
        );

        $fourthSubsetOtherEvents = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart->copy()->modify('second Monday'),
            $this->eventsDateRangeStart->copy()->modify('second Wednesday')->endOfDay(),
            [
                Event::EVENT_FLAG_LOCAL_VIRTUAL
            ],
            [
                'popularity' => 10
            ]
        );

        //Merge the subsets
        $inPersonEvents = $firstSubsetInPersonEvents->merge($secondSubsetInPersonEvents)
            ->merge($thirdSubsetInPersonEvents)
            ->merge($fourthSubsetInPersonEvents);

        $otherEvents = $firstSubsetOtherEvents->merge($secondSubsetOtherEvents)
            ->merge($thirdSubsetOtherEvents)
            ->merge($fourthSubsetOtherEvents);

        //get the local events for the next date range
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        //verify the featured event
        $this->assertEquals($expectedFeaturedEvent->uuid, $eventsColl->featuredEvent->uuid);

        //verify the number of returned events equals the number of in person event
        $this->assertEquals($eventsColl->events->count(), $inPersonEvents->count());

        //verify all the in person events were returned
        $inPersonDiff = $eventsColl->events->pluck('uuid')->diff($inPersonEvents->pluck('uuid'))->count();
        $this->assertEquals(0, $inPersonDiff);

        //verify none of the other events were returned
        $otherEventsIntersect = $eventsColl->events->pluck('uuid')->intersect($otherEvents->pluck('uuid'))->count();
        $this->assertEquals(0, $otherEventsIntersect);
    }

    /** @test */
    public function it_pads_returned_events_from_other_results_if_not_enough_in_person_events_are_found_for_date_subsets()
    {
        //create the expected featured event with all the required flags
        $expectedFeaturedEvent = $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ],
            [
                'popularity' => 10
            ]
        )->first();

        //create events for each date subset with the required flags for in person,
        // and an extra event for each subset not in person
        $firstSubsetInPersonEvents = $this->createEventsForMarketAndDateRange(
            1, //event gathering tries for 5 since there's a featured event
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeStart->copy()->modify('next sunday')->endOfDay(),
            [
                Event::EVENT_FLAG_IN_PERSON
            ],
            [
                'popularity' => 1
            ]
        );

        $firstSubsetOtherEvents = $this->createEventsForMarketAndDateRange(
            2,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeStart->copy()->modify('next sunday')->endOfDay(),
            [
                Event::EVENT_FLAG_LOCAL_VIRTUAL
            ],
            [
                'popularity' => 10
            ]
        );

        $secondSubsetInPersonEvents = $this->createEventsForMarketAndDateRange(
            1, //event gathering tries for 3
            $this->localMarket,
            $this->eventsDateRangeStart->copy()->modify('next Monday'),
            $this->eventsDateRangeStart->copy()->modify('next Thursday')->endOfDay(),
            [
                Event::EVENT_FLAG_IN_PERSON
            ],
            [
                'popularity' => 1
            ]
        );

        $secondSubsetOtherEvents = $this->createEventsForMarketAndDateRange(
            2,
            $this->localMarket,
            $this->eventsDateRangeStart->copy()->modify('next Monday'),
            $this->eventsDateRangeStart->copy()->modify('next Thursday')->endOfDay(),
            [
                Event::EVENT_FLAG_LOCAL_VIRTUAL
            ],
            [
                'popularity' => 10
            ]
        );

        $thirdSubsetInPersonEvents = $this->createEventsForMarketAndDateRange(
            1, //event gathering tries for 3
            $this->localMarket,
            $this->eventsDateRangeStart->copy()->modify('second Friday'),
            $this->eventsDateRangeStart->copy()->modify('second Sunday')->endOfDay(),
            [
                Event::EVENT_FLAG_IN_PERSON
            ],
            [
                'popularity' => 1
            ]
        );

        $thirdSubsetOtherEvents = $this->createEventsForMarketAndDateRange(
            2,
            $this->localMarket,
            $this->eventsDateRangeStart->copy()->modify('second Friday'),
            $this->eventsDateRangeStart->copy()->modify('second Sunday')->endOfDay(),
            [
                Event::EVENT_FLAG_LOCAL_VIRTUAL
            ],
            [
                'popularity' => 10
            ]
        );

        $fourthSubsetInPersonEvents = $this->createEventsForMarketAndDateRange(
            1, //event gathering tries for 1
            $this->localMarket,
            $this->eventsDateRangeStart->copy()->modify('second Monday'),
            $this->eventsDateRangeStart->copy()->modify('second Wednesday')->endOfDay(),
            [
                Event::EVENT_FLAG_IN_PERSON
            ],
            [
                'popularity' => 1
            ]
        );

        $fourthSubsetOtherEvents = $this->createEventsForMarketAndDateRange(
            2,
            $this->localMarket,
            $this->eventsDateRangeStart->copy()->modify('second Monday'),
            $this->eventsDateRangeStart->copy()->modify('second Wednesday')->endOfDay(),
            [
                Event::EVENT_FLAG_LOCAL_VIRTUAL
            ],
            [
                'popularity' => 10
            ]
        );

        //Merge the subsets
        $inPersonEvents = $firstSubsetInPersonEvents->merge($secondSubsetInPersonEvents)
            ->merge($thirdSubsetInPersonEvents)
            ->merge($fourthSubsetInPersonEvents);

        $otherEvents = $firstSubsetOtherEvents->merge($secondSubsetOtherEvents)
            ->merge($thirdSubsetOtherEvents)
            ->merge($fourthSubsetOtherEvents);

        //get the local events for the next date range
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        //verify the number of events returned ( Event::MINIMUM_NUMBER_OF_EVENTS - 1 for the featured event)
        $this->assertEquals($eventsColl->events->count(), Event::MINIMUM_NUMBER_OF_EVENTS - 1);

        //verify the featured event is from the in person events
        $this->assertEquals($expectedFeaturedEvent->uuid, $eventsColl->featuredEvent->uuid);

        //verify all the in person events were returned
        $inPersonDiff = $inPersonEvents->pluck('uuid')->diff($eventsColl->events->pluck('uuid'))->count();
        $this->assertEquals(0, $inPersonDiff);

        //get all the event uuids that are not from in person event,
        // and verify that all of those can be found in the other events collection
        // use values() to reset the keys
        $eventsOtherEventsUuids = $eventsColl->events->pluck('uuid')->diff($inPersonEvents->pluck('uuid'))->values();
        $otherEventsDiff = $eventsOtherEventsUuids->diff($eventsColl->events->pluck('uuid'))->count();
        $this->assertEquals(0, $otherEventsDiff);
    }

    /** @test */
    public function same_recurring_event_uuids_removes_duplicates()
    {
        //create the featured event with all the required flags
        $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ]
        )->first();

        // Create events with the same name but different venues
        $events = $this->createEventsForMarketAndDateRange(
            2,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL_GEM
            ]
        );

        // Set the same recurring event uuid for both events
        $events[0]->update(['recurring_event_uuid' => 'TestRecurringEventUuid']);
        $events[1]->update(['recurring_event_uuid' => 'TestRecurringEventUuid']);

        // Get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        // Ensure that both events are present since they have different names
        $this->assertCount(1, $eventsColl->events);
    }

    /** @test */
    public function same_names_different_venues_produce_unique_events()
    {
        //create the featured event with all the required flags
        $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ]
        )->first();

        // Create events with the same name but different venues
        $events = $this->createEventsForMarketAndDateRange(
            2,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL_GEM
            ]
        );

        // Set the same name for both events
        $events[0]->update(['name' => 'Event with Same Name']);
        $events[1]->update(['name' => 'Event with Same Name']);

        // Get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        // Ensure that both events are present since they have different venues
        $this->assertCount(2, $eventsColl->events);
    }

    /** @test */
    public function same_venues_different_names_produce_unique_events()
    {
        //create the featured event with all the required flags
        $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ]
        )->first();

        // Create events with the same venue but different names
        $events = $this->createEventsForMarketAndDateRange(
            2,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL_GEM
            ]
        );

        $venueData =             [
            'venue' => [
                'uuid' => 'unique-venue',
                'name' => 'same-venue-name',
                'city' => 'same-venue-city',
                'region' => 'same-venue-region',
                'postal_code' => '00000',
            ]
        ];

        // Set the same venue for both events
        $events[0]->update($venueData);
        $events[1]->update($venueData);

        // Get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        // Ensure that both events are present since they have different names
        $this->assertCount(2, $eventsColl->events);
    }

    /** @test */
    public function different_names_and_venues_produce_unique_events()
    {
        //create the featured event with all the required flags
        $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ]
        )->first();

        // Create events with different names and venues
        $this->createEventsForMarketAndDateRange(
            2,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL_GEM
            ]
        );

        // Get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        // Ensure that both events are present since they have different names and venues
        $this->assertCount(2, $eventsColl->events);
    }

    /** @test */
    public function same_venues_with_different_uuids_same_names_removes_duplicates()
    {
        //create the featured event with all the required flags
        $this->createEventsForMarketAndDateRange(
            1,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL,
                Event::EVENT_FLAG_LOCAL_GEM,
                Event::EVENT_FLAG_DONT_MISS,
                Event::EVENT_FLAG_IN_PERSON
            ]
        )->first();

        // Create events with the same name but different venues
        $events = $this->createEventsForMarketAndDateRange(
            2,
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd,
            [
                Event::EVENT_FLAG_LOCAL_GEM
            ]
        );

        // Set the same name for both events
        $events[0]->update(['name' => 'Event with Same Name']);
        $events[1]->update(['name' => 'Event with Same Name']);

        $venueData = [
            'venue' => [
                'uuid' => $this->faker->uuid,
                'name' => 'same-venue-name',
                'city' => 'same-venue-city',
                'region' => 'same-venue-region',
                'postal_code' => '00000',
                'latitude' => $this->faker->latitude,
                'longitude' => $this->faker->longitude,
            ]
        ];

        //set the venue data for the first event
        $events[0]->update($venueData);

        //Update the uuid and set the venue for the second event
        $venueData['venue']['uuid'] = $this->faker->uuid;
        $events[1]->update($venueData);

        // Get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        // Ensure that both events are present since they have different names
        $this->assertCount(1, $eventsColl->events);
    }

    /** @test */
    public function it_return_single_event_when_duplicated_based_on_date_range()
    {
        $marketUuid = "54ba5228-46c2-3cdd-9d6f-************";
        $singleEventUuid = "4a505745-3d06-43eb-bbca-501fedc256e4";
        $eventUuid1 = "9e44064b-4a86-30d5-ace2-4dc2bf9f32ae";
        $eventUuid2 = "dec0e0c8-3489-3929-96e8-959c8c3ca6d2";
        $eventUuid3 = "1f728576-137d-3d31-8065-7ea5653ffa6a";

        $eventName = "Test Disney On Ice";
        $eventFlags = [
            Event::EVENT_FLAG_LOCAL_GEM,
            Event::EVENT_FLAG_IN_PERSON
        ];

        $eventVenue = new Venue([
            'uuid' => '5929f804-c1c7-47bb-9423-58fed4fca3ee',
            'name' => 'Test Venue',
            'address_1' => '701 1st Ave N',
            'address_2' => '',
            'city' => 'New York',
            'region' => 'NY',
            'postal_code' => '55403',
            'country' => '',
            'phone' => null,
            'url' => null,
            'latitude' => '44.970299',
            'longitude' => '-93.286102',
            'space' => '',
        ]);

        //Event dates - create them out of order
        $eventDates = [
            $this->eventsDateRangeStart->copy()->addDays(2),
            $this->eventsDateRangeStart->copy()->addDays(15),
            $this->eventsDateRangeStart->copy()->addDays(4),
            $this->eventsDateRangeStart->copy()->addDays(20)
        ];

        $this->localMarket = Market::find($marketUuid);

        if (! $this->localMarket) {
            $this->localMarket = factory(Market::class)->create([
                'uuid' => $marketUuid,
                'name' => 'TEST MARKET',
                'timezone' => new DateTimeZone('America/New_York'),
            ]);
        }

        $events = collect();
        $eventUuids = [$singleEventUuid, $eventUuid1, $eventUuid2, $eventUuid3];

        foreach ($eventUuids as $uuid) {
            $event = Event::find($uuid) ?? factory(Event::class)->create([
                    'uuid' => $uuid,
                    'name' => $eventName,
                    'venue' => $eventVenue,
                    'flags' => $eventFlags,
                ]);

            $event->markets()->attach($this->localMarket);
            $events->push($event);
        }

        $events->each(function (Event $event, $index) use ($eventDates) {
            $this->setupEventDate($event, $eventDates[$index], $eventDates[$index]->copy()->addHours(6));
        });

        // Ensure the events are associated with the market
        foreach ($events as $event) {
            $this->assertTrue($event->markets->contains($this->localMarket));
        }

        // Get the events for the date ranges
        $eventsColl = Event::getForMailing(
            $this->localMarket,
            $this->eventsDateRangeStart,
            $this->eventsDateRangeEnd
        );

        $this->assertCount(1, $eventsColl->events);

        $this->assertEquals($eventsColl->events->first()->name, $eventName);
        $this->assertEquals(json_encode($eventsColl->events->first()->venue), json_encode($eventVenue));
    }

    private function setupEventDate(Event $event, $startDate, $endDate)
    {
        $eventStartDate = $this->faker->dateTimeBetween(
            $startDate,
            $endDate
        );

        factory(EventDate::class)->create([
            'event_uuid' => $event->uuid,
            'starts_at' => $eventStartDate,
        ]);
    }

    private function createEventsForMarketAndDateRange(
        int $count,
        Market $market,
        Carbon $startDate,
        Carbon $endDate,
        array $flags,
        array $otherAttributes = [],
        array $venueData = []
    ) : Collection {
        //Create the events
        $attributes = array_merge($otherAttributes, [
            'flags' => $flags
        ]);

        $eventData = $attributes;

        if ($venueData) {
            $eventData = array_merge($eventData, $venueData);
        }

        $events = factory(Event::class, $count)->create($eventData);

        //for each event:
        $events->each(function (Event $event) use ($market, $startDate, $endDate, $otherAttributes) {
            //add the event to the market
            $event->markets()->attach($market);

            //set the start time to any time between the passed start and end dates
            $eventStartDate = $this->faker->dateTimeBetween(
                $startDate,
                $endDate
            );

            //add an EventDate for the event
            //, up to 3 days after that
            //need to make sure it will not happen later than a Sunday so we can get a featured event
            factory(EventDate::class)->create([
                'event_uuid' => $event->uuid,
                'starts_at' => $eventStartDate,
            ]);
        });

        //return the collection of events
        return $events;
    }
}
