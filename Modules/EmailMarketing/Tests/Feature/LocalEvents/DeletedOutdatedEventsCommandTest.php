<?php

namespace Modules\EmailMarketing\Tests\Feature\LocalEvents;

use Carbon\Carbon;
use Modules\EmailMarketing\Console\RemoveOutdatedEvents;
use Modules\EmailMarketing\Models\EventDate;
use Modules\EmailMarketing\Models\Event;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Artisan;
use Tests\RefreshDatabase;
use Tests\TestCase;

class DeletedOutdatedEventsCommandTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /** @test */
    public function the_console_command_deletes_events_that_are_outdated()
    {
        //create some events, and add outdated event dates
        $events = factory(Event::class, 3)->create();
        $events->each(function ($event) {
            $endDate = $this->faker->dateTimeBetween(
                Carbon::now()->subDays(14),
                Carbon::now()->subDays(2)
            )->format('Y-m-d H:i:s');

            factory(EventDate::class)->create([
                'event_uuid' => $event->uuid,
                'starts_at'  => Carbon::parse($endDate)->subDays(2),
                'ends_at'    => $endDate
            ]);
        });

        //Add 1 more date (in the future) for the last event
        factory(EventDate::class)->create([
            'event_uuid' => $events->last()->uuid,
            'starts_at'  => $this->faker->dateTimeBetween(
                Carbon::now()->addDays(2),
                Carbon::now()->addDays(3)
            )
                ->format('Y-m-d H:i:s'),
        ]);

        //Before: check that there are 3 events in the db currently
        $this->assertEquals(3, Event::count());

        //Run the artisan command
        Artisan::call(RemoveOutdatedEvents::class);

        //After: Assert there is only 1 event in the database
        $this->assertEquals(1, Event::count());
    }
}
