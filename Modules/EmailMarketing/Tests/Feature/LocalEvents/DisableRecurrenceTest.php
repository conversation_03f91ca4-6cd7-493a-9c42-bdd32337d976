<?php

namespace Tests\Feature\Event\Listener;

use App\Events\AccountRemovedFromLocalEvents;
use App\EventSourcing\Broker;
use App\Models\Plan;
use Illuminate\Foundation\Testing\WithFaker;
use Infrastructure\Repositories\Titan\TitanApiRepository;
use Mockery;
use Modules\EmailMarketing\Models\RecurrenceSettings;
use Tests\RefreshDatabase;
use Tests\TestCase;

class DisableRecurrenceTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $this->account->plans()->sync([
            Plan::PLAN_ID_AMERICAN_LIFESTYLE_MAGAZINE,
            Plan::PLAN_ID_LOCAL_EVENT,
        ]);
    }

    /** @test */
    public function it_disables_recurrence_on_account_removed_from_local_events()
    {
        // set expectations for titan repository calls
        $titanRepo = Mockery::mock(TitanApiRepository::class);
        $titanRepo->shouldReceive('updateDigitalProductAssociations')->once()->andReturn([]);
        $titanRepo->shouldReceive('getRecipientGroups')
            ->times(2)
            ->andReturn(['recipientGroups' => [[
                'id' => $this->faker->uuid,
                'name' => 'Test Group',
                'meta' => [
                    "allMembers" => 75,
                    "activeMembers" => 75,
                    "inactiveMembers" => 0,
                    "addressWillSend" => 72,
                    "addressPaused" => 0,
                    "addressBad" => 0,
                    "emailValid" => 73,
                    "emailBad" => 2,
                    "addressWillNotSend" => 3,
                    "sendAnyway" => 0,
                    "deliverAnyway" => 0,
                    "postcardWillSend" => 72
                ]
            ]]]);
        $this->app->instance(TitanApiRepository::class, $titanRepo);

        /** @var \Modules\EmailMarketing\Models\RecurrenceSettings $recurrence */
        $recurrence = factory(RecurrenceSettings::class)->create([
            'is_enabled' => true,
        ]);

        // handle AccountRemovedFromLocalEvents
        $this->app->make(Broker::class)
            ->handle($this->makeAccountRemovedFromLocalEvents());

        // assert recurrence is disabled
        $this->assertFalse($recurrence->refresh()->is_enabled, 'Recurrence was not disabled');
    }

    private function makeAccountRemovedFromLocalEvents()
    {
        return AccountRemovedFromLocalEvents::fromArray([
            'uuid'        => $this->faker->uuid,
            'occurred_at' => now()->format('Y-m-d H:i:s'),
            'global_name' => 'account.removed-from-local-content',
            'data'        => ['plan_id' => Plan::PLAN_ID_LOCAL_EVENT],
            'metadata'    => [
                'source'  => 'crm',
                'context' => ['account_id' => $this->account->id],
            ],
        ]);
    }
}
