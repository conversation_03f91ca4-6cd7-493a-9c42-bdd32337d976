<?php
namespace Modules\EmailMarketing\Tests\Feature\LocalEvents;

use App\Models\ContactBlock;
use App\Models\ContactBlockItem;
use App\Models\EmailAddress;
use App\Models\Plan;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\WithFaker;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\EventCollection;
use Modules\EmailMarketing\Domain\LocalEvents\Entities\MarketEvents;
use Modules\EmailMarketing\DTO\MailingContentDTO;
use Modules\EmailMarketing\Models\Event as LocalEvent;
use Modules\EmailMarketing\Models\EventDate;
use Modules\EmailMarketing\Models\Market;
use Modules\EmailMarketing\Models\RecurrenceSettings;
use Modules\EmailMarketing\Services\EventMailingContentService;
use Modules\Mailing\Models\Mailing;
use Tests\RefreshDatabase;
use Tests\TestCase;

class MailContentServiceTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /** @var \App\Models\EmailAddress */
    private $emailAddress;

    /** @var \App\Models\ContactBlock */
    private $contactBlock;

    /** @var \Carbon\Carbon */
    private $nextMailingDate;

    /** @var \Carbon\Carbon */
    private $eventsDateRangeStart;

    /** @var \Carbon\Carbon */
    private $eventsDateRangeEnd;

    /** @var string */
    private $displayName;

    /** @var string */
    private $defaultSubject;

    /** @var \Modules\EmailMarketing\Models\Market */
    private $market;

    /** @var \Modules\EmailMarketing\Models\Event */
    private $featuredEvent;

    /** @var \Modules\EmailMarketing\Domain\LocalEvents\Entities\MarketEvents */
    private $marketEvents;

    protected function setUp(): void
    {
        parent::setUp();

        //Create a market
        $this->market = factory(Market::class)->create();

        //Create an account, and assign the market
        $this->account->update(['market_uuid' => $this->market->uuid]);

        //create an email address for the account
        $this->emailAddress = factory(EmailAddress::class);

        //set the next mailing date
        $this->nextMailingDate = Carbon::now();

        //Create an event (it will also be a featured event)
        $this->featuredEvent = factory(LocalEvent::class)->create(['flags' => [LocalEvent::EVENT_FLAG_LOCAL, LocalEvent::EVENT_FLAG_DONT_MISS, LocalEvent::EVENT_FLAG_IN_PERSON]]);

        //attach the event to the location
        $this->featuredEvent->markets()->attach($this->market);

        //add an event date for the event
        //set the start time to any time between the day after the mailing date, up to 3 days after that
        //need to make sure it will not happen later than a Sunday so we can get a featured event
        factory(EventDate::class)->create([
            'event_uuid' => $this->featuredEvent->uuid,
            'starts_at'  => $this->faker->dateTimeBetween(
                $this->nextMailingDate->copy()->addDays(2),
                $this->nextMailingDate->copy()->addDays(3)
            )
                ->format('Y-m-d H:i:s'),
        ]);

        //create a contact block for the account
        $this->displayName = $this->faker->name;
        $this->contactBlock = factory(ContactBlock::class)->create([
            'product'    => ContactBlock::PRODUCT_LOCAL_EVENTS,
        ]);

        factory(ContactBlockItem::class)->create([
            'contact_block_id' => $this->contactBlock->id,
            'item_type'        => ContactBlockItem::ITEM_TYPE_DISPLAY_NAME,
            'item_id'          => null,
            'custom'           => $this->displayName,
        ]);

        //get the date ranges used in the mailing object
        $this->eventsDateRangeStart = RecurrenceSettings::getEventsStartDate($this->nextMailingDate, $this->market->timezone);
        $this->eventsDateRangeEnd = RecurrenceSettings::getEventsEndDate($this->nextMailingDate, $this->market->timezone, 'bi-weekly');


        //create the MarketEvents collection
        $this->marketEvents = new MarketEvents(
            $this->market,
            $this->featuredEvent,
            EventCollection::make([])
        );
    }

    /** @test */
    public function mail_content_service_returns_the_correct_email_contents()
    {
        //TODO - Verify the test works using the mailing module mailing model

        // todo update this test or remove it?
        $this->markTestSkipped("Due to blog content stuff and rearrangement, I don't have time to fix");

        //create a new mailing
        $mailingObj = factory(Mailing::class)->create([
            'product'                    => Plan::PLAN_ID_LOCAL_EVENT,
            'email_from_address'         => $this->emailAddress->email,
            'featured_image_id'          => null,
            'html_body'                  => null,
            'test_body'                  => null,
            'send_at'                    => $this->nextMailingDate,
            'properties'                 => [
                'market_uuid'            => $this->market->uuid,
                'event_dates_start_at'   => $this->eventsDateRangeStart,
                'event_dates_end_at'     => $this->eventsDateRangeEnd,
            ]
        ]);

        //create a DTO from the mailing object
        $mailingDto = MailingContentDTO::fromModel($mailingObj);

        //use the MailingContentService to generate the mailing contents
        /** @var EventMailingContentService $mailingContentService */
        $mailingContentService = app(EventMailingContentService::class);
        $mailContents = $mailingContentService->generate($mailingDto, $this->account->id);

        //Compile data manually
        //Get the view data
        //TODO - build the view data manually if keeping this test

        //Calculate what the mailing contents should be
        $expectedFeaturedImageUrl = $this->featuredEvent->image_url;
        $expectedHtmlContents = view('emails.local-events.default-html', $viewData)->render();
        $expectedTextContents = view('emails.local-events.default-text', $viewData)->render();

        //Assert that the steps the mailing content service has taken to build the mail contents have the same result
        //as when manually calling the individual steps
        $this->assertEquals($expectedFeaturedImageUrl, $mailContents['featured_image_url']);
        $this->assertEquals($expectedHtmlContents, $mailContents['mail_content_html']);
        $this->assertEquals($expectedTextContents, $mailContents['mail_content_text']);
    }
}
