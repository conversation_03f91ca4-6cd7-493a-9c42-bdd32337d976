<?php

namespace Modules\EmailMarketing\Tests\Feature\LocalEvents;

use Modules\EmailMarketing\Console\RemoveEventsWithNoMarkets;
use Modules\EmailMarketing\Models\Market;
use Modules\EmailMarketing\Models\Event;
use Illuminate\Support\Facades\Artisan;
use Tests\RefreshDatabase;
use Tests\TestCase;

class DeleteEventsWithNoMarketsCommandTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function the_console_command_deletes_events_that_are_not_attached_to_a_market()
    {
        //Create an event and attach a market to it
        $eventWithMarket = factory(Event::class)->create();
        $market = factory(Market::class)->create();
        $eventWithMarket->markets()->syncWithoutDetaching($market);

        //create events without a market
        $eventsWithoutMarket = factory(Event::class, 3)->create();

        //Before: check that there are 4 events in the db currently
        $this->assertEquals(4, Event::count());

        //Run the artisan command
        Artisan::call(RemoveEventsWithNoMarkets::class);

        //After: Assert there is only 1 event in the database
        $this->assertEquals(1, Event::count());

    }
}
