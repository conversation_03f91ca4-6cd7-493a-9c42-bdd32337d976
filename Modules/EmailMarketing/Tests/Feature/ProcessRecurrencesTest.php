<?php

namespace Modules\EmailMarketing\Tests\Feature;

use App\Context\AccountId;
use App\Models\Account;
use App\Models\ContactBlock;
use Exception;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Mockery;
use Modules\EmailMarketing\Console\ProcessRecurrences;
use Modules\EmailMarketing\Models\Event as LocalEvent;
use Modules\EmailMarketing\Models\EventDate;
use Modules\EmailMarketing\Models\Market;
use Modules\EmailMarketing\Models\RecurrenceSettings;
use Modules\Mailing\Contracts\MailingRepository;
use Modules\Mailing\DataTransferObjects\MailingDTO;
use Tests\RefreshDatabase;
use Tests\TestCase;

class ProcessRecurrencesTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /** @var \Illuminate\Support\Collection */
    private $markets;

    /** @var \Carbon\Carbon */
    private $now;

    protected function setUp(): void
    {
        parent::setUp();

        $this->markets = factory(Market::class, 10)->create();
        $this->now = now();
    }

    /** @test */
    public function it_tests_happy_path()
    {
        $this->markTestSkipped("Need to come back to this ASAP (XRMC-2497)");
        // stub some recurrences
        $accounts = $this->createAccounts(2);
        $this->createEventsForMailing($accounts);
        $accountIds = $accounts->pluck('id')->toArray();
        $this->createRecurrences($accountIds);
        $this->createContactBlock($accountIds);

        // mock mailing service responses
        $this->mockMailingServiceCreateMailing($accountIds);

        // run command
        Artisan::call(ProcessRecurrences::class);

        // assert mailings in db
        $this->assertDatabaseHas('local_events_mailings', [
            'mailing_service_mailing_id' => 1,
            'mailing_subject'            => "Subject {$accountIds[0]}",
            'mailing_body'               => "Body {$accountIds[0]}",
        ]);

        $this->assertDatabaseHas('local_events_mailings', [
            'mailing_service_mailing_id' => 2,
            'mailing_subject'            => "Subject {$accountIds[1]}",
            'mailing_body'               => "Body {$accountIds[1]}",
        ]);

        // assert updated recurrences
        $this->assertDatabaseHas('local_events_recurrence_settings', [
            'account_id'      => $accountIds[0],
            'mailing_date'    => $this->now->copy()->addWeeks(2)->toDateTimeString(),
            'mailing_subject' => null,
            'mailing_heading' => null,
            'mailing_body'    => null,
            'is_enabled'      => true,
            'failed'          => false,
        ]);

        $this->assertDatabaseHas('local_events_recurrence_settings', [
            'account_id'      => $accountIds[1],
            'mailing_date'    => $this->now->copy()->addWeeks(2)->toDateTimeString(),
            'mailing_subject' => null,
            'mailing_heading' => null,
            'mailing_body'    => null,
            'is_enabled'      => true,
            'failed'          => false,
        ]);
    }

    /** @test */
    public function it_does_not_reset_subject_or_heading_on_success_if_they_are_persistent()
    {
        $this->markTestSkipped("Need to come back to this ASAP (XRMC-2497)");
        // stub some recurrences
        $accounts = $this->createAccounts(2);
        $this->createEventsForMailing($accounts);
        $accountIds = $accounts->pluck('id')->toArray();
        $recurrences = $this->createRecurrences($accountIds);
        $this->createContactBlock($accountIds);

        //update persistence on the recurrences
        $recurrences->each(function ($recurrence) {
            $recurrence->update([
                'persist_subject' => true,
                'persist_heading' => true,
            ]);
        });

        // mock mailing service responses
        $this->mockMailingServiceCreateMailing($accountIds);

        // run command
        Artisan::call(ProcessRecurrences::class);

        // assert unchanged recurrences
        $this->assertDatabaseHas('local_events_recurrence_settings', [
            'account_id'      => $accountIds[0],
            'mailing_date'    => $this->now->copy()->addWeeks(2)->toDateTimeString(),
            'mailing_subject' => "Subject " . $accountIds[0],
            'mailing_heading' => "Heading " . $accountIds[0],
            'mailing_body'    => null,
            'is_enabled'      => true,
            'failed'          => false,
        ]);

        $this->assertDatabaseHas('local_events_recurrence_settings', [
            'account_id'      => $accountIds[1],
            'mailing_date'    => $this->now->copy()->addWeeks(2)->toDateTimeString(),
            'mailing_subject' => "Subject " . $accountIds[1],
            'mailing_heading' => "Heading " . $accountIds[1],
            'mailing_body'    => null,
            'is_enabled'      => true,
            'failed'          => false,
        ]);
    }

    /** @test */
    public function it_does_not_create_a_mailing_record_if_there_are_no_events_for_the_market_in_the_date_range()
    {
        $this->markTestSkipped("Need to come back to this ASAP (XRMC-2497)");
        // create an account, a recurrence, and an event for the market
        $accounts = $this->createAccounts(1);
        $accountIds = $accounts->pluck('id')->toArray();
        $this->createRecurrences($accountIds);
        $this->createContactBlock($accountIds);

        // mock mailing service responses
        $this->mockMailingServiceCreateMailing($accountIds);

        // run command
        Artisan::call(ProcessRecurrences::class);

        // assert mailings in db
        $this->assertEquals(0, DB::table('local_events_mailings')->count());
        $this->assertDatabaseMissing('local_events_mailings', [
            'mailing_subject'            => "Subject {$accountIds[0]}",
            'mailing_body'               => "Body {$accountIds[0]}",
        ]);
    }

    /** @test */
    public function it_does_not_mark_a_recurrence_as_failed_if_there_are_no_events_for_the_market_in_the_date_range()
    {
        $this->markTestSkipped("Need to come back to this ASAP (XRMC-2497)");
        // create an account, a recurrence, and an event for the market
        $accounts = $this->createAccounts(1);
        $accountIds = $accounts->pluck('id')->toArray();
        $recurrences = $this->createRecurrences($accountIds);
        $this->createContactBlock($accountIds);

        // mock mailing service responses
        $this->mockMailingServiceCreateMailing($accountIds);

        // run command
        Artisan::call(ProcessRecurrences::class);

        //Verify the database
        $this->assertDatabaseHas('local_events_recurrence_settings', [
            'id'              => $recurrences[0]->id,
            'account_id'      => $accountIds[0],
            'failed'          => false,
        ]);
    }

    /** @test */
    public function it_updates_recurrence_mailing_date_and_subject_and_heading_and_mailing_body_if_there_are_no_events()
    {
        $this->markTestSkipped("Need to come back to this ASAP (XRMC-2497)");
        // create an account, a recurrence, and an event for the market
        $accounts = $this->createAccounts(1);
        $accountIds = $accounts->pluck('id')->toArray();
        $recurrences = $this->createRecurrences($accountIds);
        $this->createContactBlock($accountIds);

        // mock mailing service responses
        $this->mockMailingServiceCreateMailing($accountIds);

        // run command
        Artisan::call(ProcessRecurrences::class);

        //Verify the database
        $this->assertDatabaseHas('local_events_recurrence_settings', [
            'id'              => $recurrences[0]->id,
            'account_id'      => $accountIds[0],
            'mailing_date'    => $this->now->copy()->addWeeks(2)->toDateTimeString(),
            'mailing_subject' => null,
            'mailing_heading' => null,
            'mailing_body'    => null,
            'failed'          => false,
        ]);
    }

    /** @test */
    public function it_marks_recurrences_as_failed_if_mailing_service_returns_an_error()
    {
        $this->markTestSkipped("Need to come back to this ASAP (XRMC-2497)");
        // stub some recurrences
        $accounts = $this->createAccounts(2);
        $this->createEventsForMailing($accounts);
        $accountIds = $accounts->pluck('id')->toArray();
        $this->createRecurrences($accountIds);
        $this->createContactBlock($accountIds);

        // mock mailing service responses
        $this->mockMailingServiceCreateMailing([$accountIds[0]], [$accountIds[1]]);

        // run command
        Artisan::call(ProcessRecurrences::class);

        // assert mailings in db
        $this->assertDatabaseHas('local_events_mailings', [
            'mailing_service_mailing_id' => 1,
            'mailing_subject'            => "Subject {$accountIds[0]}",
            'mailing_body'               => "Body {$accountIds[0]}",
        ]);

        $this->assertEquals(1, DB::table('local_events_mailings')->count());

        // assert updated recurrences
        $this->assertDatabaseHas('local_events_recurrence_settings', [
            'account_id'      => $accountIds[0],
            'mailing_date'    => $this->now->copy()->addWeeks(2)->toDateTimeString(),
            'is_enabled'      => true,
            'failed'          => false,
        ]);

        $this->assertDatabaseHas('local_events_recurrence_settings', [
            'account_id'      => $accountIds[1],
            'mailing_date'    => $this->now->toDateTimeString(),
            'is_enabled'      => true,
            'failed'          => true,
        ]);
    }

    /** @test */
    public function it_resets_subject_and_heading_and_body_for_disabled_recurrences()
    {
        // stub some recurrences - make sure they're disabled
        $accounts = $this->createAccounts(2);
        $this->createEventsForMailing($accounts);
        $accountIds = $accounts->pluck('id')->toArray();
        $this->createRecurrences($accountIds,false);

        // mock mailing service responses
        $this->mockMailingServiceCreateMailing([$accountIds[0]], [$accountIds[1]]);

        // run command
        Artisan::call(ProcessRecurrences::class);

        // assert updated recurrences
        $this->assertDatabaseHas('local_events_recurrence_settings', [
            'account_id'      => $accountIds[0],
            'mailing_date'    => $this->now,
            'mailing_subject' => null,
            'mailing_heading' => null,
            'mailing_body'    => null,
            'is_enabled'      => false,
            'failed'          => false,
        ]);

        $this->assertDatabaseHas('local_events_recurrence_settings', [
            'account_id'      => $accountIds[1],
            'mailing_date'    => $this->now,
            'mailing_subject' => null,
            'mailing_heading' => null,
            'mailing_body'    => null,
            'is_enabled'      => false,
            'failed'          => false,
        ]);
    }

    /** @test */
    public function it_does_not_reset_subject_and_heading_for_disabled_recurrences_if_they_are_persistent()
    {
        $this->markTestIncomplete('Does this logic even apply any more?');

        // stub some recurrences - make sure they're disabled
        $accounts = $this->createAccounts(2);
        $this->createEventsForMailing($accounts);
        $accountIds = $accounts->pluck('id')->toArray();
        $recurrences = $this->createRecurrences($accountIds, false);

        //update persistence on the recurrences
        $recurrences->each(function ($recurrence) {
            $recurrence->update([
                'persist_subject' => true,
                'persist_heading' => true,
            ]);
        });

        // mock mailing service responses
        $this->mockMailingServiceCreateMailing([$accountIds[0]], [$accountIds[1]]);

        // run command
        Artisan::call(ProcessRecurrences::class);

        // assert updated recurrences
        $this->assertDatabaseHas('local_events_recurrence_settings', [
            'account_id'      => $accountIds[0],
            'mailing_date'    => $this->now,
            'mailing_subject' => "Subject " . $accountIds[0],
            'mailing_heading' => "Heading " . $accountIds[0],
            'mailing_body'    => null,
            'is_enabled'      => false,
            'failed'          => false,
        ]);

        $this->assertDatabaseHas('local_events_recurrence_settings', [
            'account_id'      => $accountIds[1],
            'mailing_date'    => $this->now,
            'mailing_subject' => "Subject " . $accountIds[1],
            'mailing_heading' => "Heading " . $accountIds[1],
            'mailing_body'    => null,
            'is_enabled'      => false,
            'failed'          => false,
        ]);
    }


    //*************************************************************

    private function createAccounts(int $times = 1) : Collection
    {
        $accounts = collect();

        for ($i = 1; $i <= $times; $i++) {
            $accounts->push(
                factory(Account::class)->create(['market_uuid' => $this->markets->random()->uuid])
            );
        }

        return $accounts;
    }

    private function createRecurrences(array $accountIds, bool $isEnabled = true) : Collection
    {
        $recurrences = collect();

        foreach ($accountIds as $accountId) {
            $recurrences->push(
                factory(RecurrenceSettings::class)->create([
                    'account_id'      => $accountId,
                    'mailing_date'    => $this->now,
                    'mailing_subject' => "Subject $accountId",
                    'persist_subject' => false,
                    'mailing_heading' => "Heading $accountId",
                    'persist_heading' => false,
                    'mailing_body'    => "Body $accountId",
                    'is_enabled'      => $isEnabled,
                ])
            );
        }

        return $recurrences;
    }

    private function mockMailingServiceCreateMailing(array $accountIdsToSucceed, array $accountIdsToFail = []) : void
    {
        $mock = Mockery::mock(MailingRepository::class);

        $mock
            ->shouldReceive('createMailing')
            ->andReturnUsing(function (MailingDTO $dto) use ($accountIdsToSucceed, $accountIdsToFail) {
                if (in_array($dto->account_id, $accountIdsToFail)) {
                    throw new Exception('mailing-service has blown up');
                }

                return array_search($dto->account_id, $accountIdsToSucceed) + 1;
            });

        $this->app->instance(MailingRepository::class, $mock);
    }

    private function createEventsForMailing(Collection $accounts)
    {
        //loop on the passed accounts
        $accounts->each(function (Account $account) {
            //create an event for the market
            $localEvent = factory(LocalEvent::class)->create(['flags' => [LocalEvent::EVENT_FLAG_LOCAL, LocalEvent::EVENT_FLAG_DONT_MISS, LocalEvent::EVENT_FLAG_IN_PERSON]]);

            //attach the event to the location
            $localEvent->markets()->attach($account->localContentMarket);

            //add an event date for the event
            //set the start time to any time between the day after the mailing date, up to 3 days after that
            //need to make sure it will not happen later than a Sunday so we can get a featured event
            factory(EventDate::class)->create([
                'event_uuid' => $localEvent->uuid,
                'starts_at'  => $this->faker->dateTimeBetween(
                    $this->now->copy()->addDays(2),
                    $this->now->copy()->addDays(3)
                )
                ->format('Y-m-d H:i:s'),
            ]);
        });
    }

    private function createContactBlock(array $accountIds)
    {
        foreach ($accountIds as $accountId) {
            (new AccountId($accountId))->execute(function () {
                factory(ContactBlock::class)->state('local-events')->create();
            });
        }
    }
}
