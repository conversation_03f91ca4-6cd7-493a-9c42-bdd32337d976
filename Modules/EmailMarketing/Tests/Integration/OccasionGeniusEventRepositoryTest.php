<?php

namespace Modules\EmailMarketing\Tests\Integration;

use Carbon\Carbon;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Response;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Collection;
use Infrastructure\Repositories\RestRepository\DefaultParser;
use Infrastructure\Repositories\RestRepository\RestClient;
use Modules\EmailMarketing\Entities\MarketId;
use Modules\EmailMarketing\OccasionGenius\ApiClient;
use Modules\EmailMarketing\Repositories\OccasionGeniusEventRepository;
use Tests\LandlordTestCase;

class OccasionGeniusEventRepositoryTest extends LandlordTestCase
{
    use WithFaker;

    /** @var \Modules\EmailMarketing\Repositories\OccasionGeniusEventRepository */
    private $eventRepository;

    /** @test */
    public function it_prepares_events_using_the_instance_date_field()
    {
        $stubFile = 'occasion-genius-events-dates-with-instance-date.json';

        //get an instance of the repo with the correct response mocked in the API client
        $eventRepository = $this->getEventRepositoryWithMockedApiForEvents($stubFile);

        $eventCollection = $eventRepository->getForLocation(
            MarketId::fromString($this->faker->uuid),
            $this->faker->randomNumber(),
            Carbon::now(),
            Carbon::now()->addDays(14)
        );

        //get the data array for the stubbed event
        $stubbedData = $this->getEventsDataFromStubFile($stubFile);
        $stubbedEvent = $stubbedData->first();

        //get the data for the mapped event from the repo
        $mappedDates = collect($eventCollection->first()->dates);

        //Build the expectations
        $expectedStart = Carbon::parse($stubbedEvent['instance_date'])->startOfDay();
        $expectedEnd = Carbon::parse($stubbedEvent['instance_date'])->endOfDay();

        //verify the event only have 1 date
        $this->assertEquals(1, $mappedDates->count());

        //verify the date values
        $this->assertEquals($expectedStart, $mappedDates->first()->startsAt);
        $this->assertEquals($expectedEnd, $mappedDates->first()->endsAt);
    }

    /** @test */
    public function it_can_prepare_events_with_no_instance_date_field()
    {
        $stubFile = 'occasion-genius-events-dates-without-instance-date.json';

        //get an instance of the repo with the correct response mocked in the API client
        $eventRepository = $this->getEventRepositoryWithMockedApiForEvents($stubFile);

        $eventCollection = $eventRepository->getForLocation(
            MarketId::fromString($this->faker->uuid),
            $this->faker->randomNumber(),
            Carbon::now(),
            Carbon::now()->addDays(14)
        );

        //get the data array for the stubbed event
        $stubbedData = $this->getEventsDataFromStubFile($stubFile);
        $stubbedEvent = $stubbedData->first();

        //get the data for the mapped event from the repo
        $mappedDates = collect($eventCollection->first()->dates);

        //Build the expectations
        $expectedStart = Carbon::parse($stubbedEvent['start_date']);
        $expectedEnd = Carbon::parse($stubbedEvent['end_date']);

        //verify the event only have 1 date
        $this->assertEquals(1, $mappedDates->count());

        //verify the date values
        $this->assertEquals($expectedStart, $mappedDates->first()->startsAt);
        $this->assertEquals($expectedEnd, $mappedDates->first()->endsAt);
    }

    /**
     * Creates a new instance of the OccasionGeniusEventRepository, and a mocked api client that returns the result
     * loaded from the parse json stub
     *
     * @param string $stubFile - the stuf file to use to build the api result
     * @return \Modules\EmailMarketing\Repositories\OccasionGeniusEventRepository
     */
    private function getEventRepositoryWithMockedApiForEvents(string $stubFile): OccasionGeniusEventRepository
    {
        //Mock the api client
        $client = $this->mock(Client::class)->makePartial();
        $client->shouldReceive('get')->andReturn(
            new Response(200, ['Content-Type' => 'application/json'], file_get_contents(__DIR__ . '/../stubs/' . $stubFile))
        );

        $ogApiClient = new ApiClient(new RestClient($client, new DefaultParser));

        return new OccasionGeniusEventRepository($ogApiClient);
    }

    private function getEventsDataFromStubFile(string $stubFile): Collection
    {
        $resArray = json_decode(file_get_contents(__DIR__ . '/../stubs/' . $stubFile), true);
        return collect($resArray['results']);
    }
}
