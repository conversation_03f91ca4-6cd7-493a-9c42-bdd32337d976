<?php

namespace Modules\EmailMarketing\Tests\Integration;

use Carbon\Carbon;
use GuzzleHttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Illuminate\Foundation\Testing\WithFaker;
use Infrastructure\Repositories\RestRepository\DefaultParser;
use Infrastructure\Repositories\RestRepository\RestClient;
use Modules\EmailMarketing\Entities\MarketId;
use Modules\EmailMarketing\OccasionGenius\ApiClient;
use Modules\EmailMarketing\OccasionGenius\Exceptions\UnableToGetEventsException;
use Modules\EmailMarketing\OccasionGenius\Exceptions\UnableToGetLocationsException;
use Tests\LandlordTestCase;

class OccasionGeniusApiTest extends LandlordTestCase
{
    use WithFaker;

    /** @var \Modules\EmailMarketing\OccasionGenius\ApiClient */
    private $apiClient;

    /** @var \GuzzleHttp\Handler\MockHandler */
    private $mock;

    /** @var \Modules\EmailMarketing\Entities\MarketId */
    private $marketId;

    /** @var array */
    private $container = [];

    protected function setUp(): void
    {
        parent::setUp();

        $history = Middleware::history($this->container);

        // Create a mock and queue two responses.
        $this->mock = new MockHandler([]);

        $handler = HandlerStack::create($this->mock);
        $handler->push($history);

        $client = new Client(['handler' => $handler]);

        $this->apiClient = new ApiClient(new RestClient($client, new DefaultParser));

        $this->marketId = MarketId::fromString($this->faker->uuid);
    }

    /** @test */
    public function it_successfully_gets_locations_from_api()
    {
        $this->mock->append(new Response(200, ['Content-Type' => 'application/json'], file_get_contents(__DIR__ . '/../stubs/occasion-genius-locations.json')));

        $locations = $this->apiClient->getLocations();

        $this->assertEquals(1, $locations->count());
    }

    /** @test */
    public function it_throws_custom_exception_when_request_fails()
    {
        $this->mock->append(new Response(401, ['Content-Type' => 'application/json'], file_get_contents(__DIR__ . '/../stubs/occasion-genius-locations.json')));

        $this->expectException(UnableToGetLocationsException::class);

        $this->apiClient->getLocations();
    }

    /** @test */
    public function it_throws_custom_exception_when_server_fails()
    {
        $this->mock->append(new Response(500, ['Content-Type' => 'application/json']));

        $this->expectException(UnableToGetLocationsException::class);

        $this->apiClient->getLocations();
    }

    /** @test */
    public function it_successfully_gets_events()
    {
        $this->mock->append(new Response(200, ['Content-Type' => 'application/json'], file_get_contents(__DIR__ . '/../stubs/occasion-genius-events-page-1.json')));

        $eventsResponse = $this->apiClient->getEventsForLocation($this->marketId);

        $this->assertEquals(10, $eventsResponse->events->count());
        $this->assertEquals(20, $eventsResponse->total);
    }

    /** @test */
    public function it_successfully_gets_raw_response_for_events()
    {
        $this->mock->append(new Response(200, ['Content-Type' => 'application/json'], file_get_contents(__DIR__ . '/../stubs/occasion-genius-events-page-1.json')));

        $rawResponse = $this->apiClient->getRawEventsFromApi(
            $this->marketId,
            10,
            Carbon::now(),
            Carbon::now()->addDays(2)
        );

        $expectedResult = json_decode(file_get_contents(__DIR__ . '/../stubs/occasion-genius-events-page-1.json'), true);

        $this->assertEquals($rawResponse, $expectedResult);
    }

    /** @test */
    public function it_successfully_gets_events_within_date_range()
    {
        $this->mock->append(new Response(200, ['Content-Type' => 'application/json'], file_get_contents(__DIR__ . '/../stubs/occasion-genius-events-page-1.json')));

        $startOn = Carbon::now()->startOfDay();
        // add 7 days, end of day and start of minute (microsecond throws things off)
        $endOn = $startOn->copy()->addDays(7)->endOfDay()->startOfMinute();

        $eventsResponse = $this->apiClient->getEventsForLocation($this->marketId, null, $startOn, $endOn);

        $this->assertEquals(10, $eventsResponse->events->count());
        $this->assertEquals(20, $eventsResponse->total);

        /** @var \GuzzleHttp\Psr7\Request $first */
        $first = $this->container[0]['request'];

        $this->assertQueryStringMatches($first, [
            'area_uuid' => $this->marketId->getValue()->toString(),
            'start_date'     => $startOn->toDateString(),
            'end_date'       => $endOn->toDateString()
        ]);
    }

    /** @test */
    public function it_successfully_gets_all_events()
    {
        $this->mock->append(new Response(200, ['Content-Type' => 'application/json'], file_get_contents(__DIR__ . '/../stubs/occasion-genius-events-page-1.json')));
        $this->mock->append(new Response(200, ['Content-Type' => 'application/json'], file_get_contents(__DIR__ . '/../stubs/occasion-genius-events-page-2.json')));

        $eventsResponse = $this->apiClient->getAllEventsForLocation($this->marketId);

        $events = collect($eventsResponse->all());

        $this->assertEquals(20, $events->count());


        foreach ($this->container as $index => $guzzleHistory) {
            /** @var \GuzzleHttp\Psr7\Request $request */
            $request = $guzzleHistory['request'];

            $this->assertQueryStringMatches($request, [
                'area_uuid' => $this->marketId->getValue()->toString(),
                'limit'       => ApiClient::DEFAULT_LIMIT,
//                'offset'      => $index * 10 // dummy data only has 10 in it per page, not 100
            ]);
        }
    }

    /** @test */
    public function it_throws_custom_exception_when_request_for_events_fails()
    {
        $this->mock->append(new Response(401, ['Content-Type' => 'application/json'], file_get_contents(__DIR__ . '/../stubs/occasion-genius-no-auth.json')));

        $this->expectException(UnableToGetEventsException::class);

        $this->apiClient->getEventsForLocation($this->marketId);
    }

    private function assertQueryStringMatches(Request $request, array $expectedQuery) : void
    {
        parse_str($request->getUri()->getQuery(), $actualQuery);

        foreach ($expectedQuery as $key => $value) {
            $this->assertArrayHasKey($key, $actualQuery);
            $this->assertEquals($value, $actualQuery[$key], "Query Parameter '$key' does not match");
        }
    }
}
