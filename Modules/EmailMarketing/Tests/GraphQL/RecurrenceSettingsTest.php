<?php

namespace Modules\EmailMarketing\Tests\GraphQL;

use App\Models\ContactBlock;
use App\Models\ContactBlockItem;
use App\Models\EmailAddress;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\WithFaker;
use Infrastructure\Repositories\Titan\TitanApiRepository;
use Mockery;
use Modules\EmailMarketing\Models\Event as LocalEvent;
use Modules\EmailMarketing\Models\EventDate;
use Modules\EmailMarketing\Models\Market;
use Modules\EmailMarketing\Models\RecurrenceSettings;
use Tests\Concerns\InteractsWithAccountDatabase;
use Tests\RefreshDatabase;
use Tests\TestCase;
use Tests\TestsAuthorization;
use Tests\TestsGraphQL;

class RecurrenceSettingsTest extends TestCase
{
    use InteractsWithAccountDatabase, RefreshDatabase, WithFaker, TestsAuthorization, TestsGraphQL;

    /** @var \App\Models\User */
    private $user;

    /** @var \App\Models\EmailAddress */
    private $emailAddress;

    /** @var \Carbon\Carbon */
    private $nextMailingDate;

    /** @var string */
    private $displayName;

    /** @var string */
    private $defaultSubject;

    /** @var \Modules\EmailMarketing\Models\Event */
    private $localEvent;

    protected function setUp(): void
    {
        parent::setUp();

        //Set up the user
        $this->user = $this->setupValidApiUser();

        $this->emailAddress = factory(EmailAddress::class)->create();
    }

    /**
     * creates a location to add to the account
     * and creates an event to associate to that location for a given time period
     */
    private function setUpLocationAndEventsForRecurrence()
    {
        //get the account
        $account = $this->account;

        //create a local event location, and attach it to the account
        $location = factory(Market::class)->create();
        $account->update(['market_uuid' => $location->uuid]);
        $account->save();

        //Calculate what the next mailing date would be
        //get the current date and time in the correct timezone for the location
        $today = Carbon::now($location->timezone);

        //if today is thursday, we need to check if it is before 9AM
        $this->nextMailingDate = null;
        if ($today->isThursday()) {
            $limit = $today->copy()->setTime(9, 0, 0);

            //if today is before the limit, then the limit is the next sending date
            if ($today->isBefore($limit)) {
                $this->nextMailingDate = $limit->copy();
            }
        }

        //mailing date has not yet been set
        if (!$this->nextMailingDate) {
            //set it to next thursday local time
            $this->nextMailingDate =  Carbon::createFromFormat(
                'Y-m-d H:i:s',
                Carbon::parse('next thursday 9AM')->toDateTimeString(), //get the date for Thursday and hard code the time
                $location->timezone //set it to the right timezone
            );
        }

        //reset to the app time zone
        $this->nextMailingDate->setTimezone(config('app.timezone'));

        //Create an event
        $this->localEvent = factory(LocalEvent::class)->create(['flags' => [LocalEvent::EVENT_FLAG_LOCAL, LocalEvent::EVENT_FLAG_DONT_MISS, LocalEvent::EVENT_FLAG_IN_PERSON]]);

        //attach the event to the location
        $this->localEvent->markets()->attach($location);

        //add an event date for the event
        //set the start time to any time between the day after the mailing date, up to 5 days after that
        factory(EventDate::class)->create([
            'event_uuid' => $this->localEvent->uuid,
            'starts_at' => $this->faker->dateTimeBetween(
                $this->nextMailingDate->copy()->addDays(2),
                $this->nextMailingDate->copy()->addDays(5)
            )
                ->format('Y-m-d H:i:s')
        ]);

        //create a contact block for the account
        $this->displayName = $this->faker->name;
        $contactBlock = factory(ContactBlock::class)->create([
            'product' => ContactBlock::PRODUCT_LOCAL_EVENTS
        ]);

        factory(ContactBlockItem::class)->create([
            'contact_block_id' => $contactBlock->id,
            'item_type' => ContactBlockItem::ITEM_TYPE_DISPLAY_NAME,
            'item_id' => null,
            'custom' => $this->displayName
        ]);

        //build the default subject
        $this->defaultSubject = $this->displayName . " presents your local events: " . $this->localEvent->name . " and more";
    }

    /** @test */
    public function it_gets_an_accounts_recurrence_settings()
    {
        //Mock the Titan API repo calls
        $titanRepo = Mockery::mock(TitanApiRepository::class);
        $titanRepo->shouldReceive('getRecipientGroups')->once()->andReturn(['recipientGroups' => [['id' => $this->faker->uuid]]]);
        $this->app->instance(TitanApiRepository::class, $titanRepo);

        //create a recurrence setting
        $recurrenceSettings = factory(RecurrenceSettings::class)->create([
            'email_from_id' => $this->emailAddress->id
        ]);

        //build the GraphQL query
        $query = <<<EOD
query LocalEventsRecurrenceSettings {
   localEventsRecurrenceSettings {
        id
        frequency
        email_from_id
        mailing_date
        mailing_subject
        mailing_body
        recipient_group_ids
        is_enabled
        failed
   }
}
EOD;

        //make the call and get the response
        $response = $this->postGraphQl($query);

        //asset no errors
        $this->assertNoGraphQlErrors($response);

        //assert the response structure
        $response->assertJsonStructure([
            'data' => [
                'localEventsRecurrenceSettings' => [
                    'id',
                    'frequency',
                    'email_from_id',
                    'mailing_date',
                    'mailing_subject',
                    'mailing_body',
                    'recipient_group_ids',
                    'is_enabled',
                    'failed',
                ],
            ],
        ]);

        //assert some values
        $this->assertEquals($recurrenceSettings->id, $response->json('data.localEventsRecurrenceSettings.id'));
        $this->assertEquals($recurrenceSettings->frequency,
            $response->json('data.localEventsRecurrenceSettings.frequency'));
        $this->assertEquals($recurrenceSettings->email_from_id,
            $response->json('data.localEventsRecurrenceSettings.email_from_id'));
        $this->assertEquals($recurrenceSettings->mailing_date,
            $response->json('data.localEventsRecurrenceSettings.mailing_date'));
        $this->assertEquals($recurrenceSettings->mailing_subject,
            $response->json('data.localEventsRecurrenceSettings.mailing_subject'));
        $this->assertEquals($recurrenceSettings->mailing_body,
            $response->json('data.localEventsRecurrenceSettings.mailing_body'));
        $this->assertEquals($recurrenceSettings->is_enabled,
            (bool)$response->json('data.localEventsRecurrenceSettings.is_enabled'));
    }

    /** @test */
    public function it_returns_a_default_recurrence_settings_if_none_exist_for_the_account()
    {
        //set up the events
        $this->setUpLocationAndEventsForRecurrence();

        //Delete any recurrency settings that may exist for the account
        RecurrenceSettings::all()->each(function (RecurrenceSettings $recurrence) {
            $recurrence->delete();
        });

        //Create an email for the account
        $emailAddress = factory(EmailAddress::class)->create(['is_primary' => true]);

        //build the GraphQL query
        $query = <<<EOD
query LocalEventsRecurrenceSettings {
   localEventsRecurrenceSettings {
        id
        frequency
        email_from_id
        mailing_date
        mailing_subject
        mailing_body
        recipient_group_ids
        is_enabled
   }
}
EOD;

        //make the call and get the response
        $response = $this->postGraphQl($query);

        //asset no errors
        $this->assertNoGraphQlErrors($response);

        //assert the response structure
        $response->assertJsonStructure([
            'data' => [
                'localEventsRecurrenceSettings' => [
                    'id',
                    'frequency',
                    'email_from_id',
                    'mailing_date',
                    'mailing_subject',
                    'mailing_body',
                    'recipient_group_ids',
                    'is_enabled'
                ],
            ],
        ]);

        //assert some values
        $this->assertNull($response->json('data.localEventsRecurrenceSettings.id'));
        $this->assertEquals('bi-weekly', $response->json('data.localEventsRecurrenceSettings.frequency'));
        $this->assertEquals($emailAddress->id, $response->json('data.localEventsRecurrenceSettings.email_from_id'));
        $this->assertEquals($this->nextMailingDate, $response->json('data.localEventsRecurrenceSettings.mailing_date'));
        $this->assertEquals($this->defaultSubject, $response->json('data.localEventsRecurrenceSettings.mailing_subject'));
        $this->assertEmpty($response->json('data.localEventsRecurrenceSettings.mailing_body'));
        $this->assertEquals([], $response->json('data.localEventsRecurrenceSettings.recipient_group_ids'));
        $this->assertEquals(false, (bool)$response->json('data.localEventsRecurrenceSettings.is_enabled'));
    }

    /** @test */
    public function it_creates_a_recurrence_settings_entry()
    {
        //Mock the Titan API repo calls
        $titanRepo = Mockery::mock(TitanApiRepository::class);
        $titanRepo->shouldReceive('updateDigitalProductAssociations')->once()->andReturn([]);
        $titanRepo->shouldReceive('getRecipientGroups')->once()->andReturn(['recipientGroups' => [['id' => $this->faker->uuid]]]);
        $this->app->instance(TitanApiRepository::class, $titanRepo);

        //build the GraphQL query
        $query = $this->getSaveLocalEventsRecurrenceSettingsQuery();

        //Assert no record exists in the database
        $this->assertEmpty(RecurrenceSettings::first());

        //build the createParams
        $createParams = [
            'frequency' => 'weekly',
            'email_from_id' => $this->emailAddress->id,
            'mailing_date' => Carbon::parse('next thursday 9AM')->toDateTimeString(),
            'mailing_subject' => $this->faker->sentence,
            'mailing_body' => $this->faker->paragraph,
            'recipient_group_ids' => [$this->faker->uuid],
            'is_enabled' => true,
            'failed' => false,
            'mailing_customizations' => []
        ];

        //make the call and get the response
        $response = $this->postGraphQl($query, $createParams);

        //assert no errors
        $this->assertNoGraphQlErrors($response);

        //assert the response structure
        $response->assertJsonStructure([
            'data' => [
                'saveLocalEventsRecurrenceSettings' => [
                    'id',
                    'frequency',
                    'email_from_id',
                    'mailing_date',
                    'mailing_subject',
                    'mailing_body',
                    'recipient_group_ids',
                    'is_enabled'
                ],
            ],
        ]);

        //Verify the database
        $this->assertAccountDatabaseHas('local_events_recurrence_settings', [
            'id' => $response->json('data.saveLocalEventsRecurrenceSettings.id'),
            'frequency' => $response->json('data.saveLocalEventsRecurrenceSettings.frequency'),
            'email_from_id' => $response->json('data.saveLocalEventsRecurrenceSettings.email_from_id'),
            'mailing_date' => $response->json('data.saveLocalEventsRecurrenceSettings.mailing_date'),
            'mailing_subject' => $response->json('data.saveLocalEventsRecurrenceSettings.mailing_subject'),
            'mailing_body' => $response->json('data.saveLocalEventsRecurrenceSettings.mailing_body'),
            'is_enabled' => $response->json('data.saveLocalEventsRecurrenceSettings.is_enabled')
        ]);
    }

    /** @test */
    public function it_sends_a_bad_request_for_new_recurrence_settings_and_returns_errors()
    {
        //build the GraphQL query
        $query = $this->getSaveLocalEventsRecurrenceSettingsQuery();

        //build the createParams
        $createParams = [
            'frequency' => null,
            'email_from_id' => "notANumber",
            'recipient_group_ids' => null,
            'is_enabled' => "notABoolean"
        ];

        //make the call and get the response
        $response = $this->postGraphQl($query, $createParams);

        //assert there are errors
        $this->assertGraphQlErrors($response);
    }

    /** @test */
    public function it_sends_an_invalid_email_to_save_recurrence_settings_and_returns_errors()
    {
        //create a recurrence setting
        $recurrenceSettings = factory(RecurrenceSettings::class)->create([
            'email_from_id' => $this->emailAddress->id
        ]);

        //create an invalid email address
        $newEmail = factory(EmailAddress::class)->create(["email" => "Not an email address"]);

        //build the GraphQL query
        $query = $this->getSaveLocalEventsRecurrenceSettingsQuery();

        //build the updateParams
        $updateParams = [
            'frequency' => "bi-weekly",
            'email_from_id' => $newEmail->id,
            'mailing_date' => now()->addWeek()->toDateTimeString(),
            'mailing_subject' => $this->faker->sentence . "UPDATED",
            'mailing_body' => $this->faker->paragraph . "UPDATED",
            'recipient_group_ids' => [1,2,3],
            'is_enabled' => false,
            'failed' => false,
        ];

        //make the call and get the response
        $response = $this->postGraphQl($query, $updateParams);

        $response->assertStatus(200);
        $this->assertNotEmpty($response->json('errors'));

        //check the json error
        $errMsgs = $response->json('errors.0.extensions.validation');

        $this->assertEquals("Selected email is not a valid email address", $response->json('errors.0.extensions.validation.email_from_id.0'));
    }

    /** @test */
    public function it_updates_recurrence_settings()
    {
        //Mock the Titan API repo calls
        $titanRepo = Mockery::mock(TitanApiRepository::class);
        $titanRepo->shouldReceive('updateDigitalProductAssociations')->once()->andReturn([]);
        $titanRepo->shouldReceive('getRecipientGroups')->once()->andReturn(['recipientGroups' => [['id' => $this->faker->uuid]]]);
        $this->app->instance(TitanApiRepository::class, $titanRepo);

        //create a recurrence setting
        $recurrenceSettings = factory(RecurrenceSettings::class)->create([
            'email_from_id' => $this->emailAddress->id
        ]);

        //create a new email
        $newEmail = factory(EmailAddress::class)->create();

        //build the GraphQL query
        $query = $this->getSaveLocalEventsRecurrenceSettingsQuery();

        //build the updateParams
        $updateParams = [
            'frequency' => "bi-weekly",
            'email_from_id' => $newEmail->id,
            'mailing_date' => now()->addWeek()->toDateTimeString(),
            'mailing_subject' => $this->faker->sentence . "UPDATED",
            'persist_subject' => true,
            'mailing_heading' => '',
            'persist_heading' => true,
            'mailing_body' => $this->faker->paragraph . "UPDATED",
            'recipient_group_ids' => [$this->faker->uuid],
            'is_enabled' => true,
            'failed' => false,
            'mailing_customizations' => []
        ];

        //make the call and get the response
        $response = $this->postGraphQl($query, $updateParams);

        //assert no errors
        $this->assertNoGraphQlErrors($response);

        //assert the response structure
        $response->assertJsonStructure([
            'data' => [
                'saveLocalEventsRecurrenceSettings' => [
                    'id',
                    'frequency',
                    'email_from_id',
                    'mailing_date',
                    'mailing_subject',
                    'mailing_body',
                    'recipient_group_ids',
                    'is_enabled'
                ],
            ],
        ]);

        //check the database
        $this->assertAccountDatabaseHas('local_events_recurrence_settings', [
            'id'                  => $recurrenceSettings->id,
            'frequency'           => $updateParams['frequency'],
            'email_from_id'       => $updateParams['email_from_id'],
            'mailing_subject'     => $updateParams['mailing_subject'],
            'mailing_body'        => $updateParams['mailing_body'],
            'is_enabled'          => $updateParams['is_enabled'],
        ]);
    }

    /** @test */
    public function it_disables_recurrence_settings()
    {
        //Mock the Titan API repo calls
        $titanRepo = Mockery::mock(TitanApiRepository::class);
        $titanRepo->shouldReceive('updateDigitalProductAssociations')->once()->andReturn([]);
        $titanRepo->shouldReceive('getRecipientGroups')->once()->andReturn(['recipientGroups' => [['id' => $this->faker->uuid]]]);
        $this->app->instance(TitanApiRepository::class, $titanRepo);

        //create a recurrence setting
        $recurrenceSettings = factory(RecurrenceSettings::class)->create([
            'email_from_id' => $this->emailAddress->id,
        ]);

        //create a new email
        $newEmail = factory(EmailAddress::class)->create();

        //build the GraphQL query
        $query = $this->getSaveLocalEventsRecurrenceSettingsQuery();

        //build the updateParams
        $updateParams = [
            'frequency'              => "bi-weekly",
            'email_from_id'          => $newEmail->id,
            'mailing_date'           => now()->addWeek()->toDateTimeString(),
            'mailing_subject'        => $this->faker->sentence . "UPDATED",
            'mailing_body'           => $this->faker->paragraph . "UPDATED",
            'recipient_group_ids'    => [$this->faker->uuid],
            'is_enabled'             => false,
            'failed'                 => false,
            'mailing_customizations' => [],
        ];

        //make the call and get the response
        $response = $this->postGraphQl($query, $updateParams);

        //assert no errors
        $this->assertNoGraphQlErrors($response);

        //assert the response structure
        $response->assertJsonStructure([
            'data' => [
                'saveLocalEventsRecurrenceSettings' => [
                    'id',
                    'frequency',
                    'email_from_id',
                    'mailing_date',
                    'mailing_subject',
                    'mailing_body',
                    'recipient_group_ids',
                    'is_enabled',
                ],
            ],
        ]);

        //check the database
        $this->assertAccountDatabaseHas('local_events_recurrence_settings', [
            'id'         => $recurrenceSettings->id,
            'is_enabled' => $updateParams['is_enabled'],
        ]);
    }

    /** @test */
    public function it_sends_a_bad_request_for_update_recurrence_settings_and_returns_errors()
    {
        //create a recurrence setting
        factory(RecurrenceSettings::class)->create([
            'email_from_id' => $this->emailAddress->id,
        ]);

        //create a new email
        factory(EmailAddress::class)->create();

        //build the GraphQL query
        $query = $this->getSaveLocalEventsRecurrenceSettingsQuery();

        //build the updateParams
        $updateParams = [
            'email_from_id' => "notANumber",
            'recipient_group_ids' => "notAnArray",
            'is_enabled' => "notABoolean"
        ];

        //make the call and get the response
        $response = $this->postGraphQl($query, $updateParams);

        //assert there are errors
        $this->assertGraphQlErrors($response);
    }

    private function getSaveLocalEventsRecurrenceSettingsQuery() : string
    {
        return <<<EOD
mutation SaveLocalEventsRecurrenceSettings(
    \$frequency: String,
    \$email_from_id: ID,
    \$mailing_date : DateTime,
    \$mailing_subject : String,
    \$persist_subject : Boolean,
    \$mailing_heading : String,
    \$persist_heading : Boolean,
    \$mailing_body : String,
    \$recipient_group_ids : [String],
    \$is_enabled : Boolean
    \$failed : Boolean
    \$mailing_customizations : LocalEventNextRecurrenceMailingCustomizationsInput) {
    saveLocalEventsRecurrenceSettings(
        frequency: \$frequency,
        email_from_id: \$email_from_id,
        mailing_date: \$mailing_date,
        mailing_subject: \$mailing_subject,
        persist_subject: \$persist_subject,
        mailing_heading: \$mailing_heading,
        persist_heading: \$persist_heading,
        mailing_body: \$mailing_body,
        recipient_group_ids: \$recipient_group_ids,
        is_enabled: \$is_enabled
        failed: \$failed
        mailing_customizations: \$mailing_customizations
    ) {
        id
        frequency
        email_from_id
        mailing_date
        mailing_subject
        mailing_body
        recipient_group_ids
        is_enabled
        failed
        mailing_customizations {
            background_image
            color_theme
            custom_colors {
                background
                main_heading
                article_heading
                button
            }
        }
    }
}
EOD;
    }
}
