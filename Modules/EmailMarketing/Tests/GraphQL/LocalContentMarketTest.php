<?php
namespace Modules\EmailMarketing\Tests\GraphQL;

use App\Models\Account;
use App\Models\Plan;
use App\Models\Role;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Event;
use Modules\EmailMarketing\Domain\Location\Events\LocalContentMarketAdded;
use Modules\EmailMarketing\Domain\Location\Events\LocalContentMarketDeleted;
use Modules\EmailMarketing\Domain\Location\Events\LocalContentMarketUpdated;
use Modules\EmailMarketing\Models\Event as LocalContentEvent;
use Modules\EmailMarketing\Models\Market;
use Ramsey\Uuid\Uuid;
use Tests\RefreshDatabase;
use Tests\TestCase;
use Tests\TestsAuthorization;
use Tests\TestsGraphQL;

class LocalContentMarketTest extends TestCase
{
    use RefreshDatabase, WithFaker, TestsAuthorization, TestsGraphQL;

    /** @var \App\Models\User */
    private $user;

    protected function setUp(): void
    {
        parent::setUp();

        //set up a user and account, assign the employee role
        // employee role will contain basic permissions for listing markets, as well as add and edit
        $this->user = $this->setupValidApiUser();
        $this->user->assignEmployeeRole();
    }

    /** @test */
    public function it_returns_a_list_of_markets()
    {
        //some create variables
        $pageNum = 2;
        $perPage = 10;
        $marketsToCreate = ($pageNum * $perPage) + 1;

        //create some markets in the database
        factory(Market::class, $marketsToCreate)->create();

        //Since the DB already has markets in it from the create migration, we need to get the total count
        $totalMarkets = Market::count();

        //build the GraphQL query
        $query = <<<EOD
query LocalContentMarketsQuery(\$page: Int!, \$pageSize: Int!) {
       localContentMarkets(page: \$page, count: \$pageSize) {
           data {
               uuid
               name
               address
               latitude
               longitude
               event_radius
               suggested_radius
               updated_at
           }
           paginatorInfo {
               currentPage
               hasMorePages
               lastPage
               total
           }
       }
   }
EOD;
        //build the createParams
        $queryParams = [
            'page' => $pageNum,
            'pageSize' => $perPage
        ];

        //make the call and get the response
        $response = $this->postGraphQl($query, $queryParams);

        //asset no errors
        $this->assertNoGraphQlErrors($response);

        //assert the response structure
        $response->assertJsonStructure([
            'data' => [
                'localContentMarkets' => [
                    'data' => [[
                        'uuid',
                        'name',
                        'address',
                        'latitude',
                        'longitude',
                        'event_radius',
                        'suggested_radius',
                        'updated_at'
                    ]],
                    'paginatorInfo' => [
                        'currentPage',
                        'hasMorePages',
                        'lastPage',
                        'total'
                    ]
                ],
            ],
        ]);

        //assert some values
        $this->assertEquals($perPage, count($response->json('data.localContentMarkets.data')));
        $this->assertEquals($pageNum, $response->json('data.localContentMarkets.paginatorInfo.currentPage'));
        $this->assertTrue($response->json('data.localContentMarkets.paginatorInfo.hasMorePages'));
        $this->assertEquals(ceil($totalMarkets/$perPage), $response->json('data.localContentMarkets.paginatorInfo.lastPage'));
    }

    /** @test */
    public function it_returns_a_market_based_on_the_passed_uuid()
    {
        //create some markets in the database
        $market = factory(Market::class)->create();

        //build the GraphQL query
        $query = <<<EOD
query LocalContentMarketQuery(\$uuid: ID!) {
       localContentMarket(uuid: \$uuid) {
           uuid
           name
           address
           latitude
           longitude
           event_radius
           suggested_radius
       }
   }
EOD;
        //make the call and get the response
        $response = $this->postGraphQl($query, ['uuid' => $market->uuid]);

        //asset no errors
        $this->assertNoGraphQlErrors($response);

        //assert the response structure
        $response->assertJsonStructure([
            'data' => [
                'localContentMarket' => [
                    'uuid',
                    'name',
                    'address',
                    'latitude',
                    'longitude',
                    'event_radius',
                    'suggested_radius',
                ],
            ],
        ]);

        //assert some values
        $this->assertEquals($market->uuid, $response->json('data.localContentMarket.uuid'));
        $this->assertEquals($market->name, $response->json('data.localContentMarket.name'));
        $this->assertEquals($market->address, $response->json('data.localContentMarket.address'));
        $this->assertEquals($market->latitude, $response->json('data.localContentMarket.latitude'));
        $this->assertEquals($market->longitude, $response->json('data.localContentMarket.longitude'));
        $this->assertEquals($market->event_radius, $response->json('data.localContentMarket.event_radius'));
        $this->assertEquals($market->suggested_radius, $response->json('data.localContentMarket.suggested_radius'));
    }

    /** @test */
    public function it_adds_a_new_market()
    {
        $query = <<<EOD
mutation CreateLocalContentMarket(\$uuid: ID, \$name : String!, \$address : String!, \$latitude : Float!, \$longitude : Float!, \$eventRadius : Int!, \$suggestedRadius : Int!) {
    createLocalContentMarket(uuid: \$uuid, name : \$name, address : \$address, latitude : \$latitude, longitude : \$longitude, eventRadius : \$eventRadius, suggestedRadius : \$suggestedRadius) {
        uuid,
        name,
        address,
        latitude,
        longitude,
        event_radius,
        suggested_radius
    }
}
EOD;
        $newMarket = factory(Market::class)->make();
        $response = $this->postGraphQl($query, [
            "uuid" => $newMarket->uuid,
            "name" => $newMarket->name,
            "address" => $newMarket->address,
            "latitude" => $newMarket->latitude,
            "longitude" => $newMarket->longitude,
            "eventRadius" => $newMarket->event_radius,
            "suggestedRadius" => $newMarket->suggested_radius
        ]);

        $this->assertNoGraphQlErrors($response);
        $response->assertJsonStructure([
            "data" => [
                "createLocalContentMarket" => [
                    "uuid",
                    "name",
                    "address",
                    "latitude",
                    "longitude",
                    "event_radius",
                    "suggested_radius"
                ],
            ],
        ]);

        $this->assertDatabaseHas('local_event_markets', [
            'uuid' => $newMarket->uuid
        ]);
    }

    /** @test */
    public function it_dispatches_market_created_event()
    {
        Event::fake([LocalContentMarketAdded::class]);

        $query = <<<EOD
mutation CreateLocalContentMarket(\$uuid: ID, \$name : String!, \$address : String!, \$latitude : Float!, \$longitude : Float!, \$eventRadius : Int!, \$suggestedRadius : Int!) {
    createLocalContentMarket(uuid: \$uuid, name : \$name, address : \$address, latitude : \$latitude, longitude : \$longitude, eventRadius : \$eventRadius, suggestedRadius : \$suggestedRadius) {
        uuid,
        name,
        address,
        latitude,
        longitude,
        event_radius,
        suggested_radius
    }
}
EOD;
        //make the call
        $newMarket = factory(Market::class)->make();
        $resp = $this->postGraphQl($query, [
            "uuid" => $newMarket->uuid,
            "name" => $newMarket->name,
            "address" => $newMarket->address,
            "latitude" => $newMarket->latitude,
            "longitude" => $newMarket->longitude,
            "eventRadius" => $newMarket->event_radius,
            "suggestedRadius" => $newMarket->suggested_radius
        ]);

        //assert the event
        Event::assertDispatched(
            LocalContentMarketAdded::class,
            function (LocalContentMarketAdded $event) use ($newMarket) {
                return $event->getMarketUuid() === $newMarket->uuid &&
                    $event->getName() === $newMarket->name &&
                    $event->getAddress() === $newMarket->address &&
                    $event->getLatitude() === $newMarket->latitude &&
                    $event->getLongitude() === $newMarket->longitude &&
                    $event->getSuggestedRadius() === $newMarket->suggested_radius;
            }
        );
    }

    /** @test */
    public function it_updates_a_market()
    {
        $query = <<<EOD
mutation UpdateLocalContentMarket(\$uuid: ID!, \$name : String!, \$address : String!, \$latitude : Float!, \$longitude : Float!, \$eventRadius : Int!, \$suggestedRadius : Int!) {
    updateLocalContentMarket(uuid: \$uuid, name : \$name, address : \$address, latitude : \$latitude, longitude : \$longitude, eventRadius : \$eventRadius, suggestedRadius : \$suggestedRadius) {
        uuid,
        name,
        address,
        latitude,
        longitude,
        event_radius,
        suggested_radius
    }
}
EOD;
        //create an existing market
        $existingMarket = factory(Market::class)->create();
        //get variables for a new market
        $newMarket = factory(Market::class)->make();

        //make the call
        $response = $this->postGraphQl($query, [
            "uuid" => $existingMarket->uuid,
            "name" => $newMarket->name,
            "address" => $newMarket->address,
            "latitude" => $newMarket->latitude,
            "longitude" => $newMarket->longitude,
            "eventRadius" => $newMarket->event_radius,
            "suggestedRadius" => $newMarket->suggested_radius
        ]);

        //check the errors
        $this->assertNoGraphQlErrors($response);

        //check the structure
        $response->assertJsonStructure([
            "data" => [
                "updateLocalContentMarket" => [
                    "uuid",
                    "name",
                    "address",
                    "latitude",
                    "longitude",
                    "event_radius",
                    "suggested_radius"
                ],
            ],
        ]);

        //check the database
        $this->assertDatabaseHas('local_event_markets', [
            "uuid" => $existingMarket->uuid,
            "name" => $newMarket->name,
            "address" => $newMarket->address,
            "latitude" => $newMarket->latitude,
            "longitude" => $newMarket->longitude,
            "event_radius" => $newMarket->event_radius,
            "suggested_radius" => $newMarket->suggested_radius
        ]);
    }

    /** @test */
    public function it_dispatches_market_updated_event()
    {
        Event::fake([LocalContentMarketUpdated::class]);

        $query = <<<EOD
mutation UpdateLocalContentMarket(\$uuid: ID!, \$name : String!, \$address : String!, \$latitude : Float!, \$longitude : Float!, \$eventRadius : Int!, \$suggestedRadius : Int!) {
    updateLocalContentMarket(uuid: \$uuid, name : \$name, address : \$address, latitude : \$latitude, longitude : \$longitude, eventRadius : \$eventRadius, suggestedRadius : \$suggestedRadius) {
        uuid,
        name,
        address,
        latitude,
        longitude,
        event_radius,
        suggested_radius
    }
}
EOD;
        //create an existing market
        $existingMarket = factory(Market::class)->create();
        //get variables for a new market
        $newMarket = factory(Market::class)->make();

        //make the call
        $this->postGraphQl($query, [
            "uuid" => $existingMarket->uuid,
            "name" => $newMarket->name,
            "address" => $newMarket->address,
            "latitude" => $newMarket->latitude,
            "longitude" => $newMarket->longitude,
            "eventRadius" => $newMarket->event_radius,
            "suggestedRadius" => $newMarket->suggested_radius
        ]);

        //assert the event
        Event::assertDispatched(
            LocalContentMarketUpdated::class,
            function (LocalContentMarketUpdated $event) use ($existingMarket, $newMarket) {
                return $event->getOldUuid() === $existingMarket->uuid &&
                    $event->getOldName() === $existingMarket->name &&
                    $event->getOldAddress() === $existingMarket->address &&
                    $event->getOldLatitude() === $existingMarket->latitude &&
                    $event->getOldLongitude() === $existingMarket->longitude &&
                    $event->getOldEventRadius() === $existingMarket->event_radius &&
                    $event->getOldSuggestedRadius() === $existingMarket->suggested_radius &&
                    $event->getNewUuid() === $existingMarket->uuid && //uuid doesn't change
                    $event->getNewName() === $newMarket->name &&
                    $event->getNewAddress() === $newMarket->address &&
                    $event->getNewLatitude() === $newMarket->latitude &&
                    $event->getNewLongitude() === $newMarket->longitude &&
                    $event->getNewEventRadius() === $newMarket->event_radius &&
                    $event->getNewSuggestedRadius() === $newMarket->suggested_radius;
            }
        );
    }

    /** @test */
    public function it_deletes_a_market()
    {
        $query = <<<EOD
mutation DeleteLocalContentMarket(\$uuid: ID!) {
  deleteLocalContentMarket(uuid: \$uuid) {
    result
  }
}
EOD;
        //add the Content Manager role to the user
        $this->user->assignRole(Role::ROLE_CONTENT_MANAGER);

        //create a model
        $existingMarket = factory(Market::class)->create();

        //make the call
        $response = $this->postGraphQl($query, ['uuid' => $existingMarket->uuid]);

        //verify no errors
        $this->assertNoGraphQlErrors($response);

        //verify the response structure
        $response->assertJsonStructure([
            'data' => [
                'deleteLocalContentMarket' => [
                    'result'
                ]
            ],
        ]);

        //assert the returned id is the same as the object if
        $this->assertTrue($response->json('data.deleteLocalContentMarket.result'));

        //assert the record does not exist in the database
        $this->assertEmpty(Market::find($existingMarket->id));
    }

    /** @test */
    public function it_dispatches_market_deleted_event()
    {
        //add the Content Manager role to the user
        $this->user->assignRole(Role::ROLE_CONTENT_MANAGER);

        //fake the event
        Event::fake([LocalContentMarketDeleted::class]);

        $query = <<<EOD
mutation DeleteLocalContentMarket(\$uuid: ID!) {
  deleteLocalContentMarket(uuid: \$uuid) {
    result
  }
}
EOD;

        //create a model
        $existingMarket = factory(Market::class)->create();

        //make the call
        $this->postGraphQl($query, ['uuid' => $existingMarket->uuid]);

        //verify the dispatched event
        Event::assertDispatched(
            LocalContentMarketDeleted::class,
            function (LocalContentMarketDeleted $event) use ($existingMarket) {
                return $event->getMarketUuid() === $existingMarket->uuid &&
                    $event->getName() === $existingMarket->name &&
                    $event->getAddress() === $existingMarket->address &&
                    $event->getLatitude() === $existingMarket->latitude &&
                    $event->getLongitude() === $existingMarket->longitude &&
                    $event->getSuggestedRadius() === $existingMarket->suggested_radius;
            }
        );
    }

    /** @test */
    public function it_detaches_events_when_it_deletes_a_market()
    {
        $query = <<<EOD
mutation DeleteLocalContentMarket(\$uuid: ID!) {
  deleteLocalContentMarket(uuid: \$uuid) {
    result
  }
}
EOD;
        //add the Content Manager role to the user
        $this->user->assignRole(Role::ROLE_CONTENT_MANAGER);

        //create a model
        $existingMarket = factory(Market::class)->create();

        //create a second Market
        $otherMarket = factory(Market::class)->create();

        //Create an event, and attach both markets
        $event = factory(LocalContentEvent::class)->create();
        $event->markets()->syncWithoutDetaching([$existingMarket->uuid, $otherMarket->uuid]);

        //Create another event, and attach only the first market (the one to delete)
        $otherEvent = factory(LocalContentEvent::class)->create();
        $otherEvent->markets()->syncWithoutDetaching($existingMarket->uuid);

        //make the call
        $response = $this->postGraphQl($query, ['uuid' => $existingMarket->uuid]);

        //verify no errors
        $this->assertNoGraphQlErrors($response);

        //verify the response structure
        $response->assertJsonStructure([
            'data' => [
                'deleteLocalContentMarket' => [
                    'result'
                ]
            ],
        ]);

        //assert the returned id is the same as the object if
        $this->assertTrue($response->json('data.deleteLocalContentMarket.result'));

        //assert the record does not exist in the database
        $this->assertEmpty(Market::find($existingMarket->id));

        //Assert the relationships
        $event->load('markets');
        $this->assertEquals(1, $event->markets->count());
        $otherEvent->load('markets');
        $this->assertEquals(0, $otherEvent->markets->count());

        //Assert the database records
        $this->assertDatabaseHas('local_event_market_events', [
            'market_uuid' => $otherMarket->uuid,
            'event_uuid' => $event->uuid
        ]);

        $this->assertDatabaseMissing('local_event_market_events', [
            'market_uuid' => $existingMarket->uuid,
            'event_uuid' => $event->uuid
        ]);

        $this->assertDatabaseMissing('local_event_market_events', [
            'market_uuid' => $existingMarket->uuid,
            'event_uuid' => $otherEvent->uuid
        ]);
    }

    /** @test */
    public function it_returns_an_error_if_trying_to_delete_a_non_existent_market()
    {
        //add the Content Manager role to the user
        $this->user->assignRole(Role::ROLE_CONTENT_MANAGER);

        //get a new uuid
        $nonExistentMarketId = Uuid::uuid4()->toString();

        $query = <<<EOD
mutation DeleteLocalContentMarket(\$uuid: ID!) {
  deleteLocalContentMarket(uuid: \$uuid) {
    result
  }
}
EOD;

        //make the call
        $response = $this->postGraphQl($query, ['uuid' => $nonExistentMarketId]);

        //assert there are errors
        $this->assertNotEmpty($response->json('errors'));
    }

    /** @test */
    public function it_does_not_dispatch_a_market_deleted_event_if_trying_to_delete_a_non_existent_market()
    {
        //add the Content Manager role to the user
        $this->user->assignRole(Role::ROLE_CONTENT_MANAGER);

        //fake the event
        Event::fake([LocalContentMarketDeleted::class]);

        //get a new uuid
        $nonExistentMarketId = Uuid::uuid4()->toString();

        $query = <<<EOD
mutation DeleteLocalContentMarket(\$uuid: ID!) {
  deleteLocalContentMarket(uuid: \$uuid) {
    result
  }
}
EOD;

        //make the call
        $this->postGraphQl($query, ['uuid' => $nonExistentMarketId]);

        //assert the event was not dispatched
        Event::assertNotDispatched(LocalContentMarketDeleted::class);
    }

    /** @test */
    public function it_returns_an_error_if_trying_to_delete_a_market_without_the_correct_permissions()
    {
        //create a model
        $existingMarket = factory(Market::class)->create();

        $query = <<<EOD
mutation DeleteLocalContentMarket(\$uuid: ID!) {
  deleteLocalContentMarket(uuid: \$uuid) {
    result
  }
}
EOD;

        //make the call
        $response = $this->postGraphQl($query, ['uuid' => $existingMarket->uuid]);

        //assert there are errors
        $this->assertNotEmpty($response->json('errors'));
    }

    /** @test */
    public function it_does_not_dispatch_a_market_deleted_event_if_trying_to_delete_a_market_without_the_correct_permissions()
    {
        //fake the event
        Event::fake([LocalContentMarketDeleted::class]);

        //create a model
        $existingMarket = factory(Market::class)->create();

        $query = <<<EOD
mutation DeleteLocalContentMarket(\$uuid: ID!) {
  deleteLocalContentMarket(uuid: \$uuid) {
    result
  }
}
EOD;

        //make the call
        $this->postGraphQl($query, ['uuid' => $existingMarket->uuid]);

        //assert the event was not dispatched
        Event::assertNotDispatched(LocalContentMarketDeleted::class);
    }

    /** @test */
    public function it_returns_an_error_if_trying_to_delete_a_market_with_enrolled_accounts()
    {
        //add the Content Manager role to the user
        $this->user->assignRole(Role::ROLE_CONTENT_MANAGER);

        //get a new uuid
        $existingMarket = factory(Market::class)->create();

        //Create an account, and add the market uuid
        $enrolledAccount = factory(Account::class)->create(['market_uuid' => $existingMarket->uuid]);

        //Enroll the account in LC
        $enrolledAccount->assignPlan(Plan::find(Plan::PLAN_ID_LOCAL_EVENT));

        $query = <<<EOD
mutation DeleteLocalContentMarket(\$uuid: ID!) {
  deleteLocalContentMarket(uuid: \$uuid) {
    result
  }
}
EOD;

        //make the call
        $response = $this->postGraphQl($query, ['uuid' => $existingMarket]);

        //assert there are errors
        $this->assertNotEmpty($response->json('errors'));
    }

    /** @test */
    public function it_does_not_dispatch_a_market_deleted_event_if_trying_to_delete_a_market_with_enrolled_accounts()
    {
        //add the Content Manager role to the user
        $this->user->assignRole(Role::ROLE_CONTENT_MANAGER);

        //fake the event
        Event::fake([LocalContentMarketDeleted::class]);

        //get a new uuid
        $existingMarket = factory(Market::class)->create();

        //Create an account, and add the market uuid
        $enrolledAccount = factory(Account::class)->create(['market_uuid' => $existingMarket->uuid]);

        //Enroll the account in LC
        $enrolledAccount->assignPlan(Plan::find(Plan::PLAN_ID_LOCAL_EVENT));

        $query = <<<EOD
mutation DeleteLocalContentMarket(\$uuid: ID!) {
  deleteLocalContentMarket(uuid: \$uuid) {
    result
  }
}
EOD;

        //make the call
        $this->postGraphQl($query, ['uuid' => $existingMarket]);

        //assert the event was not dispatched
        Event::assertNotDispatched(LocalContentMarketDeleted::class);
    }
}
