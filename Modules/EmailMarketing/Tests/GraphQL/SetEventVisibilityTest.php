<?php

namespace Modules\EmailMarketing\Tests\GraphQL;

use App\Models\Role;
use Illuminate\Foundation\Testing\WithFaker;
use Modules\EmailMarketing\Models\Event;
use Nuwave\Lighthouse\Testing\MakesGraphQLRequests;
use Tests\RefreshDatabase;
use Tests\TestCase;
use Tests\TestsAuthorization;

class SetEventVisibilityTest extends TestCase
{
    use RefreshDatabase, WithFaker, TestsAuthorization, MakesGraphQLRequests;

    /** @var \App\Models\User */
    private $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = $this->setupValidApiUser();
    }

    /** @test */
    public function it_sets_event_as_hidden()
    {
        //add the Content Manager role to the user
        $this->user->assignRole(Role::ROLE_CONTENT_MANAGER);

        /** @var \Modules\EmailMarketing\Models\Event $event */
        $event = factory(Event::class)->create(['is_hidden' => false]);

        $response = $this->postGraphQL([
            'query'     => $this->getSetEventVisibilityMutation(),
            'variables' => [
                'uuid'      => $event->uuid,
                'is_hidden' => true,
            ],
        ]);

        $this->assertTrue($response->json('data.setLocalContentEventVisibility.is_hidden'));

        $this->assertDatabaseHas('local_event_events', [
            'uuid'      => $event->uuid,
            'is_hidden' => true,
        ]);
    }

    /** @test */
    public function it_returns_an_error_when_trying_to_set_event_as_hidden_without_the_correct_permissions()
    {
        /** @var \Modules\EmailMarketing\Models\Event $event */
        $event = factory(Event::class)->create(['is_hidden' => false]);

        $response = $this->postGraphQL([
            'query'     => $this->getSetEventVisibilityMutation(),
            'variables' => [
                'uuid'      => $event->uuid,
                'is_hidden' => true,
            ],
        ]);

        //assert there are errors
        $this->assertNotEmpty($response->json('errors'));
    }

    /** @test */
    public function it_sets_event_as_visible()
    {
        //add the Content Manager role to the user
        $this->user->assignRole(Role::ROLE_CONTENT_MANAGER);

        /** @var \Modules\EmailMarketing\Models\Event $event */
        $event = factory(Event::class)->create(['is_hidden' => true]);

        $response = $this->postGraphQL([
            'query'     => $this->getSetEventVisibilityMutation(),
            'variables' => [
                'uuid'      => $event->uuid,
                'is_hidden' => false,
            ],
        ]);

        $this->assertFalse($response->json('data.setLocalContentEventVisibility.is_hidden'));

        $this->assertDatabaseHas('local_event_events', [
            'uuid'      => $event->uuid,
            'is_hidden' => false,
        ]);
    }

    /** @test */
    public function it_returns_an_error_when_trying_to_set_event_as_visible_without_the_correct_permissions()
    {
        /** @var \Modules\EmailMarketing\Models\Event $event */
        $event = factory(Event::class)->create(['is_hidden' => true]);

        $response = $this->postGraphQL([
            'query' => $this->getSetEventVisibilityMutation(),
            'variables' => [
                'uuid' => $event->uuid,
                'is_hidden' => false,
            ],
        ]);

        //assert there are errors
        $this->assertNotEmpty($response->json('errors'));
    }


    private function getSetEventVisibilityMutation() : string
    {
        return <<<EOD
mutation SetLocalContentEventVisibility(\$uuid: ID!, \$is_hidden: Boolean) {
    setLocalContentEventVisibility(uuid: \$uuid, is_hidden: \$is_hidden) {
        uuid
        is_hidden
    }
}
EOD;
    }
}
