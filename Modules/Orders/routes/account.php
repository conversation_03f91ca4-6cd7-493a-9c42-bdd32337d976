<?php

use Illuminate\Support\Facades\Route;
use Modules\Orders\Http\Controllers\OrderController;
use Modules\Orders\Http\Controllers\ShoppingCartController;
use Modules\Orders\Http\Middleware\HasShoppingCart;
use Modules\Orders\Http\Middleware\ConfirmOrderPageCheck;

// Shopping Cart
Route::post('cart', route_action(ShoppingCartController::class, 'store'))->name('cart.store');
Route::middleware(HasShoppingCart::class)->group(function () {
    Route::get('order-summary', route_action(ShoppingCartController::class, 'show'))->name('cart.show');
    Route::delete('cart', route_action(ShoppingCartController::class, 'destroy'))->name('cart.destroy');
});

// Orders
Route::get('confirm-order', route_action(OrderController::class, 'show'))
    ->middleware(ConfirmOrderPageCheck::class)
    ->name('orders.show');

Route::put('orders', route_action(OrderController::class, 'update'))->name('orders.update');
Route::post('orders', route_action(OrderController::class, 'store'))
    ->middleware(HasShoppingCart::class)
    ->name('orders.store');
