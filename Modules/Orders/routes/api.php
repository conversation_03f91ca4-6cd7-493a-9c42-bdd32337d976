<?php

use Illuminate\Support\Facades\Route;
use Modules\Orders\Http\Controllers\FetchMasterServiceTermsController;
use Modules\Orders\Http\Controllers\FetchServiceTermsController;
use Modules\Orders\Http\Controllers\FetchServiceTermsExistenceController;

Route::group(['middleware' => ['auth:api']], function () {

    // Service Terms
    Route::post('service-terms', route_action(FetchServiceTermsController::class))->name('plan.service-terms');

    Route::get('service-terms-exist', route_action(FetchServiceTermsExistenceController::class))->name('orders.terms-exist');

    Route::get('master-service-terms', route_action(FetchMasterServiceTermsController::class))->name('master-service-terms');

});
