<?php

namespace Modules\Orders\Traits;

use Domain\Account\Models\AccountPlan;
use Modules\Orders\Contracts\ShoppingCartRepository;
use Modules\Orders\DTO\CreateShoppingCartDTO;
use App\Models\Plan;

trait ShoppingCart
{
    public function createShoppingCart(CreateShoppingCartDTO $dto)
    {
        /** @var ShoppingCartRepository $orderRepository */
        $orderRepository = app(ShoppingCartRepository::class);
        $shoppingCart = $orderRepository->findOrCreateShoppingCart();

        $shoppingCart = $this->shouldAddBundle($dto) ? $shoppingCart->addBundle() : $shoppingCart->addPlan($dto->planId);

        $shoppingCart->persist();
    }

    protected function shouldAddBundle(CreateShoppingCartDTO $dto): bool
    {
        if (! $dto->isBundle && $dto->planId == Plan::PLAN_ID_BRANDED_MAGAZINE) {
            return false;
        }
       else if ($dto->isBundle) {
            return true;
            // Check if the campaign has digital products and the account is already enrolled in a digital product
        } else if ($dto->isOnlineCampaign && $dto->campaignHasDigitalProducts && AccountPlan::digitalProducts()->exists()) {
            return true;
            //forces to add a bundle when the account has at least one digital product
        } else if (AccountPlan::digitalProducts()->exists()) {
            return true;
        }
        return false;
    }
}
