<?php

namespace Modules\Orders\Contracts;

use Modules\Orders\Models\Order;
use Modules\Orders\Models\ServiceTermsAgreement;

interface OrderRepository
{
    public function create(array $details): Order;
    public function find(int $id): ?Order;
    public function findOrCreate(array $details): Order;
    public function save(Order $order): bool;
    public function addPlans(Order $order, array $planIds);
    public function saveServiceTermsAgreement(ServiceTermsAgreement $serviceTermsAgreement): bool;
    public function getLatestOrder(?int $planId, $status = []): ?Order;
    public function getLatestOrderByDate(string $date): ?Order;
}
