<?php
/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\Models\Plan;
use Faker\Generator as Faker;
use Modules\Orders\Models\Order;
use Modules\Orders\Models\ServiceTerms;
use Modules\Orders\Models\ServiceTermsAgreement;

$factory->define(Order::class, function () {
    return [
        'bundle_id' => Plan::BUNDLE_ID_NONE,
        'status' => Order::STATUS_CHARGED,
        'pricing' => [],
    ];
});

$factory->state(Order::class, 'completed', function () {
    return ['status' => Order::STATUS_CLOSED];
});


$factory->define(ServiceTerms::class, function (Faker $faker) {
    return [
        'name' => $faker->name(),
        'service_terms_text' => $faker->randomHtml(),
        'version' => '1.0.0'
    ];
});

$factory->state(ServiceTerms::class, 'ALM', function () {
    return ['plan_id' => Plan::PLAN_ID_AMERICAN_LIFESTYLE_MAGAZINE];
});

$factory->state(ServiceTerms::class, 'BP', function () {
    return ['plan_id' => Plan::PLAN_ID_BRANDED_POSTS];
});

$factory->state(ServiceTerms::class, 'DE', function () {
    return ['plan_id' => Plan::PLAN_ID_DIGITAL_EDITIONS];
});

$factory->state(ServiceTerms::class, 'LP', function () {
    return ['plan_id' => Plan::PLAN_ID_LANDING_PAGES];
});

$factory->state(ServiceTerms::class, 'LE', function () {
    return ['plan_id' => Plan::PLAN_ID_LOCAL_EVENT];
});

/**
 *
 */
$factory->define(ServiceTermsAgreement::class, function (Faker $faker) {
    return [
        'service_terms_id' => function () {
            return factory(ServiceTerms::class)->create()->id;
        },
        'order_id'         => function () {
            return factory(Order::class)->create()->id;
        },
        'ip'               => $faker->ipv4,
        'signed_at'        => now(),
    ];
});

$factory->state(ServiceTermsAgreement::class, 'unsigned', function () {
    return [
        'signed_at' => null,
    ];
});
