<?php

use Carbon\Carbon;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class UpdateServiceTermsBrandedPosts extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //disable foreign keys as the plans might not exist yet
        Schema::disableForeignKeyConstraints();

        DB::table('service_terms')->get()->each(function ($terms) {
            DB::table('service_terms')->insert([
                'name' => preg_replace('/Social Media Shares Premium/', 'Branded Posts', $terms->name),
                'plan_id' => $terms->plan_id,
                'service_terms_text' => preg_replace(
                    '/Social Media Shares and Social Media Shares Premium/',
                    'Branded Posts',
                    $terms->service_terms_text
                ),
                'version' => '1.0.1',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
        });

        //enable foreign keys
        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('service_terms')->where('service_terms_text', 'like', '%Branded Posts%')->delete();
    }
}
