<?php

use Carbon\Carbon;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class UpdateServiceTermsDigitalEdition extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //disable foreign keys as the plans might not exist yet
        Schema::disableForeignKeyConstraints();

        DB::table('service_terms')->where('version', '1.0.2')
            ->get()
            ->each(function ($terms) {
                DB::table('service_terms')->insert([
                    'name' => $terms->name,
                    'plan_id' => $terms->plan_id,
                    'service_terms_text' => preg_replace(
                        '/\(iii\) Branded Posts; \(v\)/',
                        '(iii) Branded Posts; (iv) Landing Pages; (v)',
                        $terms->service_terms_text
                    ),
                    'version' => '1.0.3',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            });

        //enable foreign keys
        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('service_terms')
            ->where('service_terms_text', 'like', '%Landing Pages%')
            ->delete();
    }
}
