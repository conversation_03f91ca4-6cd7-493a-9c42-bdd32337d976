<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateShoppingCartPlansTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shopping_cart_plans', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('shopping_cart_id');
            $table->unsignedInteger('plan_id');
            $table->timestamps();

            $table->foreign('shopping_cart_id')->references('id')->on('shopping_carts');
            $table->foreign('plan_id')->references('id')->on('plans');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shopping_cart_plans');
    }
}
