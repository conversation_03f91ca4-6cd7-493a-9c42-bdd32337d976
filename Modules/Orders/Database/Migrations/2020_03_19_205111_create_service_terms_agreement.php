<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateServiceTermsAgreement extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('service_terms_agreements', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('account_id');
            $table->unsignedInteger('service_terms_id');
            $table->unsignedInteger('order_id');
            $table->string('ip');
            $table->timestamp('signed_at');
            $table->timestamps();

            $table->foreign('service_terms_id')->references('id')->on('service_terms');
            $table->foreign('order_id')->references('id')->on('orders');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('service_terms_agreements');
    }
}
