<?php

namespace Modules\Orders\Tests\Unit\Http\Middleware;

use App\Http\Account\LocalEvents\Middleware\PromoPageCheck;
use App\Models\Plan;
use Domain\Account\Models\AccountPlan;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\Orders\Models\ShoppingCart;
use Tests\RefreshDatabase;
use Tests\TestCase;

class PromoPageCheckTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    /** @var \Modules\Orders\Http\Middleware\PromoPageCheck  */
    protected $middleware;

    protected function setUp(): void
    {
        parent::setUp();

        $this->middleware = new PromoPageCheck;
    }

    public function test_it_stores_route_names_when_it_receives_request()
    {
        // given an empty session
        $this->assertFalse(session()->has(['last_promo_page', 'product_dashboard']));

        // when it receives a request
        Route::shouldReceive('currentRouteName')->andReturn('local-events.promo');
        $this->middleware->handle(new Request, function () {
        });

        // the route names have been stored in the session
        $this->assertEquals('local-events.promo', session('last_promo_page'));
        $this->assertEquals('local-events.index', session('product_dashboard'));
    }

    public function test_it_redirects_to_product_product_page_when_account_is_enrolled()
    {
        // given the account has the plan
        AccountPlan::create(['plan_id' => Plan::PLAN_ID_LOCAL_EVENT]);

        // when it receives a request
        $response = $this->middleware->handle(new Request, function () {
        });

        // it redirects to product page
        $this->assertTrue($response->isRedirect(rmc_route('local-events.index')));
    }

    public function test_it_redirects_to_order_summary_when_product_has_been_added_to_the_cart()
    {
        // given the user has added the plan to the shopping cart
        $cart = ShoppingCart::create();
        $cart->plans()->attach(Plan::PLAN_ID_LOCAL_EVENT);

        // when it receives a request
        $response = $this->middleware->handle(new Request, function () {
        });

        // it redirects to product page
        $this->assertTrue($response->isRedirect(rmc_route('cart.show')));
    }

    public function test_it_doesnt_redirect_when_account_is_not_enrolled()
    {
        // given the account is not enrolled in the plan
        $this->assertEmpty(AccountPlan::all());

        // when it receives a request
        $redirect = $this->middleware->handle(new Request, function () {
        });

        // no redirect happened
        $this->assertNull($redirect);
    }

    public function test_it_doesnt_redirect_when_account_has_no_shopping_cart()
    {
        // given the account doesn't have a shopping cart at all
        $this->assertNull(ShoppingCart::first());

        // when it receives a request
        $redirect = $this->middleware->handle(new Request, function () {
        });

        // no redirect happened
        $this->assertNull($redirect);
    }

    public function test_it_doesnt_redirect_when_plan_is_not_in_shopping_cart()
    {
        // given the account has a shopping cart with products
        $cart = ShoppingCart::create();
        $cart->plans()->attach(Plan::PLAN_ID_SOCIAL_MEDIA_SHARES);

        // but it doesn't have the product the user is trying to see
        $this->assertFalse($cart->plans->has(Plan::PLAN_ID_LOCAL_EVENT));

        // when it receives a request
        $redirect = $this->middleware->handle(new Request, function () {
        });

        // no redirect happened
        $this->assertNull($redirect);
    }
}
