<?php

namespace Modules\Orders\Tests\Unit\Http\Middleware;

use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Request;
use Modules\Orders\Http\Middleware\ConfirmOrderPageCheck;
use Modules\Orders\Models\Order;
use Tests\RefreshDatabase;
use Tests\TestCase;

class ConfirmOrderPageCheckTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    /** @var \Modules\Orders\Http\Middleware\ConfirmOrderPageCheck  */
    protected $middleware;

    protected function setUp(): void
    {
        parent::setUp();

        $this->middleware = resolve(ConfirmOrderPageCheck::class);
    }

    public function test_it_redirects_when_account_has_no_orders_at_all()
    {
        // given the account has no orders
        $this->assertTrue(Order::all()->isEmpty());

        // when it receives a request
        $response = $this->middleware->handle(new Request, function () {});

        // it redirects to the dashboard
        $this->assertTrue($response->isRedirect(rmc_route('dashboard.index')));
    }

    public function test_it_redirects_when_account_doesnt_have_an_order_on_the_charged_status()
    {
        // given the account has orders, but none of them are 'charged'
        factory(Order::class, 3)->create(['status' => Order::STATUS_CLOSED]);

        // when it receives a request
        $response = $this->middleware->handle(new Request, function () {});

        // it redirects to the dashboard
        $this->assertTrue($response->isRedirect(rmc_route('dashboard.index')));
    }

    public function test_it_doesnt_redirect_when_account_has_a_charged_order_without_service_terms_agreements()
    {
        // given the account's latest 'charged' order doesn't have service terms agreement
        /** @var Order $order */
        $order = factory(Order::class)->create();
        $this->assertEmpty($order->serviceTermsAgreements);

        // when it receives a request
        $redirect = $this->middleware->handle(new Request, function () {});

        // no redirect happened
        $this->assertNull($redirect);
    }
}
