<?php

namespace Modules\Orders\Tests\Unit\Http\Middleware;

use App\Contracts\AccountRepository;
use App\Models\Plan;
use Illuminate\Http\Request;
use Modules\Orders\Http\Middleware\HasShoppingCart;
use Modules\Orders\Models\ShoppingCart;
use Tests\RefreshDatabase;
use Tests\TestCase;

class HasShoppingCartTest extends TestCase
{
    use RefreshDatabase;

    /** @var \Modules\Orders\Http\Middleware\HasShoppingCart  */
    protected $middleware;

    protected function setUp(): void
    {
        parent::setUp();

        $this->middleware = new HasShoppingCart(app(AccountRepository::class));
    }

    public function test_it_redirects_when_account_doesnt_have_a_shopping_cart_relationship()
    {
        // given the account has no shopping cart
        $this->assertNull(ShoppingCart::first());

        // when it receives a request
        $response = $this->middleware->handle(new Request, function () {
        });

        // it responds with redirect to the dashboard
        $this->assertTrue($response->isRedirect(route('dashboard.index')));
    }

    /** @test */
    public function it_redirects_when_shopping_cart_is_empty()
    {
        // given the account has a shopping cart
        $cart = ShoppingCart::create();

        // but the shopping cart is empty
        $this->assertEmpty($cart->plans);

        // when it receives a request
        $response = $this->middleware->handle(new Request, function () {
        });

        // it responds with redirect to the dashboard
        $this->assertTrue($response->isRedirect(route('dashboard.index')));
    }

    /** @test */
    public function it_doesnt_redirect_when_account_has_shopping_cart()
    {
        // given the account has a shopping cart
        $cart = ShoppingCart::create();

        // and the shopping cart is not empty
        $cart->plans()->attach(Plan::PLAN_ID_DIGITAL_EDITIONS);

        // when it receives a request
        $redirect = $this->middleware->handle(new Request, function () {
        });

        // it doesn't respond with a redirect
        $this->assertNull($redirect);
    }
}
