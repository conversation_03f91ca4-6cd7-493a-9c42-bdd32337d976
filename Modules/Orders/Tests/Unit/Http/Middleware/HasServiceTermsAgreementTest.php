<?php

namespace Modules\Orders\Tests\Unit\Http\Middleware;

use App\Models\Plan;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\Orders\Http\Middleware\HasServiceTermsAgreement;
use Modules\Orders\Models\Order;
use Modules\Orders\Models\ServiceTermsAgreement;
use Tests\RefreshDatabase;
use Tests\TestCase;

/**
 * @property \Illuminate\Database\Eloquent\Model|mixed $user
 */
class HasServiceTermsAgreementTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    /** @var \Modules\Orders\Http\Middleware\HasServiceTermsAgreement */
    protected $middleware;

    /** @var String */
    protected $alias;

    protected function setUp(): void
    {
        parent::setUp();
        $this->alias = 'bp';

        $this->user = $this->setupUnauthorizedUser();
        $this->user->giveOwnershipOfAccount($this->account);
        $this->actingAs($this->user);

        // This needs to be solved last if using the containing, otherwise the context/current user is null
        $this->middleware = resolve(HasServiceTermsAgreement::class);
    }

    public function test_it_doesnt_redirect_when_account_doesnt_have_the_plan()
    {
        // given the account doesn't have the plan
        $this->assertEmpty($this->account->plans);

        // when it receives a request
        $redirect = $this->middleware->handle(new Request, function () {
        }, $this->alias);

        // it doesn't respond with a redirect
        $this->assertNull($redirect);
    }

    public function test_it_doesnt_redirect_when_account_has_agreed_to_the_service_terms()
    {
        // given the account has the plan
        $plan = Plan::find(Plan::PLAN_ID_SOCIAL_MEDIA_SHARES);
        $this->account->plans()->attach($plan->id);

        // and the user has agreed to the service terms
        factory(Order::class)->create(['status' => Order::STATUS_CLOSED])->each(function (Order $order) use ($plan) {
            $order->serviceTermsAgreements()->save(
                new ServiceTermsAgreement([
                    'service_terms_id' => $plan->currentServiceTerms()->id,
                    'ip' => $this->faker->ipv4,
                    'account_id' => $this->account->id
                ])
            );
        });

        // when it receives a request
        $redirect = $this->middleware->handle(new Request, function () {
        }, $this->alias);

        // it doesn't respond with a redirect
        $this->assertNull($redirect);
    }

    public function test_it_redirects_when_account_has_not_agreed_to_the_service_terms()
    {
        // given the account has the plan
        /** @var Order $order */
        $order = factory(Order::class)->create(['status' => Order::STATUS_CLOSED]);
        $plan = Plan::find(Plan::PLAN_ID_SOCIAL_MEDIA_SHARES);
        $this->account->plans()->attach($plan->id);
        $order->plans()->attach($plan->id);

        $this->assertEmpty($plan->currentServiceTerms()->serviceTermsAgreements);

        // when it receives a request
        Route::shouldReceive('currentRouteName')->andReturn('branded-posts.index');
        $response = $this->middleware->handle(
            Request::create(rmc_route('branded-posts.index')),
            function () {
            },
            $this->alias
        );

        // it saves the route and the product the user is trying to see
        $this->assertEquals($plan->id, $response->getSession()->get('product_to_confirm'));
        $this->assertEquals('branded-posts.index', $response->getSession()->get('product_dashboard'));

        // it responds with a redirect prompting the user to accept the service terms
        $this->assertTrue($response->isRedirect(rmc_route('orders.show')));
    }

    public function test_it_doesnt_redirect_when_user_is_employee()
    {
        // given the account has the plan
        $plan = Plan::find(Plan::PLAN_ID_SOCIAL_MEDIA_SHARES);
        $this->account->plans()->attach($plan->id);

        // but the account hasn't agreed to the service terms
        $this->assertEmpty($plan->currentServiceTerms()->serviceTermsAgreements);

        // and the user who is using the system is one of our employees
        $employee = $this->setupAuthorizedUser();
        $this->assertTrue($employee->isEmployee());
        $this->actingAs($employee);

        // when it receives a request
        $redirect = $this->middleware->handle(new Request, function () {
        }, $this->alias);

        // it doesn't respond with a redirect
        $this->assertNull($redirect);
    }

    /** @test */
    public function it_expects_no_redirect_on_landing_page_order_with_tos()
    {
        // given the account has the landing page plan
        $plan = Plan::find(Plan::PLAN_ID_LANDING_PAGES);
        $this->account->plans()->attach($plan->id);

        // and a closed order
        $order = factory(Order::class)->create(['status' => Order::STATUS_CLOSED]);
        $order->plans()->attach($plan->id);

        // sign the tos
        $order->serviceTermsAgreements()->save(
            new ServiceTermsAgreement([
                'service_terms_id' => $plan->currentServiceTerms()->id,
                'ip' => $this->faker->ipv4,
                'account_id' => $this->account->id
            ])
        );

        // when it receives a request
        $response = $this->middleware->handle(
            Request::create('/landing-pages'),
            function () {
            },
            'lp'
        );

        // it doesn't respond with a redirect
        $this->assertNull($response);
    }
}
