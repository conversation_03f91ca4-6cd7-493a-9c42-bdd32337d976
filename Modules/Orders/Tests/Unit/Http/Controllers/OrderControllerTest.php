<?php

namespace Modules\Orders\Tests\Unit\Http\Controllers;

use App\Contracts\AccountRepository;
use App\Models\Account;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\View\View;
use Mockery;
use Mockery\MockInterface;
use Modules\Orders\Contracts\OrderRepository;
use Modules\Orders\Contracts\ShoppingCartRepository;
use Modules\Orders\Domain\Order\ShoppingCartAggregateRoot;
use Modules\Orders\Http\Controllers\OrderController;
use Modules\Orders\Models\Order;
use Modules\Xrms\Contracts\ContractRepository;
use Modules\Xrms\Contracts\PaymentMethodRepository;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Tests\TestCase;

class OrderControllerTest extends TestCase
{
    use WithoutMiddleware;

    /** @var AccountRepository */
    private $accountRepo;

    /** @var OrderController */
    private $controller;

    /** @var OrderRepository */
    private $orderRepo;

    /** @var PaymentMethodRepository */
    private $paymentMethodRepo;

    /** @var ContractRepository */
    private $contractRepo;

    /** @var ShoppingCartRepository */
    private $shoppingCartRepo;

    /** @var ShoppingCartAggregateRoot */
    private $aggregateRoot;

    protected function setUp(): void
    {
        parent::setUp();

        $this->accountRepo = $this->mock(AccountRepository::class);

        $this->controller = new OrderController($this->accountRepo);

        $this->orderRepo = $this->mock(OrderRepository::class);
        $this->paymentMethodRepo = $this->mock(PaymentMethodRepository::class);
        $this->contractRepo = $this->mock(ContractRepository::class);
        $this->shoppingCartRepo = $this->mock(ShoppingCartRepository::class);
        $this->aggregateRoot = $this->mock(ShoppingCartAggregateRoot::class);
    }

    /** @test */
    public function it_expects_404()
    {
        $this->withNoLatestOrder();

        $this->expectException(NotFoundHttpException::class);

        $response = $this->controller->show($this->orderRepo, $this->paymentMethodRepo);

        $response->assertStatus(404);
    }

    /** @test */
    public function it_expects_view_returned()
    {
        $partialMock = Mockery::mock(OrderController::class, [$this->accountRepo])->makePartial();
        $partialMock->shouldAllowMockingProtectedMethods();

        $this->withLatestOrder()
            ->withCurrentAccount()
            ->withLatestUsedMethod();

        $partialMock->shouldReceive('canEnroll')
            ->andReturnTrue()
            ->once();

        $response = $partialMock->show($this->orderRepo, $this->paymentMethodRepo);

        $this->assertInstanceOf(View::class, $response);
    }

    /** @test */
    public function it_expects_success_on_update()
    {
        /** @var MockInterface|Request $request */
        $request = $this->mock(Request::class);

        $this->thatFindsShoppingCartAndSignsTos($request);

        $response = $this->controller->update($request, $this->shoppingCartRepo);

        $this->assertEquals(200, $response->getStatusCode());
    }

    /** @test */
    public function it_expects_success_on_store()
    {
        $this->thatCreatesShoppingCart()
            ->withPlanIdsAndBundleId()
            ->andEnrollsAccount(false);

        $response = $this->controller->store($this->contractRepo, $this->shoppingCartRepo);

        $this->assertEquals(201, $response->getStatusCode());
    }

    /** @test */
    public function it_expects_shopping_cart_cleared()
    {
        $this->thatCreatesShoppingCart()
            ->withPlanIdsAndBundleId()
            ->andEnrollsAccount(true)
            ->andClearsShoppingCart();

        $response = $this->controller->store($this->contractRepo, $this->shoppingCartRepo);

        $this->assertEquals(201, $response->getStatusCode());
    }

    protected function withLatestOrder(): self
    {
        $this->orderRepo->expects('getLatestOrder')
            ->andReturn(new Order())
            ->once();

        return $this;
    }

    protected function withNoLatestOrder(): self
    {
        $this->orderRepo->shouldReceive('getLatestOrder')
            ->andReturnNull()
            ->once();

        return $this;
    }

    protected function withCurrentAccount(): self
    {
        $this->accountRepo->shouldReceive('currentAccount')
            ->andReturn(new Account())
            ->once();

        return $this;
    }

    protected function withLatestUsedMethod(): self
    {
        $this->paymentMethodRepo->shouldReceive('getLastUsedPaymentMethod')
            ->andReturn()
            ->once();

        return $this;
    }

    protected function thatFindsShoppingCartAndSignsTos(Request $request): self
    {
        $request->shouldReceive('get')
            ->andReturn(12345)
            ->once();
        $this->shoppingCartRepo->shouldReceive('findOrCreateShoppingCart')
            ->andReturn($this->aggregateRoot)
            ->once();

        $request->shouldReceive('ip')
            ->andReturn('ip_address')
            ->once();
        $this->aggregateRoot->shouldReceive('signServiceTermsAgreements')
            ->andReturnSelf()
            ->once();
        $this->aggregateRoot->shouldReceive('persist')
            ->andReturnSelf()
            ->once();

        return $this;
    }

    protected function thatCreatesShoppingCart(): self
    {
        $this->shoppingCartRepo->shouldReceive('findOrCreateShoppingCart')
            ->andReturn($this->aggregateRoot)
            ->once();

        return $this;
    }

    protected function withPlanIdsAndBundleId(): self
    {
        $this->aggregateRoot->shouldReceive('getShoppingCartPlans')
            ->andReturn(new Collection())
            ->once();
        $this->aggregateRoot->shouldReceive('getShoppingCartBundleId')
            ->andReturn(123)
            ->once();

        return $this;
    }

    protected function andEnrollsAccount(bool $isEnrolled): self
    {
        $this->contractRepo->shouldReceive('enroll')
            ->andReturn($isEnrolled)
            ->once();

        return $this;
    }

    protected function andClearsShoppingCart(): self
    {
        $this->aggregateRoot->shouldReceive('clearShoppingCart')
            ->andReturnSelf()
            ->once();
        $this->aggregateRoot->shouldReceive('persist')
            ->andReturnSelf()
            ->once();

        return $this;
    }

}
