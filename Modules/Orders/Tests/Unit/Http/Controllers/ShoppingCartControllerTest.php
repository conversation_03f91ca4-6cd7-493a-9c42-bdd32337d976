<?php

namespace Modules\Orders\Tests\Unit\Http\Controllers;

use App\Models\EmailAddress;
use App\Models\Plan;
use Domain\Account\Models\AccountPlan;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Mockery\MockInterface;
use Modules\Orders\Models\ShoppingCart;
use Modules\Pricing\Services\PricePointService;
use Modules\Xrms\Contracts\PaymentMethodRepository;
use Modules\Xrms\Entities\PaymentMethod;
use Tests\RefreshDatabase;
use Tests\TestCase;
use Tests\TestsAuthorization;

class ShoppingCartControllerTest extends TestCase
{
    use RefreshDatabase;
    use TestsAuthorization;
    use WithFaker;
    use WithoutMiddleware;

    /** @var Account */
    public $account;

    public function test_it_stores_plan()
    {
        // given the user doesn't have a ShoppingCart yet
        $this->assertEmpty(ShoppingCart::first());

        // when it receives a request with the plan id
        $response = $this->post(route('cart.store'), ['plan_id' => Plan::PLAN_ID_DIGITAL_EDITIONS]);

        // it comes back with success
        $response->assertSuccessful();
        $this->assertTrue($response->json('success'));

        // the user has now a shopping cart
        $shoppingCart = ShoppingCart::first();
        $this->assertInstanceOf(ShoppingCart::class, $shoppingCart);

        // and it got the plan added
        $this->assertInstanceOf(Plan::class, $shoppingCart->plans->firstWhere('id', Plan::PLAN_ID_DIGITAL_EDITIONS));
    }

    public function test_it_stores_bundle()
    {
        // given the bundle and its plans
        $bundleId = Plan::BUNDLE_ID_DIGITAL_MARKETING_WITHOUT_MAGAZINE;
        $bundlePlans = [
            Plan::PLAN_ID_DIGITAL_EDITIONS,
            Plan::PLAN_ID_LOCAL_EVENT,
            Plan::PLAN_ID_BRANDED_POSTS,
            Plan::PLAN_ID_SOCIAL_MEDIA_AUTOMATION
        ];

        // and that the account doesn't have a magazine plan
        $this->assertFalse(AccountPlan::hasMagazine());

        // when it enrolls in a bundle
        $response = $this->post(route('cart.store'), ['is_bundle' => true]);

        // it comes back with success
        $response->assertSuccessful();
        $this->assertTrue($response->json('success'));

        // the shopping cart has now a bundle
        $shoppingCart = ShoppingCart::first();
        $this->assertEquals($bundleId, $shoppingCart->bundle_id);
        $this->assertEquals($bundlePlans, $shoppingCart->plans->pluck('id')->toArray());
    }

    public function test_it_forces_bundle_when_account_has_at_least_one_digital_product()
    {
        // given the bundle and its plans
        $bundleId = Plan::BUNDLE_ID_DIGITAL_MARKETING_WITHOUT_MAGAZINE;
        $bundlePlans = [
            Plan::PLAN_ID_DIGITAL_EDITIONS,
            Plan::PLAN_ID_LOCAL_EVENT,
            Plan::PLAN_ID_BRANDED_POSTS,
            Plan::PLAN_ID_SOCIAL_MEDIA_AUTOMATION
        ];

        //Add a digital product to the account
        $this->account->assignPlan(Plan::find(Plan::PLAN_ID_DIGITAL_EDITIONS));

        // and that the account doesn't have a magazine plan
        $this->assertFalse(AccountPlan::hasMagazine());

        //assert it does have digital products
        $this->assertTrue(AccountPlan::digitalProducts()->exists());

        // when it enrolls in a digital product
        $response = $this->post(route('cart.store'), ['plan_id' => Plan::PLAN_ID_LOCAL_EVENT]);

        // it comes back with success
        $response->assertSuccessful();
        $this->assertTrue($response->json('success'));

        // the shopping cart has now a bundle
        $shoppingCart = ShoppingCart::first();
        $this->assertEquals($bundleId, $shoppingCart->bundle_id);
        $this->assertEquals($bundlePlans, $shoppingCart->plans->pluck('id')->toArray());
    }

    public function test_show()
    {
        $user = $this->setupAuthorizedUser();
        $user->giveOwnershipOfAccount($this->account);

        // the account needs an email address to display in the order summary
        factory(EmailAddress::class)->create([
            'is_primary' => true,
        ]);

        // given a shopping cart with plans and the prices from XRMS
        $shoppingCart = ShoppingCart::create();
        $this->givenThePricesForOrderSummary($shoppingCart);
        $plans = $shoppingCart->plans->map(function ($plan) {
            return ['name' => $plan->name, 'price' => 10.00, 'id' => $plan->id];
        });

        // and the account has a valid payment method
        $paymentMethod = $this->givenThePaymentMethod();

        // when the user access this route
        $response = $this->get(route('cart.show'));

        // stuff doesn't blow up
        $response->assertSuccessful();

        // it gets a viewtiful blade template with some data
        $response->assertViewIs('orders::place-order');
        $response->assertViewHasAll([
            'totalDiscount' => 0,
            'paymentMethod' => $paymentMethod,
            'plans' => $plans->toArray(),
        ]);
    }

    public function test_destroy()
    {
        $shoppingCart = ShoppingCart::create();
        $shoppingCart->plans()->attach(Plan::PLAN_ID_DIGITAL_EDITIONS);

        $response = $this->delete(route('cart.destroy'));
        $response->assertSuccessful();
        $this->assertEquals($response->json('redirect_url'), route('dashboard.index'));
        $this->assertEmpty($shoppingCart->refresh()->plans);
    }

    # Helpers #

    protected function givenThePricesForOrderSummary(ShoppingCart $shoppingCart) : void
    {
        $planIds = [ Plan::PLAN_ID_DIGITAL_EDITIONS, Plan::PLAN_ID_LOCAL_EVENT ];
        $shoppingCart->plans()->attach($planIds);

        $service = $this->mock(PricePointService::class)->makePartial();

        $service->shouldReceive('getBundleDiscounts')
            ->withArgs([$planIds, $shoppingCart->bundle_id])
            ->andReturn(0);

        $service->shouldReceive('getPricingFor')
            ->withArgs([PLAN::PLAN_ID_DIGITAL_EDITIONS, $shoppingCart->bundle_id])
            ->andReturn(10.00);

        $service->shouldReceive('getPricingFor')
            ->withArgs([PLAN::PLAN_ID_LOCAL_EVENT, $shoppingCart->bundle_id])
            ->andReturn(10.00);
    }

    protected function givenThePaymentMethod() : PaymentMethod
    {
        $paymentMethod = new PaymentMethod(
            $this->faker->creditCardType,
            $this->faker->creditCardNumber,
            $this->faker->month,
            $this->faker->year,
            $this->faker->randomNumber(3),
            $this->faker->name,
            $this->faker->postcode
        );

        $this->mock(PaymentMethodRepository::class, function (MockInterface $mock) use ($paymentMethod) {
            $mock->shouldReceive('getPaymentMethod')->andReturn($paymentMethod);
        });

        return $paymentMethod;
    }
}
