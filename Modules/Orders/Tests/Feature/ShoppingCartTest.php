<?php

namespace Modules\Orders\Tests\Feature;

use App\Context\AccountId;
use App\Models\Plan;
use Domain\Account\Models\AccountPlan;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Modules\Orders\Contracts\ShoppingCartRepository;
use Modules\Orders\Models\ShoppingCart;
use Tests\RefreshDatabase;
use Tests\TestCase;
use Tests\TestsAuthorization;

class ShoppingCartTest extends TestCase
{
    use RefreshDatabase;
    use TestsAuthorization;
    use WithoutMiddleware;

    protected function setUp(): void
    {
        parent::setUp();

        $user = $this->setupValidApiUser();
        $user->markEmailAsVerified();
    }

    /** @test */
    public function makes_shopping_cart_when_none_exist()
    {
        // Assert that no ShoppingCarts exist
        $this->assertNull(ShoppingCart::first());

        // Activate a product
        $this->activateProduct(Plan::PLAN_ID_DIGITAL_EDITIONS);

        // Assert that an ShoppingCart now exists
        $this->assertNotNull(ShoppingCart::first());
    }

    /** @test */
    public function makes_shopping_cart_and_adds_to_shopping_cart()
    {
        // Assert that no ShoppingCarts exist
        $this->assertNull(ShoppingCart::first());

        // Call the endpoint that activates a product by adding it to an ShoppingCart
        $this->activateProduct(Plan::PLAN_ID_DIGITAL_EDITIONS);

        // Check that an ShoppingCart now exists and check its Plans length
        $this->assertNotNull(ShoppingCart::first());
        $this->assertCount(1, ShoppingCart::first()->plans);

        // Add product to cart, it should add it to the order instead of creating another
        $this->activateProduct(Plan::PLAN_ID_LOCAL_EVENT);
        $this->assertCount(2, ShoppingCart::first()->plans);
    }

    /** @test */
    public function makes_shopping_cart_ignores_same_product()
    {
        // Assert that no ShoppingCarts exist
        $this->assertNull(ShoppingCart::first());

        // Call the endpoint to activate product
        $this->activateProduct(Plan::PLAN_ID_DIGITAL_EDITIONS);

        // Check ShoppingCart has 1 Plan
        $this->assertCount(1, ShoppingCart::first()->plans);

        // Call the endpoint again with the same product
        $this->activateProduct(Plan::PLAN_ID_DIGITAL_EDITIONS);

        // Assert that ShoppingCart still has 1 Plan
        $this->assertCount(1, ShoppingCart::first()->plans);
    }

    /** @test */
    public function makes_bundle_one_shopping_cart_without_magazine()
    {
        // Assert that no ShoppingCarts exist
        $this->assertNull(ShoppingCart::first());

        // Call endpoint to activate bundle
        $this->activateProduct(0, true);

        // Assert that ShoppingCart has 4 Plans and bundle_id is Plan::BUNDLE_ID_DIGITAL_MARKETING_WITHOUT_MAGAZINE
        $this->assertCount(4, ShoppingCart::first()->plans);
        $this->assertEquals(Plan::BUNDLE_ID_DIGITAL_MARKETING_WITHOUT_MAGAZINE, ShoppingCart::first()->bundle_id);
    }

    /** @test */
    public function makes_bundle_one_shopping_cart_with_magazine()
    {
        // Get the account and add Magazine to it
        AccountPlan::create(['plan_id' => Plan::PLAN_ID_AMERICAN_LIFESTYLE_MAGAZINE]);

        // Assert that no ShoppingCarts exist
        $this->assertNull(ShoppingCart::first());

        // Call endpoint to activate bundle
        $this->activateProduct(0, true);

        // Assert that the ShoppingCart has 4 Plans and the bundle_id is (Plan::BUNDLE_ID_DIGITAL_MARKETING_WITH_MAGAZINE)
        $this->assertCount(4, ShoppingCart::first()->plans);
        $this->assertEquals(Plan::BUNDLE_ID_DIGITAL_MARKETING_WITH_MAGAZINE, ShoppingCart::first()->bundle_id);
    }

    /** @test */
    public function two_bundle_calls_one_shopping_cart()
    {
        // Assert that no ShoppingCarts exist
        $this->assertNull(ShoppingCart::first());

        // Call endpoint to activate bundle twice
        $this->activateProduct(0, true);
        $this->activateProduct(0, true);

        // Assert that only 1 order exists and that it has 4 Plans
        $this->assertCount(1, ShoppingCart::all());
        $this->assertCount(4, ShoppingCart::first()->plans);
    }

    /** @test */
    public function check_shopping_cart_has_account_id()
    {
        // Activate a Product
        $this->activateProduct(Plan::PLAN_ID_DIGITAL_EDITIONS);

        // Check that the Context account ID matches Shopping Cart
        $this->assertEquals(AccountId::current()->id(), ShoppingCart::first()->account_id);
    }

    /** @test */
    public function shopping_cart_first_shows_correct_one_for_account()
    {
        // Activate a Product
        $this->activateProduct(Plan::PLAN_ID_DIGITAL_EDITIONS);
        $firstAccountId = AccountId::current()->id();

        // Change the Context to a new account
        $this->createAccount();
        $user = $this->setupValidApiUser();
        $user->markEmailAsVerified();
        $secondAccountId = AccountId::current()->id();

        // Activate a Product
        $this->activateProduct(2);

        // Check that the first account ID is the 2nd account ID and not the first (BelongsToAccount trait)
        $this->assertEquals($secondAccountId, ShoppingCart::first()->account_id);
        $this->assertNotEquals($firstAccountId, ShoppingCart::first()->account_id);
    }

    /** @test */
    public function clears_shopping_cart()
    {
        // Activate a bundle
        $this->activateProduct(0, true);

        // Assert that the ShoppingCart contains 4 Plans and bundle_id is not null
        $this->assertCount(4, ShoppingCart::first()->plans);
        $this->assertNotNull(ShoppingCart::first()->bundle_id);

        // Launch new ClearShoppingCart Event via Persist on AR
        /** @var \Modules\Orders\Domain\Order\ShoppingCartAggregateRoot $shoppingCartAR */
        $shoppingCartAR = app(ShoppingCartRepository::class)->findOrCreateShoppingCart();
        $shoppingCartAR->clearShoppingCart()->persist();

        // Assert that the count of ShoppingCart plans is 0 and bundle_id is null
        $this->assertCount(0, ShoppingCart::first()->plans);
        $this->assertEquals(0, ShoppingCart::first()->bundle_id);
    }

    private function activateProduct(int $planId = 0, bool $isBundle = false)
    {
        if ($isBundle) {
            $data = ['is_bundle' => $isBundle];
        } else {
            $data = ['plan_id' => $planId];
        }

        $response = $this->post(route('cart.store'), $data);
        $response->assertStatus(200);
    }

}
