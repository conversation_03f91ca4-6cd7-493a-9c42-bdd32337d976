<?php

namespace Modules\Orders\Tests\Feature;

use App\Models\Account;
use App\Models\Plan;
use App\Models\User;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Modules\Orders\Domain\Order\Events\OrderAdded;
use Modules\Orders\Models\Order;
use Modules\Orders\Models\ServiceTerms;
use Modules\Orders\Models\ServiceTermsAgreement;
use Modules\Orders\ValueObjects\MasterServiceTerms;
use Tests\RefreshDatabase;
use Tests\TestCase;

class OrderTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;
    use WithoutMiddleware;

    /** @var \App\Models\User $user */
    private $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = $this->setupUnauthorizedUser();
        $this->account = factory(Account::class)->create();
        $this->user->giveOwnershipOfAccount($this->account);
    }

    /** @test */
    public function it_creates_an_order_with_no_signed_tos()
    {
        // Create the Event
        $orderAddedEvent = $this->createOrderAddedEvent([Plan::PLAN_ID_DIGITAL_EDITIONS], false);

        // Launch the Event
        $this->actOnInterserviceEvent($orderAddedEvent);

        // Assert that an Order was created with no TOS and has 1 plan whose ID is Plan::PLAN_ID_DIGITAL_EDITIONS
        $order = Order::first();
        $this->assertNotNull($order);
        $this->assertCount(1, $order->plans);
        $this->assertEquals(Plan::PLAN_ID_DIGITAL_EDITIONS, $order->plans()->first()->id);
        $this->assertCount(0, $order->serviceTermsAgreements);
    }

    /** @test */
    public function it_creates_an_order_with_signed_tos()
    {
        // Create the Event
        $orderAddedEvent = $this->createOrderAddedEvent([Plan::PLAN_ID_DIGITAL_EDITIONS], true);

        // Launch the Event
        $this->actOnInterserviceEvent($orderAddedEvent);

        // Assert that an Order was created with TOS and has 1 plan whose ID is Plan::PLAN_ID_DIGITAL_EDITIONS
        $order = Order::first();
        $this->assertNotNull($order);
        $this->assertCount(1, $order->plans);
        $this->assertEquals(Plan::PLAN_ID_DIGITAL_EDITIONS, $order->plans()->first()->id);
        $this->assertCount(1, $order->serviceTermsAgreements);
        $this->assertEquals(Plan::PLAN_ID_DIGITAL_EDITIONS,
            $order->serviceTermsAgreements()->first()->serviceTerms()->first()->plan_id);

        // assert that has the Master Terms of Service too
        $masterServiceTerms = ServiceTerms::where('name', '=', MasterServiceTerms::toString())->first();
        $this->assertDatabaseHas('service_terms_agreements', [
            'service_terms_id' => $masterServiceTerms->id,
            'account_id' => $this->account->id
        ]);
    }

    /** @test */
    public function it_creates_an_order_with_multiple_plans_multiple_tos()
    {
        // Create the Event
        $orderAddedEvent = $this->createOrderAddedEvent(
            [Plan::PLAN_ID_DIGITAL_EDITIONS, Plan::PLAN_ID_LOCAL_EVENT, Plan::PLAN_ID_SOCIAL_MEDIA_SHARES],
            true,
            Plan::BUNDLE_ID_DIGITAL_MARKETING_WITHOUT_MAGAZINE
        );

        // Launch the Event
        $this->actOnInterserviceEvent($orderAddedEvent);

        // Assert that an Order was created with 3 TOS and 3 plans and
        // has bundle_id set to Plan::BUNDLE_ID_DIGITAL_MARKETING_WITHOUT_MAGAZINE
        /** @var Order $order */
        $order = Order::first();
        $this->assertNotNull($order);
        $this->assertCount(3, $order->plans);

        // Pluck the IDs and do assertContains on the 3 plans
        $planIds = $order->plans->pluck('id');
        $this->assertContains(Plan::PLAN_ID_DIGITAL_EDITIONS, $planIds);
        $this->assertContains(Plan::PLAN_ID_LOCAL_EVENT, $planIds);
        $this->assertContains(Plan::PLAN_ID_SOCIAL_MEDIA_SHARES, $planIds);

        // Check all the ServiceTermsAgreements
        $this->assertCount(3, $order->serviceTermsAgreements);

        // Pluck the plan_id from each ServiceTermsAgreements
        $this->orderHasAllServiceTerms($order);

        // assert that has the Master Terms of Service too
        $masterServiceTerms = ServiceTerms::where('name', '=', MasterServiceTerms::toString())->first();
        $this->assertDatabaseHas('service_terms_agreements', [
            'service_terms_id' => $masterServiceTerms->id,
            'account_id' => $this->account->id
        ]);
    }

    /** @test */
    public function it_creates_order_no_signed_tos_signs_after_in_confirm_order()
    {
        // Create the Event
        $orderAddedEvent = $this->createOrderAddedEvent([Plan::PLAN_ID_DIGITAL_EDITIONS], false);

        // Launch the Event
        $this->actOnInterserviceEvent($orderAddedEvent);

        // Assert that the Order has no signed ServiceTermsAgreements
        $order = Order::first();
        $this->assertNotNull($order);
        $this->assertCount(0, $order->serviceTermsAgreements);

        // Call the Confirm Order endpoint
        $this->confirmOrder($order);

        // Assert that the Order now has Signed terms and that it's for Plan::PLAN_ID_DIGITAL_EDITIONS
        /** @var Plan $plan */
        $plan = Plan::find(Plan::PLAN_ID_DIGITAL_EDITIONS);
        $order->refresh();

        $this->assertCount(1, $order->serviceTermsAgreements);
        $serviceTermsAgreement = $order->serviceTermsAgreements()->first();
        $this->assertEquals($serviceTermsAgreement->serviceTerms->plan_id, $plan->id);
    }

    /** @test */
    public function creates_order_bundle_signs_tos_in_confirm_order()
    {
        // Create the Event
        $orderAddedEvent = $this->createOrderAddedEvent([
            Plan::PLAN_ID_DIGITAL_EDITIONS,
            Plan::PLAN_ID_LOCAL_EVENT,
            Plan::PLAN_ID_SOCIAL_MEDIA_SHARES
        ], false);

        // Launch the event
        $this->actOnInterserviceEvent($orderAddedEvent);

        // Assert that service terms agreements is empty
        /** @var Order $order */
        $order = Order::first();
        $this->assertEmpty($order->serviceTermsAgreements);

        // Call the Confirm Order endpoint
        $this->confirmOrder($order);

        // Assert that count is 3 and has a Service Terms for each plan type
        $order->refresh();
        $this->assertCount(3, $order->serviceTermsAgreements);

        // Pluck the plan_id from each ServiceTermsAgreements
        $this->orderHasAllServiceTerms($order);

        // assert that has the Master Terms of Service too
        $masterServiceTerms = ServiceTerms::where('name', '=', MasterServiceTerms::toString())->first();
        $this->assertDatabaseHas('service_terms_agreements', [
            'service_terms_id' => $masterServiceTerms->id,
            'account_id' => $this->account->id
        ]);
    }

    public function orderHasAllServiceTerms(Order $order)
    {
        $serviceTermsPlanIds[] = $order->serviceTermsAgreements()->each(
            function (ServiceTermsAgreement $serviceTermsAgreement) {
                return $serviceTermsAgreement->serviceTerms->plan_id;
            }
        );

        // Assert that each plan has a ServiceTerms
        $this->assertContains(Plan::PLAN_ID_DIGITAL_EDITIONS, $serviceTermsPlanIds);
        $this->assertContains(Plan::PLAN_ID_LOCAL_EVENT, $serviceTermsPlanIds);
        $this->assertContains(Plan::PLAN_ID_SOCIAL_MEDIA_SHARES, $serviceTermsPlanIds);
    }

    private function confirmOrder(Order $order)
    {
        $data = ['order_id' => $order->id, 'ip' => '*******'];
        $response = $this->put(route('orders.update'), $data);

        $response->assertStatus(200);
    }

    private function createOrderAddedEvent(array $planIds, bool $shouldSignTos, int $bundleId = 0)
    {
        return $this->account->execute(function () use ($planIds, $shouldSignTos, $bundleId) {
            // todo remove calls directly to `::fromArray` and force use of constructor or named static methods.
            // from array should only be used when un-serializing an event.
            return OrderAdded::fromArray([
                'uuid'        => $this->faker->uuid,
                'occurred_at' => now()->toDateTimeString(),
                'global_name' => 'orders.order-added',
                'data'        => [
                    'bundle_id'       => $bundleId,
                    'plans'           => $planIds,
                    'should_sign_tos' => $shouldSignTos,
                    'ip'              => '*******',
                    'pricing'         => [
                        $bundleId => [
                            'product-id'              => $bundleId,
                            'enrolling-user'          => 0,
                            'commission-earning-user' => 0,
                            'bundle-id'               => '3',
                            'original-price'          => '39.99',
                            'discounts'               => [],
                            'price'                   => '39.99',
                            'charge-setup-fee'        => true,
                        ]
                    ]
                ],
                'metadata'    => [
                    'source'  => 'crm',
                    'context' => ['account_id' => $this->account->id, 'user_id' => $this->user->getId()],
                ],
            ]);
        });
    }
}
