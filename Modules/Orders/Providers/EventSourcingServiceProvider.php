<?php

namespace Modules\Orders\Providers;

use App\EventSourcing\ServiceProvider as BaseServiceProvider;
use Modules\Orders\Domain\Order\Projectors\OrderProjector;
use Modules\Orders\Domain\Order\Projectors\ShoppingCartProjector;
use Modules\Orders\Domain\Order\Reactors\OrderReactor;

class EventSourcingServiceProvider extends BaseServiceProvider
{
    protected $projectors = [
        ShoppingCartProjector::class,
        OrderProjector::class
    ];

    protected $reactors = [
        OrderReactor::class
    ];

}
