<?php

namespace Modules\Orders\Providers;

use Infrastructure\Http\AbstractRouteServiceProvider;

class RouteServiceProvider extends AbstractRouteServiceProvider
{
    /**
     * This namespace is applied to your controller routes.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'Modules\Orders\Http\Controllers';

    public function routeDirectory() : string
    {
        return module_path('Orders') . '/routes';
    }
}
