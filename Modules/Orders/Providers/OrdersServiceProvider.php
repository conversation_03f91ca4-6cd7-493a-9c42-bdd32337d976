<?php

namespace Modules\Orders\Providers;

use Faker\Generator;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Factory;
use Modules\Orders\Console\ImportServiceTermsAgreements;
use Modules\Orders\Contracts\OrderRepository;
use Modules\Orders\Contracts\ShoppingCartRepository as ShoppingCartRepositoryContract;
use Modules\Orders\Repositories\EloquentOrderRepository;
use Modules\Orders\Repositories\ShoppingCartRepository;

class OrdersServiceProvider extends ServiceProvider
{
    /**
     * Indicates if loading of the provider is deferred.
     *
     * @var bool
     */
    protected $defer = false;

    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->registerFactories();
        $this->loadMigrationsFrom(__DIR__ . '/../Database/Migrations');
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->registerRepositories();
        $this->commands([
            ImportServiceTermsAgreements::class,
        ]);
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            __DIR__ . '/../Config/config.php' => config_path('orders.php'),
        ], 'config');

        $this->mergeConfigFrom(__DIR__ . '/../Config/config.php', 'orders');
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews()
    {
        $viewPath = resource_path('views/modules/orders');

        $sourcePath = __DIR__ . '/../Resources/views';

        $this->publishes([
            $sourcePath => $viewPath,
        ], 'views');

        $paths = collect(Config::get('view.paths'))
            ->map(function ($path) {
                return $path . '/modules/orders';
            })
            ->merge([$sourcePath])
            ->filter(function ($path) {
                return is_dir($path);
            })->toArray();

        $this->loadViewsFrom($paths, 'orders');
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/orders');

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'orders');
        } else {
            $this->loadTranslationsFrom(__DIR__ .'/../Resources/lang', 'orders');
        }
    }

    /**
     * Register an additional directory of factories.
     *
     * @return void
     */
    public function registerFactories()
    {
        if (! class_exists(Generator::class)) {
            return;
        }

        app(Factory::class)->load(__DIR__ . '/../Database/factories');
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }

    private function registerRepositories()
    {
        $this->app->bind(ShoppingCartRepositoryContract::class, ShoppingCartRepository::class);
        $this->app->bind(OrderRepository::class, EloquentOrderRepository::class);
    }
}
