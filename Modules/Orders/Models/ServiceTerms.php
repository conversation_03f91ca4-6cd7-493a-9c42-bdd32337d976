<?php

namespace Modules\Orders\Models;

use App\Models\Plan;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class ServiceTerms
 * @package Modules\Orders\Models
 * @property Plan $plan
 * @property \Illuminate\Support\Collection|ServiceTermsAgreement[] $serviceTermsAgreements
 */
class ServiceTerms extends Model
{
    protected $guarded = [];

    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    public function serviceTermsAgreements(): HasMany
    {
        return $this->hasMany(ServiceTermsAgreement::class);
    }
}
