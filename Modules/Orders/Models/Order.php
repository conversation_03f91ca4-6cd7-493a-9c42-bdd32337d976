<?php

namespace Modules\Orders\Models;

use App\Models\Plan;
use App\Traits\BelongsToAccount;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Infrastructure\Audit\Traits\RmcAuditable;
use OwenIt\Auditing\Contracts\Auditable;

/**
 * Class Order
 * @package Modules\Orders\Models
 * @property \Illuminate\Support\Collection|Plan[] $plans
 * @property \Illuminate\Support\Collection|ServiceTermsAgreement[] $serviceTermsAgreements
 */
class Order extends Model implements Auditable
{
    use BelongsToAccount;
    use RmcAuditable;

    protected $guarded = [];

    protected $casts = [
        'pricing' => 'array'
    ];

    const STATUS_CHARGED = 1;
    const STATUS_CLOSED = 2;
    const STATUS_CANCELLED = 3;

    public function plans(): BelongsToMany
    {
        return $this->belongsToMany(Plan::class, 'order_plans')->withTimestamps();
    }

    public function serviceTermsAgreements(): HasMany
    {
        return $this->hasMany(ServiceTermsAgreement::class);
    }
}
