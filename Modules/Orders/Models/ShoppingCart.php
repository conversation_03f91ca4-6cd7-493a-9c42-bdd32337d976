<?php

namespace Modules\Orders\Models;

use App\Models\Account;
use App\Models\Plan;
use App\Traits\BelongsToAccount;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * Class ShoppingCart
 * @package Modules\Orders\Models
 * @mixin \Illuminate\Database\Eloquent\Builder
 * @property \Illuminate\Support\Collection|Plan[] $plans
 * @property \App\Models\Account $account
 */
class ShoppingCart extends Model
{
    use BelongsToAccount;

    protected $guarded = [];

    public function plans(): BelongsToMany
    {
        return $this->belongsToMany(Plan::class, 'shopping_cart_plans')->withTimestamps();
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }
}
