<?php

namespace Modules\Orders\Models;

use App\Models\User;
use App\Traits\BelongsToAccount;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class ServiceTermsAgreement
 * @package Modules\Orders\Models
 * @property Order $order
 * @property ServiceTerms $serviceTerms
 */
class ServiceTermsAgreement extends Model
{
    use BelongsToAccount;

    protected $guarded = [];

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function serviceTerms(): BelongsTo
    {
        return $this->belongsTo(ServiceTerms::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
