@extends('layouts.main')

@section('title', 'Checkout')

@section('content')
    <div class="row expanded">
        <div class="column">
            <div class="alignleft">
                @if ($isCheckout)
                    <h1>
                        <i
                            class="icon ion-locked text-color3 opacity text-xlarge position-relative"
                            style="top: -3px;"
                        ></i>Secure Checkout
                    </h1>
                @else
                    <h1>Order Confirmation</h1>
                @endif
            </div>
        </div>
    </div>

    <div class="row expanded">
        <div class="column large-6 xlarge-4 large-push-6 xlarge-push-8">
            <rm-order-summary :is-checkout="{{ $isCheckout ? "true" : "false" }}"/>
        </div>

        <div class="column large-6 xlarge-8 large-pull-6 xlarge-pull-4">
            <div class="box inner-large">
                <h2>Account Information</h2>
                <div class="row expanded">
                    @if ($paymentMethod)
                        {{--Name--}}
                        <div class="column xlarge-3">
                            <div class="text-medium text-light">Name</div>
                            <p class="text-large wrap">{{ $paymentMethod->getName() }}</p>
                        </div>

                        {{--Email Address--}}
                        <div class="column xlarge-4">
                            <div class="text-medium text-light">Email Address</div>
                            <p class="text-large wrap">{{ $email }}</p>
                        </div>

                        {{--Address--}}
                        <div class="column xlarge-5">
                            <div class="text-medium text-light">Address</div>
                            <p class="text-large wrap">
                                <span>{{ $paymentMethod->getBillingAddress1() . "," }}</span>
                                <span>{{ $paymentMethod->getBillingAddress2() ? $paymentMethod->getBillingAddress2() . "," : "" }}</span>
                                <span>{{ $paymentMethod->getCity() }}, {{ $paymentMethod->getState() }} {{ $paymentMethod->getZip() }}</span>
                            </p>
                        </div>
                </div>

                <hr>

                <h2>Payment Information</h2>

                <div class="row expanded">

                    {{--Name on Card--}}
                    <div class="column medium-3">
                        <div class="text-medium text-light">Name on Card</div>
                        <p class="text-large wrap">{{ $paymentMethod->getName() }}</p>
                    </div>

                    {{--Credit Card--}}
                    <div class="column medium-3">
                        <div class="text-medium text-light">Credit Card</div>
                        <p class="text-large">{{ $paymentMethod->getCardTypeFullName() }} ending in {{ $paymentMethod->getLast4Digits() }}</p>
                    </div>

                    {{--Expiration Date--}}
                    <div class="column medium-3">
                        <div class="text-medium text-light">Expiration Date</div>
                        <p class="text-large">{{ $paymentMethod->getDate() }}</p>
                    </div>

                    {{--Billing ZIP Code--}}
                    <div class="column medium-3">
                        <div class="text-medium text-light">Billing ZIP Code</div>
                        <p class="text-large">{{ $paymentMethod->getZip() }}</p>
                    </div>
                </div>
                @else
                    <div class="column xlarge-12">
                        You do not currently have a credit card on file. Please contact {{ $view_globals['customer_service_number'] }} to add one.
                    </div>
                @endif

            </div>
        </div>
    </div>
@endsection

