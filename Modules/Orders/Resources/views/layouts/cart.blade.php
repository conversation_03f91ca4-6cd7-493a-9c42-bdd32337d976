@extends('layouts.main')

@section('title', 'Checkout')

@section('content')
    <div class="row expanded">
        <div class="column">
            <div class="alignleft">
                @yield('header')
            </div>
        </div>
    </div>

    <div class="row expanded">
        <div class="column large-6 xlarge-4 large-push-6 xlarge-push-8">
            @yield('order-summary')
        </div>

        <div class="column large-6 xlarge-8 large-pull-6 xlarge-pull-4">
            <div class="box inner-large">
                <h2>Account Information</h2>
                <div class="row expanded">

                    {{--Name--}}
                    <div class="column xlarge-3">
                        <div class="text-medium text-light">Name</div>
                        <p class="text-large wrap">
                            {{ $paymentMethod ? $paymentMethod->getName() :$account->name }}
                        </p>
                    </div>

                    {{--Email Address--}}
                    <div class="column xlarge-4">
                        <div class="text-medium text-light">Email Address</div>
                        <p class="text-large wrap">
                            @if ($paymentMethod && $paymentMethod->getEmailAddress())
                                {{ $paymentMethod->getEmailAddress() }}
                            @elseif ($account->emailAddresses()->orderBy('is_primary', 'DESC')->first()) {{-- Not everyone has a primary email --}}
                                {{ $account->emailAddresses()->orderBy('is_primary', 'DESC')->first()->email }}
                            @else
                                N/a
                            @endif
                        </p>
                    </div>

                    {{--Address--}}
                    <div class="column xlarge-5">
                        <div class="text-medium text-light">Address</div>
                        <p class="text-large wrap">
                            @if ($paymentMethod)
                                <span>{{ $paymentMethod->getBillingAddress1() . "," }}</span>
                                <span>{{ $paymentMethod->getBillingAddress2() ? $paymentMethod->getBillingAddress2() . "," : "" }}</span>
                                <span>{{ $paymentMethod->getCity() }}, {{ $paymentMethod->getState() }} {{ $paymentMethod->getZip() }}</span>
                            @else
                                No billing address on file
                            @endif
                        </p>
                    </div>
                </div>

                <hr>

                <h2>Payment Information</h2>

                <div class="row expanded">
                    @if ($paymentMethod)
                        {{--Name on Card--}}
                        <div class="column medium-3">
                            <div class="text-medium text-light">Name on Card</div>
                            <p class="text-large wrap">{{ $paymentMethod->getName() }}</p>
                        </div>

                        {{--Credit Card--}}
                        <div class="column medium-3">
                            <div class="text-medium text-light">Credit Card</div>
                            <p class="text-large">{{ $paymentMethod->getCardTypeFullName() }} ending in {{ $paymentMethod->getLast4Digits() }}</p>
                        </div>

                        {{--Expiration Date--}}
                        <div class="column medium-3">
                            <div class="text-medium text-light">Expiration Date</div>
                            <p class="text-large">{{ $paymentMethod->getDate() }}</p>
                        </div>

                        {{--Billing ZIP Code--}}
                        <div class="column medium-3">
                            <div class="text-medium text-light">Billing ZIP Code</div>
                            <p class="text-large">{{ $paymentMethod->getZip() }}</p>
                        </div>
                    @else
                        <div class="column xlarge-12">
                            @yield('cc-error-message')
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

