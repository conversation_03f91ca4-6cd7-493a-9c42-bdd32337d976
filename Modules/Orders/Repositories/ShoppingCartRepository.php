<?php

namespace Modules\Orders\Repositories;

use Modules\Orders\Contracts\OrderRepository;
use Modules\Orders\Contracts\ShoppingCartRepository as ShoppingCartRepositoryContract;
use Modules\Orders\Domain\Order\ShoppingCartAggregateRoot;
use Modules\Orders\Models\ShoppingCart;

class ShoppingCartRepository implements ShoppingCartRepositoryContract
{
    public function addBundle(int $bundleId, array $plans)
    {
        /** @var ShoppingCart $shoppingCart */
        $shoppingCart = ShoppingCart::first();

        if (! $shoppingCart) {
            $shoppingCart = ShoppingCart::create();
        }

        $shoppingCart->bundle_id = $bundleId;

        $shoppingCart->save();

        $shoppingCart->plans()->sync($plans);
    }

    public function addPlan(int $planId)
    {
        /** @var ShoppingCart $shoppingCart */
        $shoppingCart = ShoppingCart::first();

        if (! $shoppingCart) {
            $shoppingCart = ShoppingCart::create();
        }

        $shoppingCart->plans()->syncWithoutDetaching([$planId]);
    }

    public function clearShoppingCart()
    {
        $shoppingCart  = ShoppingCart::first();

        if (! $shoppingCart) {
            return;
        }

        $shoppingCart->plans()->detach();
        $shoppingCart->bundle_id = null;
        $shoppingCart->save();
    }

    public function findOrCreateShoppingCart(int $orderId = 0): ?ShoppingCartAggregateRoot
    {
        $orderRepository = resolve(OrderRepository::class);
        $order = $orderRepository->find($orderId);

        /** @var ShoppingCart $shoppingCart */
        $shoppingCart = ShoppingCart::first();

        if (! $shoppingCart) {
            $shoppingCart = new ShoppingCart();
        }

        return new ShoppingCartAggregateRoot($shoppingCart, $order);
    }
}
