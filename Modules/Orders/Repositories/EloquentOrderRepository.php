<?php

namespace Modules\Orders\Repositories;

use Modules\Orders\Contracts\OrderRepository;
use Modules\Orders\Models\Order;
use Modules\Orders\Models\ServiceTermsAgreement;

class EloquentOrderRepository implements OrderRepository
{
    public function create(array $details): Order
    {
        return Order::create($details);
    }

    public function find(int $id): ?Order
    {
        return Order::find($id);
    }

    public function findOrCreate(array $details): Order
    {
        return Order::findOrCreate($details);
    }

    public function save(Order $order): bool
    {
        return $order->save();
    }

    public function addPlans(Order $order, array $planIds)
    {
        $order->plans()->sync($planIds);
    }

    public function saveServiceTermsAgreement(ServiceTermsAgreement $serviceTermsAgreement): bool
    {
        return $serviceTermsAgreement->save();
    }

    /**
     * @param int|null $planId
     * @param mixed $status status or array of statuses
     * @return Order|null
     */
    public function getLatestOrder(?int $planId = null, $status = []): ?Order
    {
        $query = Order::latest();

        if (!is_null($planId)) {
            $query->whereHas('plans', function($subQuery) use ($planId) {
                $subQuery->where('plans.id', $planId);
            });
        }

        if (!empty($status)) {
            if (!is_array($status)) {
                $status = [$status];
            }
            $query->whereIn('status', $status);
        }

        return $query->first();
    }

    // getLatestOrder occasionally was returning second-to-last record for this use case
    public function getLatestOrderByDate(string $date): ?Order
    {
        return Order::where('created_at', '>', $date)->latest()->first();
    }
}
