<?php

namespace Modules\Orders\Http\Controllers;

use App\Contracts\AccountRepository;
use App\Http\Controllers\Controller;
use App\Models\Plan;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\Orders\Contracts\ShoppingCartRepository;
use Modules\Orders\DTO\CreateShoppingCartDTO;
use Modules\Orders\Models\ShoppingCart;
use Modules\Orders\Traits\ShoppingCart as ShoppingCartTrait;
use Modules\Pricing\Services\PricePointService;
use Modules\Xrms\Contracts\PaymentMethodRepository;

class ShoppingCartController extends Controller
{
    use ShoppingCartTrait;

    /** @var \App\Contracts\AccountRepository */
    private $accountRepository;

    public function __construct(AccountRepository $accountRepository)
    {
        $this->accountRepository = $accountRepository;
    }

    public function store(Request $request): JsonResponse
    {
        $this->createShoppingCart(CreateShoppingCartDTO::fromArray($request->toArray()));
        return response()->json(['success' => true], 200);
    }

    public function show(PaymentMethodRepository $paymentMethodRepository, PricePointService $pricingService)
    {
        /** @var ShoppingCart $shoppingCart */
        $shoppingCart = ShoppingCart::first();
        $planIds = $shoppingCart->plans->pluck('id')->toArray();
        $discounts = $pricingService->getBundleDiscounts($planIds, $shoppingCart->bundle_id);
        $paymentMethod = $paymentMethodRepository->getPaymentMethod();
        $canEnroll = !Auth::user()->isEmployee() && $paymentMethod;

        return view('orders::place-order', [
            'plans' => $this->mapPlansWithPrices($shoppingCart, $pricingService),
            'account' => $this->accountRepository->currentAccount(),
            'totalDiscount' => $discounts,
            'paymentMethod' => $paymentMethod,
            'canEnroll' =>  $canEnroll,
        ]);
    }

    public function destroy(ShoppingCartRepository $shoppingCartRepository)
    {

        $shoppingCart = $shoppingCartRepository->findOrCreateShoppingCart();
        $shoppingCart->clearShoppingCart()->persist();

        return [
            'success' => true,
            'redirect_url' => rmc_route(session('last_promo_page', 'dashboard.index')),
        ];
    }

    protected function mapPlansWithPrices(ShoppingCart $shoppingCart, PricePointService $pricePointService)
    {
        return $shoppingCart->plans->map(function (Plan $plan) use ($pricePointService, $shoppingCart) {
            return [
                'id' => $plan->id,
                'name' => $plan->name,
                'price' => $pricePointService->getPricingFor($plan->id, $shoppingCart->bundle_id),
            ];
        })->toArray();
    }
}
