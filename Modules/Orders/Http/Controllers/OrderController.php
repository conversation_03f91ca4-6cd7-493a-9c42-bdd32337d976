<?php

namespace Modules\Orders\Http\Controllers;

use App\Context\AccountId;
use App\Contracts\AccountRepository;
use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Modules\Orders\Contracts\OrderRepository;
use Modules\Orders\Contracts\ShoppingCartRepository;
use Modules\Orders\Models\Order;
use Modules\Xrms\Contracts\ContractRepository;
use Modules\Xrms\Contracts\PaymentMethodRepository;

class OrderController extends Controller
{
    /** @var \App\Contracts\AccountRepository */
    private $accountRepository;

    public function __construct(AccountRepository $accountRepository)
    {
        $this->accountRepository = $accountRepository;
    }

    public function show(OrderRepository $orderRepository, PaymentMethodRepository $paymentMethodRepository): View
    {
        $planId = session('product_to_confirm');
        if (!$order = $orderRepository->getLatestOrder($planId, Order::STATUS_CHARGED)) {
            // we always show the latest order to the user, and we have a middleware in place, so this should not
            // happen, however the repository can technically return null and the session could not be set, so
            // this is just a sanity check
            abort(404);
        }

        return view('orders::confirm-order', [
            'account'       => $this->accountRepository->currentAccount(),
            'paymentMethod' => $paymentMethodRepository->getLastUsedPaymentMethod(),
            'orderId'       => $order->id,
            'plans'         => $this->plansAndPrices(collect($order->pricing)),
            'totalDiscount' => $this->discountPrice(collect($order->pricing)),
            'canEnroll'     => $this->canEnroll()
        ]);
    }

    public function update(Request $request, ShoppingCartRepository $shoppingCartRepository): Response
    {
        $shoppingCartRepository->findOrCreateShoppingCart($request->get('order_id'))
            ->signServiceTermsAgreements($request->ip())
            ->persist();

        return response([
            'success' => true,
            'redirect_url' => rmc_route(session('product_dashboard', 'dashboard.index'))
        ], 200);
    }

    public function store(ContractRepository $contractRepository, ShoppingCartRepository $shoppingCartRepository): Response
    {
        $aggregateRoot = $shoppingCartRepository->findOrCreateShoppingCart();
        $planIds = $aggregateRoot->getShoppingCartPlans()->pluck('id')->toArray();
        $bundleId = $aggregateRoot->getShoppingCartBundleId();

        // try enrolling the account via the CRM, or return with GeneralException if fails
        if ($contractRepository->enroll($planIds, $bundleId)) {
            $aggregateRoot->clearShoppingCart()->persist();
        }

        // determine where to send back the user when the enrollment was successful
        $redirectUrl = rmc_route(session('product_dashboard', 'dashboard.index'));

        return response([ 'success' => true, 'redirect_url' => $redirectUrl ], 201);
    }

    protected function discountPrice(Collection $pricing)
    {
        return $pricing->reduce(function ($carry, $item) {
                return (float) $carry + (float) Arr::get($item, 'discounts.1', 0);
        });
    }

    protected function plansAndPrices(Collection $pricing): array
    {
        return array_values($pricing->map(function(array $price) {
            /**
             * Apparently, the CRM send both product-id and productID
             */
            if (!$productId = Arr::get($price, 'productId')) {
                $productId = Arr::get($price, 'product-id');
            }

            /** @var Plan $plan */
            $plan = Plan::findOrFail($productId);
            return [
                'id' => $plan->id,
                'name' => $plan->name,
                'price' => (float) Arr::get($price, 'original-price')
            ];
        })->toArray());
    }

    protected function canEnroll() : bool
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();

        if (!$user->isEmployee()) {
            return true;
        }

        $account = $user->getDefaultAccount();

        return $user->isEmployee() && AccountId::current()->id() == optional($account)->id;
    }
}
