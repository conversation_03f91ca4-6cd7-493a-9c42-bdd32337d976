<?php

namespace Modules\Orders\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Orders\Contracts\OrderRepository;

class FetchServiceTermsExistenceController extends Controller
{
    public function __invoke(Request $request, OrderRepository $orderRepository)
    {
        $order = $orderRepository->getLatestOrder();
        $hasSignedServiceTerms = $order && $order->serviceTermsAgreements->isNotEmpty();

        if (! $hasSignedServiceTerms) {
            return response([
                'has_signed_service_terms' => $hasSignedServiceTerms
            ], Response::HTTP_NOT_FOUND);
        }

        return response([
            'has_signed_service_terms' => $hasSignedServiceTerms
        ]);
    }
}
