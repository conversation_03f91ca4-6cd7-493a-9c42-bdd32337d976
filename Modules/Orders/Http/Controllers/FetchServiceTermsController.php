<?php

namespace Modules\Orders\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Plan;
use Illuminate\Http\Request;

class FetchServiceTermsController extends Controller
{
    public function __invoke(Request $request)
    {
        $planId = $request->get('plan_id');
        $terms = Plan::find($planId)->currentServiceTerms()->service_terms_text;

        return response(['service_terms' => $terms]);
    }
}
