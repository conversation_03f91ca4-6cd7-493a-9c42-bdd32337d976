<?php

namespace Modules\Orders\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Orders\Models\ServiceTerms;
use Modules\Orders\ValueObjects\MasterServiceTerms;

class FetchMasterServiceTermsController extends Controller
{
    public function __invoke()
    {
        $terms = ServiceTerms::where('name', '=', MasterServiceTerms::toString())->first();
        return response(['href' => $terms ? $terms->href : '']);
    }
}
