<?php

namespace Modules\Orders\Http\Middleware;

use App\Context\AccountId;
use App\Contracts\AccountRepository;
use App\Models\Plan;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Modules\Orders\Contracts\OrderRepository;
use Modules\Orders\Models\Order;

class HasServiceTermsAgreement
{
    /** @var array */
    private $plans;

    /** @var \Modules\Orders\Contracts\OrderRepository */
    private $orderRepository;

    /** @var AccountRepository */
    private $accountRepository;

    public function __construct(
        OrderRepository $orderRepository,
        AccountRepository $accountRepository
    ) {
        $this->orderRepository = $orderRepository;
        $this->accountRepository = $accountRepository;
        $this->plans = [
            'alm' => Plan::PLAN_ID_AMERICAN_LIFESTYLE_MAGAZINE,
            'shm' => Plan::PLAN_ID_START_HEALTHY_MAGAZINE,
            'pc'  => Plan::PLAN_ID_POSTCARDS,
            'de'  => Plan::PLAN_ID_DIGITAL_EDITIONS,
            'le'  => Plan::PLAN_ID_LOCAL_EVENT,
            'bp'  => Plan::PLAN_ID_SOCIAL_MEDIA_SHARES,
            'lp'  => Plan::PLAN_ID_LANDING_PAGES,
            'ads' => Plan::PLAN_ID_FACEBOOK_ADS_SERVICES,
            'sma' => Plan::PLAN_ID_SOCIAL_MEDIA_AUTOMATION
        ];
    }

    public function handle(Request $request, Closure $next, $alias)
    {
        // save plan id and dashboard route to determine which product the user is supposed to confirm
        // and where to redirect upon service terms agreement
        session(['product_to_confirm' => $this->plans[$alias], 'product_dashboard' => Route::currentRouteName()]);

        /**
         * If an employee is looking at an account other than his/her own, then don't show the order summary. Otherwise,
         * show the order summary because they're looking at their own account.
         */
        if (Auth::user()->isEmployee() && !$this->isEmployeesOwnAccount()) {
            return $next($request);
        }

        // Landing Page does not require tos
        if ($request->is('landing-pages')) {
            return $next($request);
        }

        // when missing agreement, go to order confirmation
        $notCancelledStatus = [Order::STATUS_CHARGED, Order::STATUS_CLOSED];
        $latestOrder = $this->orderRepository->getLatestOrder($this->plans[$alias], $notCancelledStatus);
        if ($latestOrder && $latestOrder->serviceTermsAgreements->isEmpty()) {
            return redirect(rmc_route('orders.show'));
        }

        return $next($request);
    }

    /**
     * Determines if an employee is looking at their own account.
     *
     * @return bool
     */
    private function isEmployeesOwnAccount(): bool
    {
        return AccountId::current()->id() == optional(Auth::user()->getDefaultAccount())->id;
    }
}
