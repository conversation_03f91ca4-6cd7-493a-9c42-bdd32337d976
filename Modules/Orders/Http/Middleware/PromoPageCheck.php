<?php

namespace Modules\Orders\Http\Middleware;

use App\Contracts\AccountRepository;
use App\Models\Account;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\Orders\Models\ShoppingCart;

abstract class PromoPageCheck
{
    /** @var int */
    protected $plan;

    /** @var int */
    protected $productRoute;

    public function handle(Request $request, Closure $next)
    {
        $currentRouteName = Route::currentRouteName();

        // checking if it is landing pages,
        // and redirect to LP dashboard instead of promo page
        if ($currentRouteName == 'landing-pages.promo') {
            $currentRouteName = 'landing-pages.dashboard';
        }

        // store promo and product routes to use them when completing checkout process or clearing out the cart
        session(['last_promo_page' => $currentRouteName, 'product_dashboard' => $this->productRoute]);

        // if account has the plan already, go to product dashboard
        $account = $this->getAccount();
        if ($account->hasPlan($this->plan)) {
            return redirect(rmc_route($this->productRoute));
        }

        // if the plan has been added to the cart, show the shopping cart contents
        $plan = optional($account->shoppingCart, function (ShoppingCart $s) {
            return $s->plans()->find($this->plan);
        });

        if ($plan) {
            return redirect(rmc_route('cart.show'));
        }

        return $next($request);
    }

    private function getAccount(): ?Account
    {
        return app(AccountRepository::class)->currentAccount();
    }
}
