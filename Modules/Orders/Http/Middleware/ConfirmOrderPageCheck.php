<?php

namespace Modules\Orders\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Modules\Orders\Contracts\OrderRepository;
use Modules\Orders\Models\Order;

/**
 * Class UnconfirmedOrderCheck
 * @package Modules\Orders\Http\Middleware
 *
 * Ideally all of the orders started from within RMC should have been confirmed during the enrollment process, but those
 * started from the CRM will need to be manually confirmed by the user before making use of our digital products.
 *
 * This middleware checks that the account's latest order has been confirmed by accepting the Terms of Service.
 */
class ConfirmOrderPageCheck
{
    /** @var \Modules\Orders\Contracts\OrderRepository  */
    protected $repository;

    public function __construct(OrderRepository $orderRepository)
    {
        $this->repository = $orderRepository;
    }

    public function handle(Request $request, Closure $next)
    {
        $latestOrder = $this->repository->getLatestOrder(null, Order::STATUS_CHARGED);

        // only allow access if the latest charged order doesn't have service terms agreements
        if ($latestOrder && $latestOrder->serviceTermsAgreements->isEmpty()) {
            return $next($request);
        }

        return redirect()->route('dashboard.index');
    }
}
