<?php

namespace Modules\Orders\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

/**
 * Class ShoppingCartCheck
 * @package Modules\Orders\Http\Middleware
 *
 * This middleware prevents access to all the routes within the enrollment process that require the Account to have
 * a ShoppingCart with plans attached.
 */
class HasShoppingCart
{
    /** @var \App\Contracts\AccountRepository */
    private $accountRepository;

    public function __construct(\App\Contracts\AccountRepository $accountRepository)
    {
        $this->accountRepository = $accountRepository;
    }

    public function handle(Request $request, Closure $next)
    {
        $shoppingCart = optional($this->accountRepository->currentAccount())->shoppingCart;

        if (! $shoppingCart || $shoppingCart->plans->isEmpty()) {
            return redirect()->route('dashboard.index');
        }

        return $next($request);
    }
}
