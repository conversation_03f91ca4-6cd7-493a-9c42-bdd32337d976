<?php

namespace Modules\Orders\Domain\Order\Events;


use Illuminate\Support\Arr;
use Infrastructure\Events\AccountAwareEvent;
use ReminderMedia\Messaging\ShouldBroadcast;

class OrderFailed extends AccountAwareEvent implements ShouldBroadcast
{
    /** @var string */
    protected $globalName = 'orders.order-failed';

    /** @var string */
    private $errorMessage;

    /** @var int */
    private $bundleId;

    /** @var array */
    private $plans;

    /** @var bool */
    private $shouldSignTos;

    /** @var string */
    private $ip;

    /** @var array */
    private $pricing;

    /** @var int|null */
    private $salesOrderId;

    public function __construct($data)
    {
        parent::__construct();

        $this->setData($data);
    }

    protected function setData(array $data): void
    {
        $this->bundleId = Arr::get($data, 'bundle_id');
        $this->plans = Arr::get($data, 'plans');
        $this->shouldSignTos = Arr::get($data, 'should_sign_tos');
        $this->ip = Arr::get($data, 'ip');
        $this->pricing = Arr::get($data, 'pricing');
        $this->salesOrderId = Arr::get($data, 'sales_order_id');
        $this->errorMessage = Arr::get($data, 'error_message');
    }

    protected function getData(): array
    {
        return [
            'bundle_id'         => $this->getBundleId(),
            'plans'             => $this->getPlans(),
            'should_sign_tos'   => $this->isShouldSignTos(),
            'ip'                => $this->getIp(),
            'pricing'           => $this->getPricing(),
            'sales_order_id'    => $this->getSalesOrderId(),
            'error_message'     => $this->getErrorMessage(),
        ];
    }

    /**
     * @return string
     */
    public function getErrorMessage(): ?string
    {
        return $this->errorMessage;
    }

    /**
     * @return int
     */
    public function getBundleId(): int
    {
        return $this->bundleId;
    }

    /**
     * @return array
     */
    public function getPlans(): array
    {
        return $this->plans;
    }

    /**
     * @return bool
     */
    public function isShouldSignTos(): bool
    {
        return $this->shouldSignTos;
    }

    /**
     * @return string
     */
    public function getIp(): string
    {
        return $this->ip;
    }

    /**
     * @return array
     */
    public function getPricing(): array
    {
        return $this->pricing;
    }

    /**
     * @return int
     */
    public function getSalesOrderId(): ?int
    {
        return $this->salesOrderId;
    }
}
