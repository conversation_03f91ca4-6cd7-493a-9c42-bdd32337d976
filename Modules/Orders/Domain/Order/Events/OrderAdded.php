<?php

namespace Modules\Orders\Domain\Order\Events;

use Illuminate\Support\Arr;
use Infrastructure\Events\AccountAwareEvent;
use ReminderMedia\Messaging\ShouldBroadcast;

class OrderAdded extends AccountAwareEvent implements ShouldBroadcast
{
    protected $globalName = "orders.order-added";

    /** @var int */
    private $bundleId;

    /** @var array */
    private $plans;

    /** @var bool */
    private $shouldSignTos;

    /** @var string */
    private $ip;

    /** @var array */
    private $pricing;

    /** @var int|null */
    private $salesOrderId;

    // todo this should not just accept data but instead accept type-hinted properties or a dto(s).
    public function __construct($data)
    {
        parent::__construct();

        $this->setData($data);
    }

    protected function setData(array $data): void
    {
        $this->bundleId = Arr::get($data, 'bundle_id');
        $this->plans = Arr::get($data, 'plans');
        $this->shouldSignTos = Arr::get($data, 'should_sign_tos');
        $this->ip = Arr::get($data, 'ip');
        $this->pricing = Arr::get($data, 'pricing');
        $this->salesOrderId = Arr::get($data, 'sales_order_id');
    }

    protected function getData(): array
    {
        return [
            'bundle_id'       => $this->bundleId,
            'plans'           => $this->plans,
            'should_sign_tos' => $this->shouldSignTos,
            'ip'              => $this->ip,
            'pricing'         => $this->pricing,
            'sales_order_id'  => $this->salesOrderId,
        ];
    }

    public function getBundleId(): int
    {
        return $this->bundleId;
    }

    public function getPlans(): array
    {
        return $this->plans;
    }

    public function shouldSignTos(): bool
    {
        return $this->shouldSignTos;
    }

    public function getIp(): string
    {
        return $this->ip;
    }

    public function getPricing(): array
    {
        return $this->pricing;
    }

    public function getSalesOrderId(): ?int
    {
        return $this->salesOrderId;
    }
}
