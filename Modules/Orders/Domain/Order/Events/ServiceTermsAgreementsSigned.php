<?php

namespace Modules\Orders\Domain\Order\Events;

use Illuminate\Support\Arr;
use Infrastructure\Events\AccountAwareEvent;
use ReminderMedia\Messaging\ShouldBroadcast;

class ServiceTermsAgreementsSigned extends AccountAwareEvent implements ShouldBroadcast
{
    protected $globalName = "orders.sign-service-terms-agreement";

    /** @var int */
    private $orderId;

    /** @var string */
    private $ip;

    public function __construct(int $orderId, string $ip)
    {
        parent::__construct();
        $this->orderId = $orderId;
        $this->ip = $ip;
    }

    public function getOrderId(): int
    {
        return $this->orderId;
    }

    public function getIp(): string
    {
        return $this->ip;
    }

    protected function setData(array $data): void
    {
        $this->orderId = Arr::get($data, 'order_id');
        $this->ip = Arr::get($data, 'ip');
    }

    protected function getData(): array
    {
        return [
            'order_id' => $this->orderId,
            'ip' => $this->ip
        ];
    }
}
