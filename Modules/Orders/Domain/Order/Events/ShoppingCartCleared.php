<?php

namespace Modules\Orders\Domain\Order\Events;

use Infrastructure\Events\AccountAwareEvent;
use ReminderMedia\Messaging\ShouldBroadcast;

class ShoppingCartCleared extends AccountAwareEvent implements ShouldBroadcast
{
    protected $globalName = "shopping-cart.clear";

    protected function setData(array $data): void
    {
        // nothing to do here
    }

    protected function getData(): array
    {
        return [];
    }
}
