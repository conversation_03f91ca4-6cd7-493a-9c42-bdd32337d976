<?php

namespace Modules\Orders\Domain\Order\Events;

use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Infrastructure\Events\AccountAwareEvent;
use ReminderMedia\Messaging\ShouldBroadcast;

class BundleAdded extends AccountAwareEvent implements ShouldBroadcast
{
    protected $globalName = "shopping-cart.bundle-added";

    private $bundleId;

    private $plans;

    public function __construct(int $bundleId, Collection $plans)
    {
        parent::__construct();
        $this->bundleId = $bundleId;
        $this->plans = $plans;
    }

    protected function setData(array $data): void
    {
        $this->bundleId = Arr::get($data, 'bundle_id');
        $this->plans = Arr::get($data, 'plans');
    }

    protected function getData(): array
    {
        return [
            'bundle_id' => $this->bundleId,
            'plans' => $this->plans->toArray()
        ];
    }

    public function toArray(): array
    {
        return $this->getData();
    }

    public function getBundleId(): int
    {
        return $this->bundleId;
    }

    public function getPlans(): Collection
    {
        return $this->plans;
    }

}
