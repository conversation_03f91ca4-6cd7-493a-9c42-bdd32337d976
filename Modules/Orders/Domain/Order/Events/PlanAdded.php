<?php

namespace Modules\Orders\Domain\Order\Events;

use Illuminate\Support\Arr;
use Infrastructure\Events\AccountAwareEvent;
use ReminderMedia\Messaging\ShouldBroadcast;

class PlanAdded extends AccountAwareEvent implements ShouldBroadcast
{
    protected $globalName = "shopping-cart.plan-added";
    private $planId;

    public function __construct(int $planId)
    {
        parent::__construct();
        $this->planId = $planId;
    }

    public function getPlanId(): int
    {
        return $this->planId;
    }

    protected function setData(array $data): void
    {
        $this->planId = Arr::get($data, 'plan_id');
    }

    protected function getData(): array
    {
        return  [
            'plan_id' => $this->planId
        ];
    }

    public function toArray(): array
    {
        return $this->getData();
    }


}
