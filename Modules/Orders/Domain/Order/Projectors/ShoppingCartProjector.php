<?php

namespace Modules\Orders\Domain\Order\Projectors;

use App\EventSourcing\Projectors\Projector;
use App\EventSourcing\Projectors\ProjectsEvents;
use Modules\Orders\Contracts\ShoppingCartRepository;
use Modules\Orders\Domain\Order\Events\BundleAdded;
use Modules\Orders\Domain\Order\Events\ShoppingCartCleared;
use Modules\Orders\Domain\Order\Events\PlanAdded;

class ShoppingCartProjector implements Projector
{
    use ProjectsEvents;

    private $repository;

    public function __construct(ShoppingCartRepository $repository)
    {
        $this->repository = $repository;
    }

    public function onBundleAdded(BundleAdded $event)
    {
        $bundleId = $event->getBundleId();
        $plans = $event->getPlans()->toArray();

        $this->repository->addBundle($bundleId, $plans);
    }

    public function onShoppingCartCleared(ShoppingCartCleared $event)
    {
        $this->repository->clearShoppingCart();
    }

    public function onPlanAdded(PlanAdded $event)
    {
        $planId = $event->getPlanId();

        $this->repository->addPlan($planId);
    }
}
