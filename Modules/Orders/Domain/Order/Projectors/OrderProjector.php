<?php

namespace Modules\Orders\Domain\Order\Projectors;

use App\Contracts\MasterServiceAgreementRepository;
use App\EventSourcing\Projectors\Projector;
use App\EventSourcing\Projectors\ProjectsEvents;
use App\Models\Plan;
use Modules\Orders\Contracts\OrderRepository;
use Modules\Orders\Domain\Order\Events\OrderAdded;
use Modules\Orders\Domain\Order\Events\ServiceTermsAgreementsSigned;
use Modules\Orders\Models\Order;
use Modules\Orders\Models\ServiceTermsAgreement;

class OrderProjector implements Projector
{
    use ProjectsEvents;

    private $repository;

    /** @var MasterServiceAgreementRepository */
    private $masterServiceAgreementRepository;

    public function __construct(
        OrderRepository $repository,
        MasterServiceAgreementRepository $masterServiceAgreementRepository
    ) {
        $this->repository = $repository;
        $this->masterServiceAgreementRepository = $masterServiceAgreementRepository;
    }

    public function onOrderAdded(OrderAdded $event)
    {
        $attributes = [
            'bundle_id' => $event->getBundleId(),
            'sales_order_id' => $event->getSalesOrderId(),
            'pricing' => $event->getPricing()
        ];

        $order = $this->repository->create($attributes);

        $this->repository->addPlans($order, $event->getPlans());

        $order->status = Order::STATUS_CHARGED;

        $this->repository->save($order);
    }

    public function onSignServiceTermsAgreements(ServiceTermsAgreementsSigned $event)
    {
        $ip = $event->getIp();
        $order = $this->repository->find($event->getOrderId());

        $order->plans()->each(function (Plan $plan) use ($order, $ip) {
            $agreement = new ServiceTermsAgreement(['ip' => $ip]);
            $agreement->order()->associate($order);
            $agreement->serviceTerms()->associate($plan->currentServiceTerms());
            $this->repository->saveServiceTermsAgreement($agreement);
        });

        $this->masterServiceAgreementRepository->signMasterTermsService($ip);

        $order->status = Order::STATUS_CLOSED;
        $this->repository->save($order);
    }

}
