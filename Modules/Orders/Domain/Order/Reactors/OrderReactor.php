<?php

namespace Modules\Orders\Domain\Order\Reactors;

use App\EventSourcing\EventHandlers\EventHandler;
use App\EventSourcing\EventHandlers\HandlesEvents;
use Modules\Orders\Contracts\OrderRepository;
use Modules\Orders\Contracts\ShoppingCartRepository;
use Modules\Orders\Domain\Order\Events\OrderAdded;

class OrderReactor implements EventHandler
{
    use HandlesEvents;

    private $orderRepository;
    private $shoppingCartRepository;

    public function __construct(OrderRepository $orderRepository, ShoppingCartRepository $shoppingCartRepository)
    {
        $this->orderRepository = $orderRepository;
        $this->shoppingCartRepository = $shoppingCartRepository;
    }

    public function onOrderAdded(OrderAdded $event): void
    {
        if (! $event->shouldSignTos()) {
            return;
        }

        $order = $this->orderRepository->getLatestOrder();
        $this->shoppingCartRepository->findOrCreateShoppingCart($order->id)
            ->signServiceTermsAgreements($event->getIp())
            ->persist();
    }

}
