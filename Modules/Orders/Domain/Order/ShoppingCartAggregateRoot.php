<?php

namespace Modules\Orders\Domain\Order;

use App\Context\AccountId;
use App\EventSourcing\AggregateRoot;
use App\Models\Plan;
use Domain\Account\Models\AccountPlan;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Collection;
use Modules\Orders\Domain\Order\Events\BundleAdded;
use Modules\Orders\Domain\Order\Events\PlanAdded;
use Modules\Orders\Domain\Order\Events\ServiceTermsAgreementsSigned;
use Modules\Orders\Domain\Order\Events\ShoppingCartCleared;
use Modules\Orders\Models\Order;
use Modules\Orders\Models\ServiceTermsAgreement;
use Modules\Orders\Models\ShoppingCart;

class ShoppingCartAggregateRoot extends AggregateRoot implements Arrayable
{
    /** @var \Modules\Orders\Models\ShoppingCart */
    private $shoppingCart;

    /** @var \Modules\Orders\Models\Order|null $order */
    private $order;

    public function __construct(ShoppingCart $shoppingCart, Order $order = null)
    {
        $this->shoppingCart = $shoppingCart;
        $this->order = $order;
    }

    public function getShoppingCart(): ShoppingCart
    {
        return $this->shoppingCart;
    }

    public function getShoppingCartPlans(): Collection
    {
        return $this->shoppingCart->plans;
    }

    public function getShoppingCartBundleId(): int
    {
        return $this->shoppingCart->bundle_id ?? Plan::BUNDLE_ID_NONE;
    }

    public function addBundle(): self
    {
        $bundleId = AccountPlan::hasMagazine()
            ? Plan::BUNDLE_ID_DIGITAL_MARKETING_WITH_MAGAZINE
            : Plan::BUNDLE_ID_DIGITAL_MARKETING_WITHOUT_MAGAZINE;

        $plans = collect([
            Plan::PLAN_ID_DIGITAL_EDITIONS,
            Plan::PLAN_ID_LOCAL_EVENT,
            Plan::PLAN_ID_BRANDED_POSTS,
            Plan::PLAN_ID_SOCIAL_MEDIA_AUTOMATION
        ]);

        return $this->recordThat(new BundleAdded($bundleId, $plans));
    }

    public function addPlan(int $planId): self
    {
        if ($this->canPlanBeAdded($planId)) {
            return $this->recordThat(new PlanAdded($planId));
        }

        return $this;
    }

    public function clearShoppingCart(): self
    {
        return $this->recordThat(new ShoppingCartCleared());
    }

    public function signServiceTermsAgreements(string $ip): self
    {
        if ($this->order->status == Order::STATUS_CHARGED) {
            return $this->recordThat(new ServiceTermsAgreementsSigned($this->order->id, $ip));
        }

        return $this;
    }

    public function toArray()
    {
        return [
            'account_id'              => AccountId::current()->id(), // todo - remove the need to serialize this
            'order'                   => $this->order->attributesToArray(),
            'plans'                   => $this->order->plans->pluck('id')->toArray(),
            'service_terms_agreement' => $this->order->serviceTermsAgreements->pluck('id')->toArray()
        ];
    }

    public function applyBundleAdded(BundleAdded $event): void
    {
        $this->shoppingCart->bundle_id = $event->getBundleId();
        $event->getPlans()->each(function (int $plan) {
            $this->addPlanToList($plan);
        });
    }

    public function applyClearShoppingCart(): void
    {
        $this->shoppingCart->plans = collect();
        $this->shoppingCart->bundle_id = 0;
    }

    public function applyPlanAdded(PlanAdded $event): void
    {
        $this->addPlanToList($event->getPlanId());
    }

    public function applySignServiceTermsAgreements(ServiceTermsAgreementsSigned $event)
    {
        $ip = $event->getIp();

        $this->order->plans()->each(function (Plan $plan) use ($ip) {
            $agreement = new ServiceTermsAgreement(['ip' => $ip]);
            $agreement->order()->associate($this->order);
            $agreement->serviceTerms()->associate($plan->currentServiceTerms());
        });
        $this->order->status = Order::STATUS_CLOSED;
    }

    private function addPlanToList(int $planId)
    {
        $this->shoppingCart->plans->push(Plan::find($planId));
    }

    private function canPlanBeAdded(int $planId): bool
    {
        return !AccountPlan::hasPlan($planId) && $this->shoppingCart->plans->where('id', $planId)->isEmpty();
    }
}
