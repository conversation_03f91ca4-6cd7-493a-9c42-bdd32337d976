<?php

namespace Modules\Orders\Console;

use App\Models\Account;
use App\Models\Plan;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Modules\Orders\Models\Order;
use Modules\Orders\Models\ServiceTermsAgreement;

class ImportServiceTermsAgreements extends Command
{
    /** @var string */
    protected $name = 'import:service-terms-agreements';

    /** @var string */
    protected $description = 'Import the agreements of Terms of Services from the CRM.';

    public function handle()
    {
        // get all the agreements from XRMS
        $agreements = DB::connection('xrms')
            ->table('subscription_contact_contracts')
            ->where('row_status', 'a')
            ->get()
            ->groupBy('contact_id');

        // let the user know how many records we are about to process
        $this->info("Importing {$agreements->count()} Service Terms Agreements");
        $progress = $this->output->createProgressBar($agreements->count());

        // start processing
        $agreements->each(function (Collection $agreement) use ($progress, &$errors) {
            if ( !$account = Account::find($agreement->first()->contact_id) ) {
                return;
            }

            // get all the plans the account may have except for magazines
            $plans = $account->plans->whereNotIn('id', [
                Plan::PLAN_ID_AMERICAN_LIFESTYLE_MAGAZINE,
                Plan::PLAN_ID_START_HEALTHY_MAGAZINE,
                Plan::PLAN_ID_GOOD_TO_BE_HOME_MAGAZINE,
                Plan::PLAN_ID_BUSINESS_IN_ACTION_MAGAZINE,
                Plan::PLAN_ID_POSTCARDS,
            ]);

            if ($plans->isEmpty()) {
                return;
            }

            // create an order and add the plans
            $order = Order::create(['account_id' => $account->id, 'status' => Order::STATUS_CLOSED]);
            $order->plans()->saveMany($plans);

            // create an agreement for all the plans that have one
            $plans->each(function (Plan $plan) use ($order, $agreement, $account) {
                if ($serviceTerms = $plan->serviceTerms->first()) {
                    $order->serviceTermsAgreements()->save(new ServiceTermsAgreement([
                        'service_terms_id' => $serviceTerms->id,
                        'account_id' => $agreement->first()->contact_id,
                        'ip' => $agreement->first()->ip_address ?? '',
                        'user_id' => $account->getOwner(),
                    ]));
                }
            });

            // onto the next
            $progress->advance();
        });

        // let the user know we're done
        $progress->finish();
        $this->info("\nDone!\n");
    }
}
