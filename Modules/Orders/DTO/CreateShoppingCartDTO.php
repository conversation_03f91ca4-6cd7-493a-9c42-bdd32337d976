<?php

namespace Modules\Orders\DTO;

use Illuminate\Support\Arr;
use Spatie\DataTransferObject\DataTransferObject;

class CreateShoppingCartDTO extends DataTransferObject
{
    /** @var bool|null */
    public $isBundle;

    /** @var int|null */
    public $planId;

    /** @var bool|null */
    public $campaignHasDigitalProducts;

    /** @var bool|null */
    public $isOnlineCampaign;

    public static function fromArray(array $data): self
    {
        return new self([
            'isBundle'                   => Arr::get($data, 'is_bundle'),
            'planId'                     => Arr::get($data, 'plan_id'),
            'campaignHasDigitalProducts' => Arr::get($data, 'campaign_has_digital_products'),
            'isOnlineCampaign'           => Arr::get($data, 'is_online_campaign'),
        ]);
    }
}
